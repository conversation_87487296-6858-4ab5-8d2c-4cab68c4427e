version: 2.1

workflows:
  build-and-deploy:
    jobs:
      - build-deploy:
          context:
            - aws-credentials-generic
            - timezone-nyc
          filters:
            branches:
              only:
                - master
                - devel
                - /^release.*/

jobs:
  build-deploy:

    docker:
      - image: 648912154521.dkr.ecr.us-east-1.amazonaws.com/kashable-docker:nodejs

    resource_class: medium

    environment:
      - NVM_DIR: /usr/local/nvm
      - DOCKER_BASE: 648912154521.dkr.ecr.us-east-1.amazonaws.com

    working_directory: /app

    steps:

      - run:
          name: Setup NVM Environment Variables
          command: |
            source $NVM_DIR/nvm.sh && echo "export PATH=$PATH" >> $BASH_ENV

      - run:
          name: Workaround for git permissions
          command: |
            git config --global --add safe.directory /app

      - checkout

      - run:
          name: Take ownership
          command: |
            chown -R root:root .

      - restore_cache:
          keys:
            - npm-{{ checksum "package-lock.json" }}

      - run:
          name: "Build Essentials"
          command: |
            sudo apt update && sudo apt install -y build-essential

      - run:
          name: "NPM Install"
          command: |
            npm install

      - run:
          name: "Install Next"
          command: |
            npm i -g next

      - save_cache:
           key: npm-{{ checksum "package-lock.json" }}
           paths:
              - node_modules

      - run:
          name: Install Docker
          command: |
            install-docker

      - setup_remote_docker:
          docker_layer_caching: false

      - restore_cache:
          keys:
            - next-{{ checksum "package-lock.json" }}-{{ checksum "next.config.ts" }}

      - deploy:
          name: Build
          command: |
            export PROJECT=kashable-portal-support2
            case "${CIRCLE_BRANCH}" in
              devel)
                npm run build:test
                ;;
              release*)
                npm run build:test
                ;;
              master)
                npm run build:prod
                ;;
            esac

      - save_cache:
           key: next-{{ checksum "package-lock.json" }}-{{ checksum "next.config.ts" }}
           paths:
              - .next/cache
            
      - deploy:
          name: Archive Docker Image
          command: |
            export PROJECT=kashable-portal-support2
            case "${CIRCLE_BRANCH}" in
              devel)
                sbin/package-app devel
                ;;
              release*)
                sbin/package-app qa
                sbin/package-app stg
                ;;
              master)
                sbin/package-app master
                ;;
            esac

      - deploy:
          name: Deploy Application
          command: |
            case "${CIRCLE_BRANCH}" in
              devel*)
                sbin/deploy-app dev
                ;;
              release/*)
                sbin/deploy-app stg
                ;;
              master)
                sbin/deploy-app prod
                ;;
            esac 
