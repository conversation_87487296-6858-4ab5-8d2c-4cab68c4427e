import {
    AccessorFn,
    CellContext,
    Column,
    ColumnDef,
    ColumnDefTemplate,
    createColumnHelper,
    DeepKeys,
    HeaderContext,
} from '@tanstack/react-table';
import {ArrowDropDown, ArrowRight} from '@mui/icons-material';
import {AmountCell} from '@/components/table/cells';
import {DisplayColumnDef, IdentifiedColumnDef} from '@tanstack/table-core';
import {
    BaseAccountingTableModel,
    ComparisonColumnsTableModel,
    ExcelColumnFormatModel,
    RevenuePartnershipTableModel,
    RevenueTableModel,
    TableHeaderModel,
} from '@/interfaces';
import IconButton from '@mui/material/IconButton';
import {capitalizeWords} from '@/utils/TextUtils';
import {CURRENCY_EXCEL_FORMAT} from '@/constants';

export const getCurrencyExcelFormat = (key: string): ExcelColumnFormatModel => {
    return {key, numFmt: CURRENCY_EXCEL_FORMAT};
};

export const getObjectValue = (obj: {[key: string]: any}, keyString: string) => {
    const keys = keyString.split('.');
    let value = obj;

    for (const key of keys) {
        if (value?.hasOwnProperty(key)) {
            value = value[key];
        } else {
            return undefined;
        }
    }

    return value;
};

export const expandCell = <T, U>({row}: CellContext<T, U>) =>
    row.getCanExpand() ? (
        <IconButton
            onClick={() => {
                row.toggleExpanded();
            }}
            sx={{padding: '0', minHeight: '20px'}}>
            {row.getIsExpanded() ? <ArrowDropDown /> : <ArrowRight />}
        </IconButton>
    ) : null;

export const totalSum = <T,>(props: HeaderContext<T, number> & {columnId: string}) => {
    return props.table.getFilteredRowModel().rows.reduce((acc, row) => {
        const value = (row.getValue(props.columnId) as number) || 0;

        return isNaN(value) ? acc : acc + value;
    }, 0);
};

export const rowDifference = <T,>(props: HeaderContext<T, number> & {columnId: string}) => {
    const rows = props.table.getSortedRowModel().rows;
    const firstValue = rows[0] ? (rows[0].getValue(props.columnId) as number) : 0;
    const secondValue = rows[1] ? (rows[1].getValue(props.columnId) as number) : 0;

    return secondValue - firstValue;
};

export const revenuePartnershipUnrealizedAdjustments = (row: RevenuePartnershipTableModel) =>
    row.partnership_adjustments_end - row.partnership_adjustments_start;

export const revenuePartnershipRealizedAdjustments = (row: RevenuePartnershipTableModel) =>
    row.partnership_adjustments_start - row.partnership_adjustments_end;

export const revenuePartnershipUnrealizedPEPM = (row: RevenuePartnershipTableModel) =>
    row.pepm_billed - row.pepm_received;

export const revenuePartnershipRealizedPEPM = (row: RevenuePartnershipTableModel) => row.pepm_received;

export const revenuePartnershipUnrealizedFees = (row: RevenuePartnershipTableModel) =>
    row.unrealized_processing_fee + row.unrealized_origination_fee + row.unrealized_other_fee;

export const revenuePartnershipRealizedFees = (row: RevenuePartnershipTableModel) =>
    row.realized_origination_fee + row.realized_processing_fee + row.realized_other_fee;

export const revenuePartnershipUnrealizedTotal = (row: RevenuePartnershipTableModel) =>
    row.unrealized_interest +
    revenuePartnershipUnrealizedAdjustments(row) +
    revenuePartnershipUnrealizedFees(row) +
    row.unrealized_chargeoff_adjustment +
    revenuePartnershipUnrealizedPEPM(row);

export const revenuePartnershipRealizedTotal = (row: RevenuePartnershipTableModel) =>
    row.realized_interest +
    revenuePartnershipRealizedFees(row) +
    revenuePartnershipRealizedPEPM(row) +
    revenuePartnershipRealizedAdjustments(row);

export const revenuePartnershipBankTotal = (row: RevenuePartnershipTableModel) =>
    row.partnership_performance_fee + row.partnership_sponsor_fee + row.partnership_servicing_fee;

export const revenuePartnershipTotal = (row: RevenuePartnershipTableModel) =>
    revenuePartnershipUnrealizedTotal(row) +
    revenuePartnershipRealizedTotal(row) +
    revenuePartnershipBankTotal(row) +
    row.account_credit +
    row.other_income +
    row.reconciles.EFFECTIVE;

export const revenueRealizedTotal = (row: RevenueTableModel) =>
    row.realized_interest +
    row.realized_origination_fee +
    row.realized_processing_fee +
    row.realized_other_fee +
    row.pepm_received;
export const revenueUnrealizedTotal = (row: RevenueTableModel) =>
    row.unrealized_interest +
    row.unrealized_origination_fee +
    row.unrealized_processing_fee +
    row.unrealized_other_fee +
    row.unrealized_pepm;
export const revenueTotal = (row: RevenueTableModel) =>
    revenueUnrealizedTotal(row) +
    revenueRealizedTotal(row) +
    row.account_credit +
    row.other_income +
    row.reconciles.EFFECTIVE;

export const accountingSubtotalKas = (row: BaseAccountingTableModel) =>
    row.values.KAS + row.values.CRB + row.values.BRB + row.values.MDB;

export const accountingSubtotalKs1 = (row: BaseAccountingTableModel) =>
    row.values.CRBA + row.values.BRBA + row.values.MDBA;

export const accountingSubtotalX = (row: BaseAccountingTableModel) =>
    row.values.CRBX + row.values.BRBX + row.values.MDBX;
export const accountingSubtotalY = (row: BaseAccountingTableModel) => row.values.CRBY + row.values.BRBY;
export const accountingTotal = (row: BaseAccountingTableModel) =>
    Object.values(row.values).reduce((acc, value) => acc + value, 0);

export const allTableColumns = (columns: Column<any, unknown>[]): string[] => {
    let allColumns: string[] = [];

    columns.forEach(({getLeafColumns, getIsVisible}) => {
        if (getIsVisible()) {
            getLeafColumns().forEach(({columnDef}: {columnDef: ColumnDef<any, unknown>}) => {
                const id = columnDef.id || '';

                allColumns.push(id);
            });
        }
    });

    return allColumns;
};
export const tableColumns = (columns: Column<any, unknown>[]): TableHeaderModel[] => {
    let allColumns: TableHeaderModel[] = [];

    columns.forEach(({getLeafColumns, getIsVisible}) => {
        if (getIsVisible()) {
            getLeafColumns().forEach(({columnDef}: {columnDef: ColumnDef<any, unknown>}) => {
                if (!columnDef.meta?.notExport) {
                    const id = columnDef.id || '';
                    const title =
                        typeof columnDef.header === 'string' ? columnDef.header : capitalizeWords(id);

                    allColumns.push({id, title});
                }
            });
        }
    });

    return allColumns;
};

/**
 * TABLE STRUCTURE UTILS
 */
export const generateAmountCell = <T, U>(isMulticolor: boolean) => {
    return <T,>(cell: CellContext<T, U>) => {
        return <AmountCell isMulticolor={isMulticolor} data={Number(cell.getValue() || 0)} />;
    };
};

export const generateTotalAmountCell = <T,>(columnId: string) => {
    return <T,>(props: HeaderContext<T, number>) => {
        return <AmountCell data={totalSum({...props, columnId})} />;
    };
};

export const createColumn = <TData, T>(
    accessor: AccessorFn<TData> | DeepKeys<TData>,
    id: string,
    header: ColumnDefTemplate<HeaderContext<TData, T>> | undefined,
    cell?: ColumnDefTemplate<CellContext<TData, T>> | undefined,
    footer?: ColumnDefTemplate<HeaderContext<TData, T>> | undefined,
    enableSorting = true,
): ColumnDef<TData, T> => {
    const columnHelper = createColumnHelper<TData>();
    const column: AccessorFn<TData> | DeepKeys<TData> extends AccessorFn<TData>
        ? DisplayColumnDef<TData, T>
        : IdentifiedColumnDef<TData, T> = {id, header, enableSorting};

    if (cell) {
        column.cell = cell;
    }

    if (footer) {
        column.footer = footer;
    }

    return columnHelper.accessor(accessor, column);
};
export const createAccessorFnColumn = <TData, T>(
    accessorKey: (string & {}) | keyof TData,
    id: string,
    header: ColumnDefTemplate<HeaderContext<TData, T>> | undefined,
    accessorFn: AccessorFn<TData, T>,
    cell: ColumnDefTemplate<CellContext<TData, T>> | undefined,
    footer: ColumnDefTemplate<HeaderContext<TData, T>> | undefined,
): ColumnDef<TData, T> => ({
    id,
    header,
    accessorKey,
    accessorFn,
    cell,
    footer,
});

export const defaultAmountColumn = <T,>(
    accessor: AccessorFn<T> | DeepKeys<T>,
    header: string,
    withFooter: boolean = true,
    enableSorting: boolean = true,
) => {
    return createColumn<T, number>(
        accessor,
        accessor as string,
        header,
        generateAmountCell<T, number>(false),
        withFooter ? generateTotalAmountCell<T>(accessor as string) : undefined,
        enableSorting,
    );
};

export const defaultAccessorFnColumn = <T,>(
    accessorKey: (string & {}) | keyof T,
    header: string,
    accessorFn: AccessorFn<T, number>,
    withFooter: boolean = true,
) => {
    return createAccessorFnColumn<T, number>(
        accessorKey,
        accessorKey as string,
        header,
        accessorFn,
        generateAmountCell<T, number>(false),
        withFooter ? generateTotalAmountCell<T>(accessorKey as string) : undefined,
    );
};

export const defaultInfoColumn = <T,>(
    accessor: AccessorFn<T> | DeepKeys<T>,
    header: string,
    footer?: string,
    cell?: ColumnDefTemplate<CellContext<T, string>> | undefined,
) => {
    return createColumn<T, string>(accessor, accessor as string, header, cell, footer);
};

/**
 * FOR COMPARISON TABLES
 */
const totalsFooterDifferenceCell = <T,>(columnId: string) => {
    return <T,>(props: HeaderContext<T, number>) => {
        return <AmountCell isMulticolor data={rowDifference({...props, columnId})} />;
    };
};

export const totalsAmountColumn = <T,>(accessor: AccessorFn<T> | DeepKeys<T>, header: string) => {
    return createColumn<T, number>(
        accessor,
        accessor as string,
        header,
        generateAmountCell<T, number>(false),
        totalsFooterDifferenceCell<T>(accessor as string),
    );
};
export const totalsAccessorFnColumn = <T,>(
    accessorKey: (string & {}) | keyof T,
    header: string,
    accessorFn: AccessorFn<T, number>,
) => {
    return createAccessorFnColumn<T, number>(
        accessorKey,
        accessorKey as string,
        header,
        accessorFn,
        generateAmountCell<T, number>(false),
        totalsFooterDifferenceCell<T>(accessorKey as string),
    );
};

/**
 * FOR COMPARISON ROWS TABLES
 */
// ...

/**
 * FOR COMPARISON COLUMNS TABLES
 */
export const calculateColumnsDiff = <T,>(revenueFn: (date: T) => number) => {
    return (row: ComparisonColumnsTableModel<T>) => {
        const firstTotal = row?.date_first ? revenueFn(row.date_first) : 0;
        const secondTotal = row?.date_second ? revenueFn(row.date_second) : 0;

        return secondTotal - firstTotal;
    };
};
export const calculateColumnsTotal = <T,>(
    key: 'date_first' | 'date_second',
    revenueFn: (date: T) => number,
) => {
    return (row: ComparisonColumnsTableModel<T>) => (row[key] ? revenueFn(row[key]) : 0);
};

export const defaultColumnsAmountColumn = <T,>(
    accessor: AccessorFn<ComparisonColumnsTableModel<T>> | DeepKeys<ComparisonColumnsTableModel<T>>,
    header: string,
) => defaultAmountColumn<ComparisonColumnsTableModel<T>>(accessor, header, false);

export const totalColumnsAmountColumn = <T,>(
    id: string,
    header: string,
    accessorFn: AccessorFn<ComparisonColumnsTableModel<T>, number>,
) => ({
    id,
    header,
    accessorFn,
    cell: generateAmountCell<ComparisonColumnsTableModel<T>, number>(false),
});
const _calculateDiffByKey = <T,>(key: keyof ComparisonColumnsTableModel<unknown>) => {
    return (row: ComparisonColumnsTableModel<T>) => {
        const firstValue = getObjectValue(row.date_first as object, key) || 0;
        const secondValue = getObjectValue(row.date_second as object, key) || 0;

        if (typeof firstValue === 'number' && typeof secondValue === 'number') {
            return secondValue - firstValue;
        }

        return 0;
    };
};
export const diffColumnsAmountColumn = <T,>(
    key: keyof ComparisonColumnsTableModel<T> | string,
    header: string,
    accessorFn?: AccessorFn<ComparisonColumnsTableModel<T>, number>,
) => ({
    id: `diff_${key}`,
    header,
    accessorFn: accessorFn ? accessorFn : _calculateDiffByKey(key as keyof ComparisonColumnsTableModel<T>),
    cell: generateAmountCell<ComparisonColumnsTableModel<T>, number>(true),
});
