import {ThemeOptions} from '@mui/material/styles/createTheme';
import {PaymentFrequency, SelectModel} from '@/interfaces';

export const SAVED_PATH_KEY = 'kas-saved-path';
export const PROFILES_KEY = 'kas-profiles';
export const ONBOARDING_EMPLOYERS_KEY = 'kas-onboarding-employers';
export const USER_SETTINGS_KEY = 'kas-user-settings';
export const VERIFICATION_HASH = 'verification-dashboard';
export const UNDERWRITING_EMPL_HASH = 'underwriting-dashboard';
export const UNDERWRITING_USER_HASH = 'underwriting-dashboard-user';
export const UNDERWRITING_LOAN_HASH = 'underwriting-dashboard-loan';
export const COLLECTIONS_HASH = 'collections-dashboard';
export const NO_RESULTS_SYMBOL = '—';
export const DEFAULT_ERROR_MSG = 'Something went wrong!(';
export const DEFAULT_SUCCESS_MSG = 'Action sent successfully';
export const DEFAULT_VALIDATION_MSG = 'This field is required';
export const DIGITS_VALIDATION_MSG = 'Must be only digits';
export const DEFAULT_DATE_FORMAT = 'YYYYMMDD';

// FORMATS
export const CURRENCY_EXCEL_FORMAT = '$#,##0.00';

// SELECT OPTIONS
export const PAYMENT_FREQUENCY_OPTIONS: SelectModel<string>[] = [
    {
        id: PaymentFrequency.SINGLE,
        value: PaymentFrequency.SINGLE,
        label: 'Single',
    },
    {
        id: PaymentFrequency.WEEKLY,
        value: PaymentFrequency.WEEKLY,
        label: 'Weekly',
    },
    {
        id: PaymentFrequency.BIWEEKLY,
        value: PaymentFrequency.BIWEEKLY,
        label: 'Bi-Weekly',
    },
    {
        id: PaymentFrequency.SEMIMONTHLY,
        value: PaymentFrequency.SEMIMONTHLY,
        label: 'Semi-Monthly',
    },
    {
        id: PaymentFrequency.MONTHLY,
        value: PaymentFrequency.MONTHLY,
        label: 'Monthly',
    },
];

// THEME OPTIONS
export const DEFAULT_THEME_OPTIONS: ThemeOptions = {
    typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: 14,
        h2: {
            fontSize: 30,
            fontWeight: 600,
        },
        h3: {
            fontSize: 21,
            lineHeight: 1.5,
            fontWeight: 600,
        },
        h6: {
            fontSize: 16,
            lineHeight: 1.5,
            fontWeight: 600,
        },
        subtitle1: {
            fontSize: 14,
            fontWeight: 600,
        },
        body1: {
            fontSize: 14,
        },
        caption: {
            fontSize: 12,
            color: 'var(--color-disabled)',
        },
    },
    components: {
        MuiButton: {
            variants: [
                {
                    props: {variant: 'outlined', color: 'error'},
                    style: {
                        backgroundColor: 'rgba(219, 77, 77, 0.1)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'info'},
                    style: {
                        backgroundColor: 'var(--background-paper)',
                        '&.Mui-disabled': {
                            backgroundColor: 'rgba(0, 0, 0, 0.12)',
                            color: 'rgba(0, 0, 0, 0.38)',
                            '& .MuiSvgIcon-root': {
                                color: 'rgba(0, 0, 0, 0.38)',
                            },
                        },
                    },
                },
                {
                    props: {variant: 'outlined', color: 'warning'},
                    style: {
                        color: 'var(--color-warning)',
                        borderColor: 'var(--color-warning)',
                        backgroundColor: 'rgba(236, 172, 76, 0.1)',
                    },
                },
            ],
            styleOverrides: {
                root: {
                    minWidth: '40px',
                    minHeight: '40px',
                    textTransform: 'none',
                    borderColor: 'var(--color-divider)',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    fontSize: 'var(--small-text-size)',
                    padding: '6px 16px',
                    boxShadow: 'none',
                    '&:hover': {
                        boxShadow: 'none',
                    },
                },
                contained: {
                    color: 'var(--background-paper)',
                },
                outlined: {
                    backgroundColor: 'rgba(29, 158, 213, 0.1)',
                },
            },
        },
        MuiButtonGroup: {
            styleOverrides: {
                firstButton: {
                    '&:hover': {
                        borderRightColor: 'transparent',
                    },
                },
                middleButton: {
                    '&:hover': {
                        borderRightColor: 'transparent',
                    },
                },
                grouped: {
                    padding: '6px 16px',
                },
            },
        },
        MuiAutocomplete: {
            styleOverrides: {
                option: {
                    fontSize: 'var(--small-text-size)',
                },
                noOptions: {
                    fontSize: 'var(--small-text-size)',
                },
                loading: {
                    fontSize: 'var(--small-text-size)',
                },
            },
        },
        MuiFormControlLabel: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                },
                label: {
                    fontSize: 'var(--small-text-size)',
                },
            },
        },
        MuiInputBase: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                    minHeight: '40px',
                },
            },
        },
        MuiInputLabel: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                    lineHeight: '22px',
                },
            },
        },
        MuiMenuItem: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                    '& .MuiListItemText-primary': {
                        fontSize: 'var(--small-text-size)',
                    },
                },
            },
        },
        MuiIconButton: {
            styleOverrides: {
                root: {
                    borderRadius: '4px',
                    minHeight: '40px',
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    boxShadow: 'none',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    borderColor: 'var(--color-divider)',
                },
            },
        },
        MuiSkeleton: {
            styleOverrides: {
                root: {
                    backgroundColor: 'var(--color-divider)',
                },
            },
        },
        MuiAccordionDetails: {
            styleOverrides: {
                root: {
                    padding: 0,
                },
            },
        },
        MuiAccordionSummary: {
            styleOverrides: {
                root: {
                    padding: '0 12px',
                },
            },
        },
        MuiChip: {
            variants: [
                {
                    props: {variant: 'outlined', color: 'warning'},
                    style: {
                        color: 'var(--color-warning)',
                        borderColor: 'var(--color-warning)',
                        backgroundColor: 'rgba(236, 172, 76, 0.1)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'error'},
                    style: {
                        color: 'var(--color-error)',
                        borderColor: 'var(--color-error)',
                        backgroundColor: 'rgba(219, 77, 77, 0.1)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'success'},
                    style: {
                        color: 'var(--color-success)',
                        borderColor: 'var(--color-success)',
                        backgroundColor: 'rgba(114, 179, 49, 0.1)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'secondary'},
                    style: {
                        color: 'inherit',
                        backgroundColor: 'var(--background-paper)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'primary'},
                    style: {
                        color: 'var(--color-primary)',
                        backgroundColor: 'rgba(29, 158, 213, 0.1)',
                    },
                },
                {
                    props: {variant: 'outlined', color: 'info'},
                    style: {
                        color: 'inherit',
                        backgroundColor: 'var(--background-paper)',
                        borderColor: 'var(--color-text)',
                    },
                },
                {
                    props: {variant: 'filled', color: 'primary'},
                    style: {
                        color: 'var(--background-paper)',
                        backgroundColor: 'var(--color-primary)',
                    },
                },
                {
                    props: {variant: 'filled', color: 'secondary'},
                    style: {
                        backgroundColor: 'var(--background-paper)',
                        color: 'var(--color-primary)',
                    },
                },
                {
                    props: {variant: 'filled', color: 'success'},
                    style: {
                        color: 'var(--background-paper)',
                        borderColor: 'var(--color-success)',
                        backgroundColor: 'var(--color-success)',
                    },
                },
                {
                    props: {variant: 'filled', color: 'error'},
                    style: {
                        color: 'var(--background-paper)',
                        borderColor: 'var(--color-error)',
                        backgroundColor: 'var(--color-error)',
                    },
                },
                {
                    props: {variant: 'filled', color: 'default'},
                    style: {
                        color: 'var(--color-text)',
                    },
                },
            ],
            styleOverrides: {
                root: {
                    borderColor: 'var(--color-divider)',
                    backgroundColor: 'var(--background-paper)',
                    borderRadius: 4,
                    fontSize: 'var(--small-text-size)',
                    lineHeight: 1,
                    paddingLeft: '5px',
                    paddingRight: '5px',
                },
                sizeMedium: {
                    height: '40px',
                },
                outlined: {
                    backgroundColor: 'transparent',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                },
                filled: {
                    backgroundColor: 'var(--background-default)',
                },
                deleteIcon: {
                    color: 'inherit',
                    fontSize: '16px',
                },
            },
        },
        MuiAlert: {
            variants: [
                {
                    props: {severity: 'warning', variant: 'standard'},
                    style: {
                        color: 'var(--color-warning)',
                        borderColor: 'var(--color-warning)',
                        backgroundColor: 'rgba(236, 172, 76, 0.1)',
                    },
                },
                {
                    props: {severity: 'error', variant: 'standard'},
                    style: {
                        color: 'var(--color-error)',
                        borderColor: 'var(--color-error)',
                        backgroundColor: 'rgba(219, 77, 77, 0.1)',
                    },
                },
                {
                    props: {severity: 'success', variant: 'standard'},
                    style: {
                        color: 'var(--color-success)',
                        borderColor: 'var(--color-success)',
                        backgroundColor: 'rgba(114, 179, 49, 0.1)',
                    },
                },
                {
                    props: {severity: 'error', variant: 'filled'},
                    style: {
                        color: 'var(--background-paper)',
                        borderColor: 'var(--color-error)',
                        backgroundColor: 'var(--color-error)',
                    },
                },
                {
                    props: {severity: 'success', variant: 'filled'},
                    style: {
                        color: 'var(--background-paper)',
                        borderColor: 'var(--color-success)',
                        backgroundColor: 'var(--color-success)',
                    },
                },
            ],
            styleOverrides: {
                action: {
                    '& .MuiButtonBase-root': {
                        minHeight: 'auto',
                    },
                    marginRight: 0,
                    paddingTop: 0,
                    alignItems: 'center',
                },
            },
        },
        MuiSlider: {
            styleOverrides: {
                thumb: {
                    boxShadow: 'none',
                    '&:focus, &:hover, &.Mui-active': {
                        boxShadow: 'none',
                    },
                    '&:before': {
                        boxShadow: 'none',
                    },
                },
                valueLabel: {
                    fontSize: 12,
                    fontWeight: 'normal',
                    top: 3,
                    backgroundColor: 'unset',
                    color: 'var(--color-text)',
                    '&::before': {
                        display: 'none',
                    },
                },
            },
        },
        MuiDivider: {
            styleOverrides: {
                root: {
                    borderColor: 'var(--color-divider)',
                },
            },
        },
    },
};
