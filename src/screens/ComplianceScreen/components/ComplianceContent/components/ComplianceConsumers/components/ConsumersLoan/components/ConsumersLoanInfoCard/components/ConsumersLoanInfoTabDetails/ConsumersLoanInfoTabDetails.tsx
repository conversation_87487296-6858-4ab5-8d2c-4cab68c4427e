import React from 'react';
import Box from '@mui/material/Box';
import {
    LoanAlternativePaymentTable,
    LoanAlternativeScheduleTable,
    LoanCreditReportingTable,
    LoanDelinquencyTable,
    LoanRepaymentScheduleTable,
    LoanTransactionsTable,
} from '@/views/loan';
import {LoanInfoTabType, useLoanInfoTabs} from '@/views/loan';

export const ConsumersLoanInfoTabDetails = () => {
    const {
        activeTab,
        loan,
        repaymentScheduleState,
        loadRepaymentScheduleData,
        alternativeScheduleState,
        loadAlternativeScheduleData,
        alternativePaymentState,
        loadAlternativePaymentData,
        creditReportingState,
        loadCreditReportingData,
        delinquencyState,
        loadDelinquencyData,
        transactionsState,
        loadTransactionsData,
    } = useLoanInfoTabs();

    return (
        <Box pt={2}>
            {activeTab === LoanInfoTabType.Repayment_Schedule && (
                <LoanRepaymentScheduleTable
                    state={repaymentScheduleState}
                    loadData={loadRepaymentScheduleData}
                />
            )}
            {activeTab === LoanInfoTabType.Alternative_Schedule && (
                <LoanAlternativeScheduleTable
                    state={alternativeScheduleState}
                    loadData={loadAlternativeScheduleData}
                />
            )}
            {activeTab === LoanInfoTabType.Alternative_Payment && (
                <LoanAlternativePaymentTable
                    state={alternativePaymentState}
                    loadData={loadAlternativePaymentData}
                />
            )}
            {activeTab === LoanInfoTabType.Credit_Reporting && (
                <LoanCreditReportingTable
                    state={creditReportingState}
                    loadData={loadCreditReportingData}
                    loanId={loan.gid}
                />
            )}
            {activeTab === LoanInfoTabType.Delinquency && (
                <LoanDelinquencyTable state={delinquencyState} loadData={loadDelinquencyData} />
            )}
            {activeTab === LoanInfoTabType.Transactions && (
                <LoanTransactionsTable state={transactionsState} loadData={loadTransactionsData} />
            )}
        </Box>
    );
};
