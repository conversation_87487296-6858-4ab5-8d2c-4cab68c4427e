export enum OriginationItemType {
    Logs = 'LOGS',
    Review = 'REVIEW',
    FundingAccountBalances = 'FUNDING ACCOUNT BALANCES',
}

export interface FundingSnapshotModel {
    date: string;
    amount: number;
    origination_source: string;
}

export interface FundingBalanceModel {
    balance: number;
    last_update_time: string;
    origination_source: string;
}

export interface FundingChartDataModel {
    date: string;
    crb_amount: number | null;
    brb_amount: number | null;
    crb_balance: number;
    brb_balance: number;
}
