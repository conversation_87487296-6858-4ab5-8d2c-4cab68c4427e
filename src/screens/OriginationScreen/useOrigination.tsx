import React, {createContext, useContext, useEffect, useState} from 'react';
import {useHash<PERSON>and<PERSON>} from '@/hooks/useHashHandler';
import {OriginationItemType} from './interfaces';

interface OriginationContextModel {
    activeMenu: OriginationItemType;
    changeActiveMenu: (value: OriginationItemType) => void;
}

const OriginationContext = createContext<OriginationContextModel | undefined>(undefined);

export const OriginationProvider = ({children}: {children: React.ReactNode}) => {
    const {hashMatch, updateRoute} = useHashHandler();
    const [activeMenu, setActiveMenu] = useState<OriginationItemType>(OriginationItemType.Logs);

    const changeActiveMenu = (value: OriginationItemType) => {
        updateRoute({hash: value, type: null, value: null});
    };

    const handleRouteChange = () => {
        const hash = hashMatch.hash;

        if (hash) {
            const hashMenuItem = hash.toUpperCase() as OriginationItemType;
            const isValidMenuType = Object.values(OriginationItemType).includes(hashMenuItem);

            if (isValidMenuType) {
                setActiveMenu(hashMenuItem);
            }
        }
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    const value: OriginationContextModel = {
        activeMenu,
        changeActiveMenu,
    };

    return <OriginationContext.Provider value={value}>{children}</OriginationContext.Provider>;
};

export function useOrigination() {
    const context = useContext(OriginationContext);
    if (!context) {
        throw new Error('useOrigination must be used within OriginationProvider');
    }
    return context;
}
