import React from 'react';
import dayjs from 'dayjs';
import Box from '@mui/material/Box';
import {Divider, Stack, Typography} from '@mui/material';
import {toCurrencyNoDecimals} from '@/utils/FormatUtils';
import {FundingChartDataModel} from '@/screens/OriginationScreen/interfaces';

interface FundingChartTooltipProps {
    active?: boolean;
    payload?: any[];
    label?: string;
    chartData: FundingChartDataModel[];
}

export const FundingChartTooltip = ({active, payload, label, chartData}: FundingChartTooltipProps) => {
    if (active && payload && payload.length) {
        const dataPoint = chartData.find((item) => item.date === label);
        const fullDate = dataPoint ? dayjs(dataPoint.date).format('YYYY-MM-DD') : label;

        return (
            <Stack spacing={1} sx={{minWidth: 120}} className='kas-custom-tooltip'>
                <Typography variant='body1'>{fullDate}</Typography>
                <Divider />

                {dataPoint?.brb_amount && (
                    <Box className='kas-custom-tooltip__item'>
                        <span style={{color: 'var(--color-primary)', fontWeight: 500}}>Blue Ridge:</span>
                        {toCurrencyNoDecimals(dataPoint.brb_amount)}
                    </Box>
                )}

                {dataPoint?.crb_amount && (
                    <Box className='kas-custom-tooltip__item'>
                        <span style={{color: 'var(--color-secondary)', fontWeight: 500}}>Cross River:</span>
                        {toCurrencyNoDecimals(dataPoint.crb_amount)}
                    </Box>
                )}

                <Box className='kas-custom-tooltip__item'>
                    <span style={{color: 'var(--color-success)', fontWeight: 500}}>Cross River Balance:</span>
                    {toCurrencyNoDecimals(dataPoint?.crb_balance || 0)}
                </Box>

                <Box className='kas-custom-tooltip__item'>
                    <span style={{color: 'var(--color-warning)', fontWeight: 500}}>Blue Ridge Balance:</span>
                    {toCurrencyNoDecimals(dataPoint?.brb_balance || 0)}
                </Box>
            </Stack>
        );
    }

    return null;
};
