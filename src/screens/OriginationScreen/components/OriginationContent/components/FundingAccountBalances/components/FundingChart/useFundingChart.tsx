import React, {createContext, useCallback, useContext, useMemo, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface} from '@/interfaces';
import {FundingSnapshotModel, FundingBalanceModel, FundingChartDataModel} from '@/screens/OriginationScreen/interfaces';

interface FundingChartContextModel {
    snapshotState: DataStateInterface<FundingSnapshotModel[]>;
    balanceState: DataStateInterface<FundingBalanceModel[]>;
    loadFundingData: (startDate: string, endDate: string) => Promise<void>;
    chartData: FundingChartDataModel[];
    hiddenBars: string[];
    updateHiddenBars: (bar: string) => void;
}

const FundingChartContext = createContext<FundingChartContextModel | undefined>(undefined);

export const FundingChartProvider = ({children}: {children: React.ReactNode}) => {
    const [snapshotState, setSnapshotState] = useState(getDefaultState<FundingSnapshotModel[]>);
    const [balanceState, setBalanceState] = useState(getDefaultState<FundingBalanceModel[]>);
    const [hiddenBars, setHiddenBars] = useState<string[]>([]);

    const updateHiddenBars = useCallback(
        (dataKey: string) => {
            const newValue = hiddenBars.includes(dataKey)
                ? hiddenBars.filter((key) => key !== dataKey)
                : [...hiddenBars, dataKey];

            setHiddenBars(newValue);
        },
        [hiddenBars],
    );

    const chartData = useMemo(() => {
        if (!snapshotState.data || !balanceState.data) {
            return [];
        }

        const kasRecords = snapshotState.data.filter(sh => (sh.origination_source || 'KAS') === 'KAS');
        const crbRecords = snapshotState.data.filter(sh => sh.origination_source === 'CRB');
        const brbRecords = snapshotState.data.filter(sh => sh.origination_source === 'BRB');
        
        const brbBalance = balanceState.data.filter(sh => sh.origination_source === 'BRB')[0];
        const crbBalance = balanceState.data.filter(sh => sh.origination_source === 'CRB')[0];

        const fundingByDate: {[key: string]: FundingChartDataModel} = {};
        
        crbRecords.forEach(crb => {
            fundingByDate[crb.date] = { 
                date: crb.date,  
                crb_amount: crb.amount, 
                brb_amount: null, 
                crb_balance: crbBalance?.balance || 0, 
                brb_balance: brbBalance?.balance || 0
            };
        });
        
        brbRecords.forEach(brb => {
            if(brb.date in fundingByDate){               
                fundingByDate[brb.date]['brb_amount'] = brb.amount;
                fundingByDate[brb.date]['brb_balance'] = brbBalance?.balance || 0;
                fundingByDate[brb.date]['crb_balance'] = crbBalance?.balance || 0;
            } else {
                fundingByDate[brb.date] = { 
                    date: brb.date, 
                    crb_amount: null,  
                    brb_amount: brb.amount, 
                    crb_balance: crbBalance?.balance || 0, 
                    brb_balance: brbBalance?.balance || 0 
                };
            }
        });
        
        kasRecords.forEach(em => {
            if(!(em.date in fundingByDate)){
                fundingByDate[em.date] = { 
                    date: em.date,  
                    crb_amount: null, 
                    brb_amount: null,
                    crb_balance: crbBalance?.balance || 0, 
                    brb_balance: brbBalance?.balance || 0
                };                                      
            }
        });

        return Object.values(fundingByDate).sort((a, b) => a.date.localeCompare(b.date));
    }, [snapshotState.data, balanceState.data]);

    const loadSnapshotData = async (startDate: string, endDate: string) => {
        const url = `/api/secured/origination/funding/snapshot/${startDate}/${endDate}`;
        
        setSnapshotState(getLoadingState(snapshotState));
        const response: Completable<FundingSnapshotModel[]> = await apiRequest(url);
        setSnapshotState(getLoadedState(response));
    };

    const loadBalanceData = async () => {
        const url = '/api/secured/origination/funding/balance';
        
        setBalanceState(getLoadingState(balanceState));
        const response: Completable<FundingBalanceModel[]> = await apiRequest(url);
        setBalanceState(getLoadedState(response));
    };

    const loadFundingData = async (startDate: string, endDate: string) => {
        await Promise.all([loadSnapshotData(startDate, endDate), loadBalanceData()]);
    };

    const value: FundingChartContextModel = {
        snapshotState,
        balanceState,
        loadFundingData,
        chartData,
        hiddenBars,
        updateHiddenBars,
    };

    return <FundingChartContext.Provider value={value}>{children}</FundingChartContext.Provider>;
};

export function useFundingChart() {
    const context = useContext(FundingChartContext);
    if (!context) {
        throw new Error('useFundingChart must be used within FundingChartProvider');
    }
    return context;
}
