import React from 'react';
import {FundingChartDataModel} from '@/screens/OriginationScreen/interfaces';
import dayjs from 'dayjs';
import Box from '@mui/material/Box';
import {useTheme} from '@mui/material';
import {
    Bar,
    CartesianGrid,
    ComposedChart,
    Legend,
    Line,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis,
} from 'recharts';
import {FundingChartTooltip} from './components';
import {toCurrencyNoDecimals} from '@/utils/FormatUtils';
import {KasCustomLegend} from '@/components';
import {useFundingChart} from './../../useFundingChart';

interface BarConfig {
    dataKey: string;
    name: string;
    fill: string;
}

interface LineConfig {
    dataKey: string;
    name: string;
    stroke: string;
}

export const FundingChartBar = ({data}: {data: FundingChartDataModel[]}) => {
    const {hiddenBars, updateHiddenBars} = useFundingChart();
    const {palette} = useTheme();
    
    const barConfigs: BarConfig[] = [
        {dataKey: 'brb_amount', name: 'Blue Ridge', fill: palette.primary.main},
        {dataKey: 'crb_amount', name: 'Cross River', fill: palette.secondary.main},
    ];
    
    const lineConfigs: LineConfig[] = [
        {dataKey: 'crb_balance', name: 'Cross River Balance', stroke: palette.success.main},
        {dataKey: 'brb_balance', name: 'Blue Ridge Balance', stroke: palette.warning.main},
    ];

    const legendPayload = [
        ...barConfigs.map(({fill, name, dataKey}) => ({
            value: name,
            type: 'rect' as const,
            id: dataKey,
            color: fill,
            dataKey,
        })),
        ...lineConfigs.map(({stroke, name, dataKey}) => ({
            value: name,
            type: 'line' as const,
            id: dataKey,
            color: stroke,
            dataKey,
        })),
    ];

    return (
        <Box>
            <ResponsiveContainer width='100%' height={400}>
                <ComposedChart data={data} margin={{top: 20, right: 50, left: 20, bottom: 5}} barGap={2}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis
                        dataKey='date'
                        tickFormatter={(date) => dayjs(date).format('MM-DD')}
                        style={{fontSize: 'var(--small-text-size)'}}
                    />
                    <YAxis
                        orientation='left'
                        style={{fontSize: 'var(--small-text-size)'}}
                        tickFormatter={(value) => `$${(value / 1000)}k`}
                        label={{
                            value: 'Amount',
                            angle: -90,
                            position: 'insideLeft',
                        }}
                    />
                    <Tooltip content={(props) => <FundingChartTooltip {...props} chartData={data} />} />
                    <Legend
                        content={() => (
                            <KasCustomLegend
                                payload={legendPayload}
                                hiddenKeys={hiddenBars}
                                onClick={updateHiddenBars}
                            />
                        )}
                    />

                    {barConfigs.map((config) =>
                        !hiddenBars.includes(config.dataKey) ? (
                            <Bar
                                key={config.dataKey}
                                dataKey={config.dataKey}
                                name={config.name}
                                fill={config.fill}
                            />
                        ) : null,
                    )}

                    {lineConfigs.map((config) =>
                        !hiddenBars.includes(config.dataKey) ? (
                            <Line
                                key={config.dataKey}
                                type='monotone'
                                dataKey={config.dataKey}
                                name={config.name}
                                stroke={config.stroke}
                                strokeWidth={2}
                                dot={false}
                            />
                        ) : null,
                    )}
                </ComposedChart>
            </ResponsiveContainer>
        </Box>
    );
};
