import React, {useEffect} from 'react';
import {Button, Grid2, Typography} from '@mui/material';
import {useFormik} from 'formik';
import {useFundingChart} from './../../useFundingChart';
import {KasDatePicker} from '@/components';
import dayjs, {Dayjs} from 'dayjs';

interface FundingHeadFormValues {
    startDate: Dayjs | null;
    endDate: Dayjs | null;
}

export const FundingHead = () => {
    const {snapshotState, balanceState, loadFundingData} = useFundingChart();
    const loading = snapshotState.loading || balanceState.loading;

    const onSubmit = async (values: FundingHeadFormValues) => {
        const startDate = values.startDate?.format('YYYY-MM-DD') || '';
        const endDate = values.endDate?.format('YYYY-MM-DD') || '';
        await loadFundingData(startDate, endDate);
    };

    const formik = useFormik<FundingHeadFormValues>({
        validateOnMount: true,
        initialValues: {
            startDate: dayjs().subtract(14, 'days'),
            endDate: dayjs(),
        },
        onSubmit,
    });

    useEffect(() => {
        if (!snapshotState.data && !balanceState.data) {
            formik.handleSubmit();
        }
    }, []);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Funding Account Balances</Typography>
                </Grid2>
                <Grid2 size={2}>
                    <KasDatePicker
                        label='Start Date'
                        value={formik.values.startDate}
                        onChange={(value) => formik.setFieldValue('startDate', value)}
                        disabled={loading}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <KasDatePicker
                        label='End Date'
                        value={formik.values.endDate}
                        onChange={(value) => formik.setFieldValue('endDate', value)}
                        disabled={loading}
                    />
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        loading={loading}
                        disabled={!formik.isValid || loading}>
                        Load Data
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
