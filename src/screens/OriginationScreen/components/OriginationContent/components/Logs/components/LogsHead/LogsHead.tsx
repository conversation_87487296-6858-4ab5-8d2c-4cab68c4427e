import React, {useEffect} from 'react';
import {Button, Typography, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {LogsHeadValues, validationSchema} from './schema';
import {KasAutocompleteField} from '@/components';
import {LENGTH_OPTIONS} from './data';
import {useLogs} from './../../useLogs';

export const LogsHead = () => {
    const {loadLogs, logsState} = useLogs();

    const onSubmit = async (values: LogsHeadValues) => {
        await loadLogs(values.length);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            length: LENGTH_OPTIONS[0].value,
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (formik.values.length) {
            formik.handleSubmit();
        }
    }, []);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Funding Batch Logs</Typography>
                </Grid2>
                <Grid2 size={3}>
                    <KasAutocompleteField
                        disabled={logsState.loading}
                        name='length'
                        label='Length'
                        options={LENGTH_OPTIONS}
                        formik={formik}
                        showValidation={false}
                    />
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        disabled={!formik.isValid || logsState.loading}>
                        Search
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
