import {CellContext, ColumnDef} from '@tanstack/react-table';
import {defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {OriginationLogModel} from '@/interfaces';

const _defaultInfoColumn = defaultInfoColumn<OriginationLogModel>;

export const LogsColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<OriginationLogModel, string>) => expandCell(props),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    _defaultInfoColumn('gid', 'ID'),
    _defaultInfoColumn('job_type', 'Type'),
    _defaultInfoColumn('job_detail', 'Detail'),
    _defaultInfoColumn('job_start_time', 'Start Time'),
    _defaultInfoColumn('job_end_time', 'End Time'),
    _defaultInfoColumn('job_status', 'Status'),
] as ColumnDef<OriginationLogModel, unknown>[];
