import React from 'react';
import {Row} from '@tanstack/react-table';
import {LogsColumns} from './tables';
import {TableView} from '@/views';
import {OriginationLogModel} from '@/interfaces';
import {LogExpand} from './components';
import {useLogs} from './../../useLogs';

export const LogsResult = () => {
    const {logsState} = useLogs();

    return (
        <TableView<OriginationLogModel>
            withTableActions
            columns={LogsColumns}
            loading={logsState.loading}
            error={logsState.error}
            data={logsState.data}
            renderExpand={(row: Row<OriginationLogModel>) => <LogExpand data={row.original} />}
            tableName='Funding Batch Logs'
        />
    );
};
