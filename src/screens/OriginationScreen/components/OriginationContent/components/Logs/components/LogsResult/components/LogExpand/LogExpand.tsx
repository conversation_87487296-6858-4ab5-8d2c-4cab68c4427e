import React, {Fragment, useEffect, useState} from 'react';
import {OriginationLogModel, OriginationLogRecordModel, OriginationLogSummaryModel} from '@/interfaces';
import {KasLoa<PERSON>, Ka<PERSON>LoadingErro<PERSON>, KasNoR<PERSON>ult<PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Grid2, Typography} from '@mui/material';
import {DownloadCell} from '@/components/table/cells';
import {toCurrency} from '@/utils/FormatUtils';

export const LogExpand = ({data}: {data: OriginationLogModel}) => {
    const isSummary = data.job_type === 'LoanOriginationFundingJob';
    const [state, setState] =
        useState(getDefaultState<OriginationLogRecordModel[] | OriginationLogSummaryModel[]>());

    const loadDetails = async () => {
        const url = `/api/secured/origination/logs/${data.gid}/${isSummary ? 'summary' : 'status'}`;

        setState(getLoadingState(state));
        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!state.data || !state.data.length}>
                <KasNoResults text='No records found' p={1} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <KasLoadingError view='contained' error={state.error} onTryAgain={loadDetails} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={isSummary}>
                <Grid2 container spacing={1} pb={1} pl={6}>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>Status</Typography>
                    </Grid2>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>Count</Typography>
                    </Grid2>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>Amount</Typography>
                    </Grid2>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>Action</Typography>
                    </Grid2>
                    {((state.data as OriginationLogSummaryModel[]) || []).map((item, index) => (
                        <Fragment key={index}>
                            <Grid2 size={3}>{item.status}</Grid2>
                            <Grid2 size={3}>{item.count}</Grid2>
                            <Grid2 size={3}>{toCurrency(item.amount)}</Grid2>
                            <Grid2 size={3}>
                                <DownloadCell
                                    params={JSON.stringify({
                                        path: `/secured/origination/funding/summary/${item.batch_job_id}/download`,
                                        params: {success: item.status == 'FUNDED' ? 1 : 0},
                                    })}
                                />
                            </Grid2>
                        </Fragment>
                    ))}
                </Grid2>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!isSummary}>
                <Grid2 container spacing={1} pb={1} pl={6}>
                    <Grid2 size={2}>
                        <Typography variant='subtitle1'>Record</Typography>
                    </Grid2>
                    <Grid2 size={10}>
                        <Typography variant='subtitle1'>Message</Typography>
                    </Grid2>
                    {((state.data as OriginationLogRecordModel[]) || []).map((item) => (
                        <Fragment key={item.key}>
                            <Grid2 size={2}>{item.key}</Grid2>
                            <Grid2 size={10}>{item.message}</Grid2>
                        </Fragment>
                    ))}
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
