import React, {createContext, useContext, useState} from 'react';
import {DataStateInterface, OriginationLogModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';

interface LogsContextModel {
    logsState: DataStateInterface<OriginationLogModel[]>;
    loadLogs: (length?: string) => Promise<void>;
}

const LogsContext = createContext<LogsContextModel | undefined>(undefined);

export const LogsProvider = ({children}: {children: React.ReactNode}) => {
    const [logsState, setLogsState] =
        useState<DataStateInterface<OriginationLogModel[]>>(getDefaultState<OriginationLogModel[]>());

    const loadLogs = async (length = '') => {
        const url = `/api/secured/origination/logs?length=${length}`;

        setLogsState(getLoadingState(logsState));
        const response = await apiRequest(url);
        setLogsState(getLoadedState(response));
    };

    const value: LogsContextModel = {
        logsState,
        loadLogs,
    };

    return <LogsContext.Provider value={value}>{children}</LogsContext.Provider>;
};

export function useLogs() {
    const context = useContext(LogsContext);
    if (!context) {
        throw new Error('useLogs must be used within LogsProvider');
    }
    return context;
}
