import './styles.scss';

import React from 'react';
import {useOrigination} from './../../useOrigination';
import {OriginationItemType} from './../../interfaces';
import {Logs, LogsProvider, FundingAccountBalances} from './components';

export const OriginationContent = () => {
    const {activeMenu} = useOrigination();

    return (
        <div className='kas-origination-content'>
            <div hidden={activeMenu !== OriginationItemType.Logs}>
                <LogsProvider>
                    <Logs />
                </LogsProvider>
            </div>
            <div hidden={activeMenu !== OriginationItemType.Funding_Account_Balances}>
                <FundingAccountBalances />
            </div>
        </div>
    );
};
