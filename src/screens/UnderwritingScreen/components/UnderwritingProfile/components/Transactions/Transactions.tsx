import React from 'react';
import {
    ProfileItem,
    useUnderwritingTransactions,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {TransactionsDetails} from './components';
import {UnderwritingProfileItemModel} from '@/models';

interface TransactionsProps {
    item: UnderwritingProfileItemModel;
}

export const Transactions = ({item}: TransactionsProps) => {
    const {transactionsState, loadTransactionsData, notificationsState, loadNotificationsData} =
        useUnderwritingTransactions();

    const onRefreshHandler = () => {
        if (transactionsState.data || transactionsState.error) {
            loadTransactionsData().then();
        }
        if (notificationsState.data || notificationsState.error) {
            loadNotificationsData().then();
        }
    };

    return (
        <ProfileItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={false}
            loadingError={''}
            loaded={true}
            DetailsComponent={<TransactionsDetails />}
        />
    );
};
