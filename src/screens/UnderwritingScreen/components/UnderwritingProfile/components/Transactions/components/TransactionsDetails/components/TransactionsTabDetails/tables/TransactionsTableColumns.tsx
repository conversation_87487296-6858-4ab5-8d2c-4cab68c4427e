import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {KasStrike} from '@/components';
import React from 'react';
import {AmountCell} from '@/components/table/cells';
import {TransactionModel} from '@/interfaces';
import {TransactionTypeCell} from './cells';

const columnHelper = createColumnHelper<TransactionModel>();

const _defaultInfoColumn = defaultInfoColumn<TransactionModel>;

const _strikeContent = (props: CellContext<TransactionModel, string>) => (
    <KasStrike key={`${props.column.id}-${props.row.original.gid}`} isStrike={props.row.original.voided}>
        {props.getValue()}
    </KasStrike>
);

const _strikeAmountContent = (props: CellContext<TransactionModel, string>) => (
    <KasStrike key={`${props.column.id}-${props.row.original.gid}`} isStrike={props.row.original.voided}>
        <AmountCell data={Number(props.getValue() || 0)} />
    </KasStrike>
);

export const TransactionsTableColumns = [
    _defaultInfoColumn('effective_date', 'Effective Date', undefined, _strikeContent),
    _defaultInfoColumn('entry_date', 'Entry Date', undefined, _strikeContent),
    columnHelper.accessor('transaction_type', {
            id: 'transaction_type',
            header: 'Type',
            cell: (props: CellContext<TransactionModel, string>) => (
                <TransactionTypeCell data={props.row.original}/>
            ),
        }),
    _defaultInfoColumn('amount', 'Amount', undefined, _strikeAmountContent),
    columnHelper.group({
        id: 'loan',
        header: 'Loan',
        columns: [
            _defaultInfoColumn('loan_id', 'ID', undefined, _strikeContent),
            _defaultInfoColumn('loan_balance', 'Balance', undefined, _strikeAmountContent),
        ],
    }),
    _defaultInfoColumn('account_balance', 'Account Balance', undefined, _strikeAmountContent),
];
