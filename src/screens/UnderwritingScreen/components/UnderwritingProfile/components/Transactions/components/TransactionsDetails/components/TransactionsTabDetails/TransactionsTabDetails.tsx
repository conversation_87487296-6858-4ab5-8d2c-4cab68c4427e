import React, {useEffect, useMemo} from 'react';
import {NotificationsTableColumns, TransactionsTableColumns} from './tables';
import {TransactionsTabType} from '../../interfaces';
import {useUnderwritingTransactions} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {TableView, TransactionsFilterView} from '@/views';
import {ColumnDef} from '@tanstack/react-table';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {NotificationModel, TransactionModel} from '@/interfaces';

export const TransactionsTabDetails = ({activeTab}: {activeTab: TransactionsTabType}) => {
    const {
        transactionsState,
        loadTransactionsData,
        notificationsState,
        loadNotificationsData,
        transactionsFilters,
        updateTransactionsFilters,
    } = useUnderwritingTransactions();

    const columns = useMemo(() => {
        switch (activeTab) {
            case TransactionsTabType.Transactions:
                return TransactionsTableColumns;
            case TransactionsTabType.Notifications:
                return NotificationsTableColumns;
            default:
                return [];
        }
    }, [activeTab]);

    const loadData = () => {
        switch (activeTab) {
            case TransactionsTabType.Transactions:
                if (!transactionsState.loading && !transactionsState.data) {
                    loadTransactionsData().then();
                }
                break;
            case TransactionsTabType.Notifications:
                if (!notificationsState.loading && !notificationsState.data) {
                    loadNotificationsData().then();
                }
                break;
        }
    };

    useEffect(loadData, [activeTab]);

    useEffect(() => {
        if (!transactionsState.loading && (transactionsState.data || transactionsState.error)) {
            loadTransactionsData().then();
        }
    }, [transactionsFilters]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={activeTab === TransactionsTabType.Transactions}>
                <TableView<TransactionModel>
                    withTableActions
                    loading={transactionsState.loading}
                    error={transactionsState.error}
                    data={transactionsState.data}
                    hideBackDropLoading={!transactionsState.loading}
                    columns={columns as ColumnDef<TransactionModel, unknown>[]}
                    onRetry={loadTransactionsData}
                    tableName={activeTab}
                    tableActions={
                        <TransactionsFilterView
                            transactionsFilters={transactionsFilters}
                            updateTransactionsFilters={updateTransactionsFilters}
                            disabled={transactionsState.loading}
                        />
                    }
                />
            </KasSwitchWhen>
            <KasSwitchWhen condition={activeTab === TransactionsTabType.Notifications}>
                <TableView<NotificationModel>
                    withTableActions
                    loading={notificationsState.loading}
                    error={notificationsState.error}
                    data={notificationsState.data}
                    hideBackDropLoading={!notificationsState.loading}
                    columns={columns as ColumnDef<NotificationModel, unknown>[]}
                    onRetry={loadNotificationsData}
                    tableName={activeTab}
                />
            </KasSwitchWhen>
        </KasSwitch>
    );
};
