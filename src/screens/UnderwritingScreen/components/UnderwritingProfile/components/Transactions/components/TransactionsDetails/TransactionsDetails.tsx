import './styles.scss';

import React, {useState} from 'react';
import {Button, ButtonGroup} from '@mui/material';
import {TransactionsTabType} from './interfaces';
import {TransactionsTabDetails} from './components';

export const TransactionsDetails = () => {
    const [activeTab, setActiveTab] = useState(TransactionsTabType.Transactions);

    return (
        <div className='kas-underwriting-transactions-details' data-testid='uw-transactions-details'>
            <div className='kas-underwriting-transactions-details__tabs'>
                <ButtonGroup>
                    {Object.entries(TransactionsTabType).map(([key, value]) => (
                        <Button
                            data-testid={`uw-transactions-tab-${value}`}
                            key={key}
                            variant={activeTab === value ? 'contained' : 'text'}
                            onClick={() => setActiveTab(value)}>
                            {value}
                        </Button>
                    ))}
                </ButtonGroup>
            </div>
            <TransactionsTabDetails activeTab={activeTab} />
        </div>
    );
};
