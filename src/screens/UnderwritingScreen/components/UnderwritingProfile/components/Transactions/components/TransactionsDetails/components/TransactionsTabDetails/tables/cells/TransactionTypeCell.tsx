import React, {ReactNode} from 'react';
import {Chip, Stack, Tooltip} from '@mui/material';
import {KasStrike} from '@/components';
import {TransactionModel} from '@/interfaces';

interface TransactionModelCellProps {
    data: TransactionModel;
}

export const TransactionTypeCell = ({data}: TransactionModelCellProps) => {
    const content: (string | ReactNode)[] = [
        <KasStrike key={`transaction_type-${data.gid}`} isStrike={false}>
            {data.transaction_type}
        </KasStrike>,
    ];

    if (data.category) {
        content.push(
            <Tooltip key={`ach_type-${data.gid}`} title={'ACH Type'}>
                <Chip label={data.category} size='small' variant='outlined' />
            </Tooltip>,
        );
    }

    return (
        <Stack key={data.gid} direction='row' alignItems='center' spacing={1}>
            {content}
        </Stack>
    );
};
