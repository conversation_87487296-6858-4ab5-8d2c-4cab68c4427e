import React, {useState} from 'react';
import {Grid2, Typography} from '@mui/material';
import {LiveVoxMetaDataModal} from '@/screens/UnderwritingScreen/interfaces';
import InfoIcon from '@mui/icons-material/Info';
import {KasModal} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, NotificationModel} from '@/interfaces';

export const LiveVoxDetail = ({data}: {data: NotificationModel}) => {
    const [openModal, setOpenModal] = useState(false);
    const [liveVoxMetadataState, setLiveVoxMetadataState] = useState(getDefaultState<LiveVoxMetaDataModal>());

    const loadLiveVoxMetadata = async () => {
        const url = `/api/secured/underwriting/transactions/${data.gid}/livevox`;
        setLiveVoxMetadataState(getLoadingState(liveVoxMetadataState));

        const response: Completable<LiveVoxMetaDataModal> = await apiRequest(url);
        setLiveVoxMetadataState(getLoadedState(response));
    };

    const handleLiveVoxDetailInfoClick = async () => {
        await loadLiveVoxMetadata();
        setOpenModal(true);
    };

    return (
        <>
            <InfoIcon onClick={handleLiveVoxDetailInfoClick} />
            <KasModal
                title={`Livevox Details for Notification [${data.gid}]`}
                open={openModal}
                onClose={() => setOpenModal(false)}>
                <Grid2 container spacing={2}>
                    <Grid2 size={5}>
                        <Typography>Kashable Phone: {liveVoxMetadataState.data?.kashable_phone}</Typography>
                        <Typography>Direction: {liveVoxMetadataState.data?.direction}</Typography>
                        <Typography>Customer Phone: {liveVoxMetadataState.data?.customer_phone}</Typography>
                        <Typography>Callback Phone: {liveVoxMetadataState.data?.callback_phone}</Typography>
                        <Typography>Callback Time: {liveVoxMetadataState.data?.callback_time}</Typography>
                        <Typography>Contact Agent: {liveVoxMetadataState.data?.agent}</Typography>
                    </Grid2>
                    <Grid2 size={7}>
                        <Typography>Start Time: {liveVoxMetadataState.data?.start_time}</Typography>
                        <Typography>End Time: {liveVoxMetadataState.data?.end_time}</Typography>
                        <Typography>
                            Interaction Type: {liveVoxMetadataState.data?.interaction_type}
                        </Typography>
                        <Typography>
                            Termination Code: {liveVoxMetadataState.data?.termination_code}
                        </Typography>
                        <Typography>Recording Link: {liveVoxMetadataState.data?.recording_link}</Typography>
                    </Grid2>
                </Grid2>
            </KasModal>
        </>
    );
};
