import {defaultInfoColumn} from '@/utils/TableUtils';
import React from 'react';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {KasFlaggedIcon} from '@/components';
import {LiveVoxDetail} from '../../LiveVoxDetail/LiveVoxDetail';
import {NotificationModel} from '@/interfaces';

const columnHelper = createColumnHelper<NotificationModel>();

const _defaultInfoColumn = defaultInfoColumn<NotificationModel>;

export const NotificationsTableColumns = [
    _defaultInfoColumn('sent_time', 'Sent Time'),
    _defaultInfoColumn('notification_type', 'Type'),
    columnHelper.accessor('notification_template', {
        id: 'notification_template',
        header: 'Template',
        cell: (props: CellContext<NotificationModel, string>) =>
            props.getValue().includes('LiveVox') ? (
                <>
                    {props.getValue()}
                    <LiveVoxDetail data={props.row.original} />
                </>
            ) : (
                props.getValue()
            ),
    }),
    _defaultInfoColumn('entity_id', 'Loan', undefined, (info) =>
        info.row.original.entity_class.toLowerCase() === 'loan' ? info.getValue() : '',
    ),
    columnHelper.accessor('collections', {
        id: 'collections',
        header: 'Collections',
        cell: (props) => (props.getValue() ? <KasFlaggedIcon flagged={true} /> : null),
    }),
];
