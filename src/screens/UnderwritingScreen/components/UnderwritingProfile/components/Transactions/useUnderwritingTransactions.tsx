import React, {createContext, useContext, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable, NotificationModel, TransactionModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DataStateInterface} from '@/interfaces';
import {TransactionsFilterItemType, TransactionsFilterModel} from '@/views';

interface UnderwritingTransactionsContextModel {
    transactionsState: DataStateInterface<TransactionModel[]>;
    loadTransactionsData: () => Promise<void>;
    notificationsState: DataStateInterface<NotificationModel[]>;
    loadNotificationsData: () => Promise<void>;
    transactionsFilters: TransactionsFilterModel;
    updateTransactionsFilters: (key: TransactionsFilterItemType) => void;
}

const UnderwritingTransactionsContext = createContext<UnderwritingTransactionsContextModel | undefined>(
    undefined,
);

export const UnderwritingTransactionsProvider = ({children}: {children: React.ReactNode}) => {
    const {gid} = useUnderwritingProfile();
    const baseUrl = useMemo(() => `/api/secured/underwriting/transactions/${gid}`, [gid]);
    useState<TransactionModel | null>(null);
    const [transactionsState, setTransactionsState] = useState(getDefaultState<TransactionModel[]>());
    const [notificationsState, setNotificationsState] = useState(getDefaultState<NotificationModel[]>());
    const [transactionsFilters, setTransactionsFilters] = useState<TransactionsFilterModel>({
        showTransactions: true,
        showNotifications: false,
    });

    const updateTransactionsFilters = (key: TransactionsFilterItemType) => {
        setTransactionsFilters((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const loadTransactionsData = async () => {
        const params = new URLSearchParams({
            showTransactions: String(transactionsFilters.showTransactions),
            showNotifications: String(transactionsFilters.showNotifications),
        }).toString();
        const url = `${baseUrl}/transactions?${params}`;

        setTransactionsState(getLoadingState(transactionsState));

        const response: Completable<TransactionModel[]> = await apiRequest(url);

        setTransactionsState(getLoadedState(response));
    };

    const loadNotificationsData = async () => {
        const url = `${baseUrl}/notifications`;

        setNotificationsState(getLoadingState(notificationsState));

        const response: Completable<NotificationModel[]> = await apiRequest(url);

        setNotificationsState(getLoadedState(response));
    };

    const value: UnderwritingTransactionsContextModel = {
        transactionsState,
        loadTransactionsData,
        notificationsState,
        loadNotificationsData,
        transactionsFilters,
        updateTransactionsFilters,
    };

    return (
        <UnderwritingTransactionsContext.Provider value={value}>
            {children}
        </UnderwritingTransactionsContext.Provider>
    );
};

export function useUnderwritingTransactions() {
    const context = useContext(UnderwritingTransactionsContext);
    if (!context) {
        throw new Error('useUnderwritingTransactions must be used within UnderwritingTransactionsProvider');
    }
    return context;
}
