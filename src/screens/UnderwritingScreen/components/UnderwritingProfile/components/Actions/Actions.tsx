import './styles.scss';

import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ActionsDetails} from './components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileItemModel} from '@/models';

interface ActionsProps {
    item: UnderwritingProfileItemModel;
}

export const Actions = ({item}: ActionsProps) => {
    const {
        loadActionsData,
        actionsState: {loading, error, data},
    } = useUnderwritingProfile();

    useEffect(() => {
        loadActionsData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadActionsData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={<ActionsDetails />}
        />
    );
};
