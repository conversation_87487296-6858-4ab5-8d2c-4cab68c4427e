import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingActionsModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {PinComment} from '../components';
import {KasFlaggedIcon} from '@/components';
import {parseSafeHtml} from '@/utils/ParseSafeHtml';

const columnHelper = createColumnHelper<UnderwritingActionsModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingActionsModel>;

export const ActionsTableColumns = [
    _defaultInfoColumn('gid', 'ID'),
    _defaultInfoColumn('action_type_txt', 'Action'),
    columnHelper.accessor('user_name', {
        id: 'user_name',
        header: 'User',
        cell: (props) => props.getValue() || 'SYSTEM',
    }),
    _defaultInfoColumn('create_time', 'Timestamp'),
    columnHelper.accessor('comment', {
        id: 'comment',
        header: 'Comments',
        cell: (props) => <div>{parseSafeHtml(props.getValue())}</div>,
    }),
    columnHelper.accessor('priority', {
        id: 'priority',
        header: 'Pinned',
        cell: (props) => (props.getValue() ? <KasFlaggedIcon flagged={true} sx={{marginY: '-4px'}} /> : null),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingActionsModel, string>) => (
            <PinComment action={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
