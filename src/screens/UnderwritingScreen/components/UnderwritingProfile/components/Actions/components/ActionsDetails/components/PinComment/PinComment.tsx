import './styles.scss';

import React, {useState} from 'react';
import {PushPin} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {UnderwritingActionsModel} from '@/screens/UnderwritingScreen/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {CircularProgress} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

interface PinCommentProps {
    action: UnderwritingActionsModel;
}

export const PinComment = ({action}: PinCommentProps) => {
    const {showMessage} = useSnackbar();
    const {loadActionsData} = useUnderwritingProfile();
    const [submitting, setSubmitting] = useState(false);

    const onClick = async () => {
        setSubmitting(true);
        const url = '/api/secured/monitor/actions/comment/prioritize';
        const body = JSON.stringify({
            entity_class: 'Actions',
            entity_id: action.gid,
        });
        const response = await apiRequest(url, {method: 'post', body});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);

        if (response.value) {
            await loadActionsData();
        }
    };

    return (
        <div className='kas-underwriting-pin-comment'>
            <ActionCell
                Icon={
                    submitting ? (
                        <CircularProgress size={18} />
                    ) : (
                        <PushPin
                            fontSize='small'
                            color='action'
                            titleAccess={action.priority ? 'Unpin comment' : 'Pin comment'}
                        />
                    )
                }
                onClick={onClick}
            />
        </div>
    );
};
