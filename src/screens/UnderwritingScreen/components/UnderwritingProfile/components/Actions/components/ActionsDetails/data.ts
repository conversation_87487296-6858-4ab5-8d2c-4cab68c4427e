import {SelectModel} from '@/interfaces';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';

export const EMPLOYEE_ACTIONS_LIST: SelectModel<UnderwritingProfileAction>[] = [
    {
        id: UnderwritingProfileAction.ADD_COMMENT,
        value: UnderwritingProfileAction.ADD_COMMENT,
        label: 'Add New Comment',
    },
    {
        id: UnderwritingProfileAction.UNLINK_USER,
        value: UnderwritingProfileAction.UNLINK_USER,
        label: 'Unlink User',
    },
    {
        id: UnderwritingProfileAction.DIRECT_DEPOSIT_REPORT,
        value: UnderwritingProfileAction.DIRECT_DEPOSIT_REPORT,
        label: 'Report Direct Deposit',
    },
    {
        id: UnderwritingProfileAction.REQUEST_LETTER,
        value: UnderwritingProfileAction.REQUEST_LETTER,
        label: 'Request Letter(Dv or Gw)',
    },
    {
        id: UnderwritingProfileAction.SET_TERMINATED,
        value: UnderwritingProfileAction.SET_TERMINATED,
        label: 'Mark Employee Terminated',
    },
    {
        id: UnderwritingProfileAction.SET_DECEASED,
        value: UnderwritingProfileAction.SET_DECEASED,
        label: 'Mark Employee Deceased',
    },
    {
        id: UnderwritingProfileAction.UNFLAG_DECEASED,
        value: UnderwritingProfileAction.UNFLAG_DECEASED,
        label: 'Unmark Employee Deceased',
    },
    {
        id: UnderwritingProfileAction.PAID_LEAVE,
        value: UnderwritingProfileAction.PAID_LEAVE,
        label: 'Mark Employee On Leave(Paid)',
    },
    {
        id: UnderwritingProfileAction.UNPAID_LEAVE,
        value: UnderwritingProfileAction.UNPAID_LEAVE,
        label: 'Mark Employee On Leave(Unpaid)',
    },
    {
        id: UnderwritingProfileAction.UPLOAD_ACH,
        value: UnderwritingProfileAction.UPLOAD_ACH,
        label: 'Upload Signed ACH Agreement',
    },
    {
        id: UnderwritingProfileAction.PAYROLL_DEDUCTION_REVOKE,
        value: UnderwritingProfileAction.PAYROLL_DEDUCTION_REVOKE,
        label: 'Revoke Payroll Deduction',
    },
    {
        id: UnderwritingProfileAction.PAYROLL_DEDUCTION_AUTHORIZE,
        value: UnderwritingProfileAction.PAYROLL_DEDUCTION_AUTHORIZE,
        label: 'Authorize Payroll Deduction',
    },
    {
        id: UnderwritingProfileAction.ADD_LOAN_HARDSHIP,
        value: UnderwritingProfileAction.ADD_LOAN_HARDSHIP,
        label: 'Add Loan Hardship',
    },
    {
        id: UnderwritingProfileAction.REMOVE_LOAN_HARDSHIP,
        value: UnderwritingProfileAction.REMOVE_LOAN_HARDSHIP,
        label: 'Remove Existing Loan Hardship',
    },
    {
        id: UnderwritingProfileAction.UNFLAG_INCOME,
        value: UnderwritingProfileAction.UNFLAG_INCOME,
        label: 'Unflag from Income',
    },
    {
        id: UnderwritingProfileAction.CLOSE_BANKRUPTCY,
        value: UnderwritingProfileAction.CLOSE_BANKRUPTCY,
        label: 'Close Bankruptcy',
    },
    {
        id: UnderwritingProfileAction.ADD_REFERRAL,
        value: UnderwritingProfileAction.ADD_REFERRAL,
        label: 'Add Referral',
    },
    {
        id: UnderwritingProfileAction.FLAGGING_MODAL,
        value: UnderwritingProfileAction.FLAGGING_MODAL,
        label: '[POWER] Flag for Review/Fraud',
    },
    {
        id: UnderwritingProfileAction.FLAGGING_SCRA_MODAL,
        value: UnderwritingProfileAction.FLAGGING_SCRA_MODAL,
        label: '[POWER] Flag for SCRA',
    },
    {
        id: UnderwritingProfileAction.UNFLAG_REVIEW,
        value: UnderwritingProfileAction.UNFLAG_REVIEW,
        label: '[POWER] Unflag from Review',
    },
    {
        id: UnderwritingProfileAction.VOID_LOAN,
        value: UnderwritingProfileAction.VOID_LOAN,
        label: '[POWER] Void Current Loan',
    },
    {
        id: UnderwritingProfileAction.DECLINE_APPLICATION,
        value: UnderwritingProfileAction.DECLINE_APPLICATION,
        label: '[POWER] Decline Application',
    },
    {
        id: UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN,
        value: UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN,
        label: '[POWER] Loan Disbursement (Redisbursement)',
    },
    {
        id: UnderwritingProfileAction.CHARGEOFF_LOAN,
        value: UnderwritingProfileAction.CHARGEOFF_LOAN,
        label: '[POWER] Charge-off Current Loan',
    },
    {
        id: UnderwritingProfileAction.SETTLE_LOAN,
        value: UnderwritingProfileAction.SETTLE_LOAN,
        label: '[POWER] Settle (Partial Charge-off) Current Loan',
    },
    // {
    //     id: UnderwritingProfileAction.CANCEL_ACH_PAYMENT,
    //     value: UnderwritingProfileAction.CANCEL_ACH_PAYMENT,
    //     label: '[POWER] Cancel ACH Payment',
    // },
    {
        id: UnderwritingProfileAction.CLEAR_EMPLOYMENT,
        value: UnderwritingProfileAction.CLEAR_EMPLOYMENT,
        label: '[POWER] Clear User Employment',
    },
    {
        id: UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT,
        value: UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT,
        label: '[POWER] Add Manual Debit Card Payment',
    },
    {
        id: UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING,
        value: UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING,
        label: '[POWER] Unenroll from Credit Monitoring',
    },
    {
        id: UnderwritingProfileAction.RESET_MFA,
        value: UnderwritingProfileAction.RESET_MFA,
        label: '[POWER] Reset MFA',
    },
    {
        id: UnderwritingProfileAction.RUN_FULFILLMENT,
        value: UnderwritingProfileAction.RUN_FULFILLMENT,
        label: '[ADMIN] Re-attempt Fulfillment',
    },
    {
        id: UnderwritingProfileAction.RUN_FULFILLMENT_BF,
        value: UnderwritingProfileAction.RUN_FULFILLMENT_BF,
        label: '[ADMIN] Re-attempt Fulfillment (Benefitfocus)',
    }
];
export const USER_ACTIONS_LIST: SelectModel<UnderwritingProfileAction>[] = [
    {
        id: UnderwritingProfileAction.ADD_COMMENT,
        value: UnderwritingProfileAction.ADD_COMMENT,
        label: 'Add New Comment',
    },
    {
        id: UnderwritingProfileAction.FLAG_REVIEW,
        value: UnderwritingProfileAction.FLAG_REVIEW,
        label: '[POWER] Flag for Review',
    },
    {
        id: UnderwritingProfileAction.UNFLAG_REVIEW,
        value: UnderwritingProfileAction.UNFLAG_REVIEW,
        label: '[POWER] Unflag from Review',
    },
    {
        id: UnderwritingProfileAction.CLEAR_EMPLOYMENT,
        value: UnderwritingProfileAction.CLEAR_EMPLOYMENT,
        label: '[POWER] Clear User Employment',
    },
];

export const POWERUSER_EMPLOYEE_AVAILABLE_ACTIONS: UnderwritingProfileAction[] = [
    UnderwritingProfileAction.FLAGGING_MODAL,
    UnderwritingProfileAction.FLAGGING_SCRA_MODAL,
    UnderwritingProfileAction.UNFLAG_REVIEW,
    UnderwritingProfileAction.VOID_LOAN,
    UnderwritingProfileAction.DECLINE_APPLICATION,
    UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN,
    UnderwritingProfileAction.CHARGEOFF_LOAN,
    UnderwritingProfileAction.SETTLE_LOAN,
    UnderwritingProfileAction.CANCEL_ACH_PAYMENT,
    UnderwritingProfileAction.CLEAR_EMPLOYMENT,
    UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT,
    UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING,
    UnderwritingProfileAction.RESET_MFA,
];

export const ADMIN_EMPLOYEE_AVAILABLE_ACTIONS: UnderwritingProfileAction[] = [
    UnderwritingProfileAction.RUN_FULFILLMENT,
    UnderwritingProfileAction.RUN_FULFILLMENT_BF,
];

export const POWERUSER_USER_AVAILABLE_ACTIONS: UnderwritingProfileAction[] = [
    UnderwritingProfileAction.FLAG_REVIEW,
    UnderwritingProfileAction.UNFLAG_REVIEW,
    UnderwritingProfileAction.CLEAR_EMPLOYMENT,
];
