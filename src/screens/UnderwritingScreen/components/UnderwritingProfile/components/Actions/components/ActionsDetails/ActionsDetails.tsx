import './styles.scss';

import React, {useMemo, useState} from 'react';
import {GlobalModal, KasDefaultTable, KasLoadingBackDrop, useGlobalModal} from '@/components';
import {UnderwritingActionsModel} from '@/screens/UnderwritingScreen/interfaces';
import {ColumnDef} from '@tanstack/react-table';
import {ActionsTableColumns} from './tables';
import {Autocomplete, Button, TextField, Grid2, Stack} from '@mui/material';
import {SelectModel} from '@/interfaces';
import {
    ADMIN_EMPLOYEE_AVAILABLE_ACTIONS,
    EMPLOYEE_ACTIONS_LIST,
    POWERUSER_EMPLOYEE_AVAILABLE_ACTIONS,
    POWERUSER_USER_AVAILABLE_ACTIONS,
    USER_ACTIONS_LIST,
} from './data';
import {useSecured} from '@/hooks/useSecured';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {ProfileAlerts} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

export const ActionsDetails = () => {
    const {hasAnyRole} = useSecured();
    const {typ, profile} = useUnderwritingProfile();
    const {showGlobalModal} = useGlobalModal();
    const {setOpenActionModal} = useUnderwritingProfile();
    const {
        actionsState: {loading, data},
    } = useUnderwritingProfile();
    const [selectedAction, setSelectedAction] = useState<SelectModel<UnderwritingProfileAction> | null>(
        USER_ACTIONS_LIST[0],
    );

    const availableActions = useMemo(() => {
        if (typ === 'USER') {
            return USER_ACTIONS_LIST.filter(({value}) => {
                if (POWERUSER_USER_AVAILABLE_ACTIONS.includes(value)) {
                    return hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER']);
                }
                return true;
            });
        } else {
            return EMPLOYEE_ACTIONS_LIST.filter(({value}) => {
                if (POWERUSER_EMPLOYEE_AVAILABLE_ACTIONS.includes(value)) {
                    return hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER']);
                }
                if (ADMIN_EMPLOYEE_AVAILABLE_ACTIONS.includes(value)) {
                    return hasAnyRole(['KASH_ADMIN']);
                }
                if (value === UnderwritingProfileAction.UNLINK_USER) {
                    return profile.user_id && profile.employee_id;
                }
                return true;
            });
        }
    }, [typ]);

    const onApplyAction = () => {
        switch (selectedAction?.value) {
            case UnderwritingProfileAction.UNLINK_USER:
                showGlobalModal({
                    type: GlobalModal.Unlink_User,
                    props: {userId: profile.user_id, uid: profile.uid},
                });
                break;
            case UnderwritingProfileAction.VERIFICATION_STATUS:
            case UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT:
            case UnderwritingProfileAction.RESEND_REFERRAL_EMAIL:
                break;
            default:
                setOpenActionModal(selectedAction?.value ? {type: selectedAction.value} : null);
        }
    };

    const onSubmitComplaint = () => {
        window.open('https://comply.ncontracts.com/complaint-form', '_blank');
    };

    if (!data) {
        return null;
    }

    return (
        <Stack className='kas-underwriting-actions-details' spacing={2}>
            <ProfileAlerts />
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <Autocomplete
                        size='small'
                        value={selectedAction}
                        options={availableActions}
                        isOptionEqualToValue={(option, value) => option.value === value.value}
                        onChange={(_, newValue) => {
                            setSelectedAction(newValue);
                        }}
                        renderInput={(params) => <TextField {...params} variant='outlined' label='Action' />}
                        data-testid={`uw-actions-select`}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='contained'
                        onClick={onApplyAction}
                        disabled={!selectedAction}
                        data-testid={`uw-actions-apply`}>
                        Apply Action
                    </Button>
                </Grid2>
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='contained'
                        onClick={onSubmitComplaint}
                        color='primary'
                        data-testid={`uw-actions-submit-complaint`}>
                        Submit Complaint
                    </Button>
                </Grid2>
            </Grid2>
            {data && (
                <KasDefaultTable<UnderwritingActionsModel>
                    columns={ActionsTableColumns as ColumnDef<UnderwritingActionsModel, unknown>[]}
                    data={data}
                />
            )}
        </Stack>
    );
};
