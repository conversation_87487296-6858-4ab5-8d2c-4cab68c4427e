import React, {createContext, ReactNode, useContext, useState} from 'react';
import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable, DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface UnderwritingIPHistoryContextModel {
    state: DataStateInterface<UnderwritingIPHistoryModel[]>;
    loadData: () => Promise<void>;
    onAddToBlackList: (body: string) => Promise<Completable<boolean>>;
}

const UnderwritingIPHistoryContext = createContext<UnderwritingIPHistoryContextModel | undefined>(undefined);

interface UnderwritingIPHistoryProviderProps {
    children: ReactNode;
}

export const UnderwritingIPHistoryProvider = ({children}: UnderwritingIPHistoryProviderProps) => {
    const {profile} = useUnderwritingProfile();
    const [state, setState] = useState(getDefaultState<UnderwritingIPHistoryModel[]>());

    const loadData = async () => {
        const url = `/api/secured/underwriting/ip-history/${profile.user_id}`;

        setState(getLoadingState(state));

        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    const onAddToBlackList = async (body: string) => {
        const url = `/api/secured/config/ip/blacklist`;

        return await apiRequest(url, {method: 'POST', body});
    };

    const value: UnderwritingIPHistoryContextModel = {
        state,
        loadData,
        onAddToBlackList,
    };

    return (
        <UnderwritingIPHistoryContext.Provider value={value}>
            {children}
        </UnderwritingIPHistoryContext.Provider>
    );
};

export function useUnderwritingIPHistory() {
    const context = useContext(UnderwritingIPHistoryContext);
    if (!context) {
        throw new Error('useUnderwritingIPHistory must be used within UnderwritingIPHistoryProvider');
    }
    return context;
}
