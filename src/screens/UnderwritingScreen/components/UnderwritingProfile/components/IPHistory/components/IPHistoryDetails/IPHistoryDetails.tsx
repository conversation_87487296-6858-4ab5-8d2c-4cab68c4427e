import React from 'react';
import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {IPHistoryTableColumns} from './tables/IPHistoryTableColumns';
import {ColumnDef, Row} from '@tanstack/react-table';
import {TableView} from '@/views';
import Box from '@mui/material/Box';
import {useUnderwritingIPHistory} from './../../useUnderwritingIPHistory';
import {IPHistoryExpand} from './components';
import {useSecured} from '@/hooks/useSecured';

export const IPHistoryDetails = () => {
    const {state} = useUnderwritingIPHistory();
    const {isUserAdmin} = useSecured();

    const visibleColumns = (IPHistoryTableColumns as ColumnDef<UnderwritingIPHistoryModel, unknown>[]).filter(
        (col) => isUserAdmin || col.id !== 'action',
    );

    return (
        <Box px={5} pb={1.5} data-testid='uw-ip-history-details'>
            <TableView<UnderwritingIPHistoryModel>
                loading={state.loading}
                error={state.error}
                data={state.data}
                sortingColumns={[{id: 'last_seen', desc: true}]}
                columns={visibleColumns}
                renderExpand={(row: Row<UnderwritingIPHistoryModel>) => (
                    <IPHistoryExpand data={row.original} />
                )}
            />
        </Box>
    );
};
