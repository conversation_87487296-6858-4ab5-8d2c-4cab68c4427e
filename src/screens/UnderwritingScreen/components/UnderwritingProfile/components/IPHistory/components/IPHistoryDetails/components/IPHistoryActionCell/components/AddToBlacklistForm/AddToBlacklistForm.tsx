import {useFormik} from 'formik';
import {AddToBlacklistFormValues, validationSchema} from './schema';
import {TextField, Grid2, FormControlLabel, Checkbox} from '@mui/material';
import {KasModalFooter} from '@/components';
import React, {useState} from 'react';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useUnderwritingIPHistory} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

interface AddToBlacklistFormProps {
    ip: string;
    onClose: () => void;
}

export const AddToBlacklistForm = ({ip, onClose}: AddToBlacklistFormProps) => {
    const {showMessage} = useSnackbar();
    const {onAddToBlackList, loadData} = useUnderwritingIPHistory();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: AddToBlacklistFormValues) => {
        setSubmitting(true);

        const body = JSON.stringify({
            description: values.description,
            address_start: values.addressStart,
            address_end: values.addressStart,
            active: String(values.active),
            fedgov: 'false',
            mobile: 'false',
        });
        const response = await onAddToBlackList(body);

        if (response.value) {
            onClose();
            await loadData();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            description: '',
            active: true,
            addressStart: ip,
            addressEnd: ip,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-comment-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={8}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting}
                        onChange={formik.handleChange('description')}
                        onBlur={formik.handleBlur('description')}
                        label='Description'
                        variant='outlined'
                        error={!!formik.errors.description && formik.touched.description}
                        helperText={formik.touched.description && formik.errors.description}
                    />
                </Grid2>
                <Grid2 size={4} alignSelf='center'>
                    <FormControlLabel
                        disabled={submitting}
                        label='Active'
                        control={
                            <Checkbox
                                size='small'
                                name='active'
                                sx={{height: 28}}
                                onChange={formik.handleChange}
                                checked={formik.values.active}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting}
                        value={formik.values.addressStart}
                        onChange={formik.handleChange('addressStart')}
                        onBlur={formik.handleBlur('addressStart')}
                        label='Start Address'
                        variant='outlined'
                        error={!!formik.errors.description && formik.touched.addressStart}
                        helperText={formik.touched.description && formik.errors.addressStart}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting}
                        value={formik.values.addressEnd}
                        onChange={formik.handleChange('addressEnd')}
                        onBlur={formik.handleBlur('addressEnd')}
                        label='End Address'
                        variant='outlined'
                        error={!!formik.errors.description && formik.touched.addressEnd}
                        helperText={formik.touched.description && formik.errors.addressEnd}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter disabled={!formik.isValid} loading={submitting} onCancel={onClose} />
                </Grid2>
            </Grid2>
        </form>
    );
};
