import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasLink, KasStrike} from '@/components';
import {Typography} from '@mui/material';
import {IPAddressCell, IPHistoryActionCell} from './../components';
import {expandCell} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<UnderwritingIPHistoryModel>();

export const IPHistoryTableColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<UnderwritingIPHistoryModel, string>) => expandCell(props),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    columnHelper.accessor('ip_address', {
        id: 'ip_address',
        header: 'IP Address',
        cell: (props) => <IPAddressCell data={props.row.original} />,
    }),
    columnHelper.accessor('last_seen', {
        id: 'last_seen',
        header: 'Timestamp',
        cell: (props) => {
            const {blacklist, whitelist, last_seen} = props.row.original;

            if (blacklist) {
                return (
                    <Typography fontSize='inherit' fontWeight='600' color='error'>
                        {last_seen}
                    </Typography>
                );
            } else if (whitelist) {
                return <KasStrike isStrike={true}>{last_seen}</KasStrike>;
            } else {
                return last_seen;
            }
        },
    }),
    columnHelper.accessor('matches', {
        id: 'matches',
        header: 'Matches',
        cell: (props) => {
            const {matches, whitelist} = props.row.original;

            if (!matches) {
                if (whitelist && props.row.getCanExpand()) {
                    return (
                        <KasLink
                            testid='uw-ip-history-details-matches-show-hide'
                            onClick={() => {
                                props.row.toggleExpanded();
                            }}>
                            {props.row.getIsExpanded() ? 'Hide' : 'Show'}
                        </KasLink>
                    );
                }
                return null;
            }

            return matches.length;
        },
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingIPHistoryModel, string>) => (
            <IPHistoryActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
