import React from 'react';
import {UnderwritingIPHistoryMatchModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasUnderwritingSharedUserLink} from '@/components';
import {Stack, Typography} from '@mui/material';
import dayjs from 'dayjs';

interface IPHistoryMatchesListProps {
    lastSeen: string;
    item: UnderwritingIPHistoryMatchModel;
}

export const IPHistoryMatchesItem = ({lastSeen, item}: IPHistoryMatchesListProps) => {
    const lastSeenDate = dayjs(lastSeen);
    const lastSeenItemDate = dayjs(item.last_seen);
    const review = item.manual_review_date && dayjs(item.manual_review_date).isAfter(dayjs());
    const formattedLastSeen = lastSeenItemDate.format('YYYY-MM-DD HH:mm:ss');
    const timeDiff = lastSeenItemDate.from(lastSeenDate, true);
    const relativeTimeText = `${timeDiff} ${lastSeenItemDate.isAfter(lastSeenDate) ? 'after' : 'prior'}`;

    return (
        <Stack flexWrap='wrap' useFlexGap flexDirection='row' alignItems='center' columnGap={1}>
            {review ? (
                <abbr title={`Manual Review until ${item.manual_review_date}`}>
                    <Typography fontSize='inherit' color='error'>
                        {item.full_name}
                    </Typography>
                </abbr>
            ) : (
                item.full_name
            )}
            <span>
                [<KasUnderwritingSharedUserLink id={item.user_id} />
                ]:
            </span>
            Last seen
            <abbr title={formattedLastSeen}>
                <strong>{relativeTimeText}</strong>
            </abbr>
        </Stack>
    );
};
