import React, {useMemo} from 'react';
import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasStrike} from '@/components';
import {Stack, Typography} from '@mui/material';
import {Phone} from '@mui/icons-material';

export const IPAddressCell = ({data}: {data: UnderwritingIPHistoryModel}) => {
    const renderContent = useMemo(() => {
        if (data.blacklist) {
            return (
                <abbr title={`Blacklisted IP: ${data.description || 'N/A'}`}>
                    <Typography fontSize='inherit' fontWeight='600' color='error'>
                        {data.ip_address}
                    </Typography>
                </abbr>
            );
        }

        if (data.whitelist) {
            return (
                <KasStrike isStrike={true}>
                    <Stack flexDirection='row' alignItems='center' columnGap={1}>
                        {data.mobile && <Phone color='inherit' fontSize='inherit' />}
                        <abbr title={`Whitelisted IP: ${data.description || 'N/A'}`}>{data.ip_address}</abbr>
                    </Stack>
                </KasStrike>
            );
        }

        return data.ip_address;
    }, [data]);

    return <>{renderContent}</>;
};
