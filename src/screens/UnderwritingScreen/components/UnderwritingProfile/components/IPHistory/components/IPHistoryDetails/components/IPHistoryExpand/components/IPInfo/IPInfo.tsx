import React, {useEffect, useState} from 'react';
import {Grid2} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasInfo, KasInfoPreviewLoading, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {UnderwritingIPInfoModel} from '@/screens/UnderwritingScreen/interfaces';

export const IPInfo = ({ip}: {ip: string}) => {
    const [state, setState] = useState(getDefaultState<UnderwritingIPInfoModel>());

    const loadDetails = async () => {
        const url = `/api/secured/ui/profile/whois?ip=${ip}`;

        setState(getLoadingState(state));
        const response = await apiRequest(url);
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <Grid2 container>
            <KasSwitch>
                <KasSwitchWhen condition={state.loading}>
                    <Grid2 size={6}>
                        <KasInfoPreviewLoading />
                    </Grid2>
                    <Grid2 size={6}>
                        <KasInfoPreviewLoading />
                    </Grid2>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!state.error}>
                    <Grid2 size={12}>
                        <KasLoadingError view='contained' error={state.error} onTryAgain={loadDetails} />
                    </Grid2>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!state.data}>
                    <Grid2 size={6}>
                        <KasInfo label='City:' isInline>
                            {state.data?.city || 'N/A'}
                        </KasInfo>
                        <KasInfo label='Region:' isInline>
                            {state.data?.region || 'N/A'}
                        </KasInfo>
                        <KasInfo label='Timezone:' isInline>
                            {state.data?.timezone || 'N/A'}
                        </KasInfo>
                        <KasInfo label='IP:' isInline>
                            {state.data?.ip || 'N/A'}
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={6}>
                        <KasInfo label='Country:' isInline>
                            {state.data?.country || 'N/A'}
                        </KasInfo>
                        <KasInfo label='Zip Code:' isInline>
                            {state.data?.postal || 'N/A'}
                        </KasInfo>
                        <KasInfo label='Location:' isInline>
                            {state.data?.loc || 'N/A'}
                        </KasInfo>
                        <KasInfo label='Host:' isInline>
                            {state.data?.hostname || 'N/A'}
                        </KasInfo>
                    </Grid2>
                </KasSwitchWhen>
            </KasSwitch>
        </Grid2>
    );
};
