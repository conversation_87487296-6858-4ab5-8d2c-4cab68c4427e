import React, {useEffect, useState} from 'react';
import {Typography, useTheme, Grid2} from '@mui/material';
import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {IPHistoryMatches, IPInfo} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';

export const IPHistoryExpand = ({data}: {data: UnderwritingIPHistoryModel}) => {
    const {palette} = useTheme();
    const [state, setState] = useState(getDefaultState<UnderwritingIPHistoryModel[]>());

    const loadData = async () => {
        const url = `/api/secured/underwriting/ip-history/${data.user_id}?ip=${data.ip_address}`;

        setState(getLoadingState(state));

        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    useEffect(() => {
        if (!data.matches) {
            loadData().then();
        }
    }, []);

    return (
        <Grid2 container pl={5} spacing={2} data-testid='uw-ip-history-details-expanded'>
            <Grid2 size={6} pb={1}>
                <Typography variant='subtitle1' color={palette.action.disabled} mb={1}>
                    IP Detail:
                </Typography>
                <IPInfo ip={data.ip_address} />
            </Grid2>
            <Grid2 size={6} pb={1}>
                <Typography variant='subtitle1' color={palette.action.disabled} mb={1}>
                    Matches:
                </Typography>
                <IPHistoryMatches data={data} />
            </Grid2>
        </Grid2>
    );
};
