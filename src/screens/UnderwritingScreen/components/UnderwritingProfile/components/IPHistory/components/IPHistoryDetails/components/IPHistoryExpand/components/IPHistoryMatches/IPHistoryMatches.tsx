import React, {useEffect, useState} from 'react';
import {KasInfoPreviewLoa<PERSON>, KasL<PERSON>ding<PERSON><PERSON><PERSON>, KasNoR<PERSON><PERSON><PERSON>, KasSwitch, KasSwitch<PERSON>hen} from '@/components';
import {
    UnderwritingIPHistoryMatchModel,
    UnderwritingIPHistoryModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {IPHistoryMatchesItem} from './components';

export const IPHistoryMatches = ({data}: {data: UnderwritingIPHistoryModel}) => {
    const [state, setState] = useState(getDefaultState<UnderwritingIPHistoryModel[]>());

    const renderMatchList = (matches: UnderwritingIPHistoryMatchModel[]) => (
        <>
            {matches.map((item, i) => (
                <IPHistoryMatchesItem key={i} lastSeen={data.last_seen} item={item} />
            ))}
        </>
    );

    const loadData = async () => {
        const url = `/api/secured/underwriting/ip-history/${data.user_id}?ip=${data.ip_address}`;

        setState(getLoadingState(state));

        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    useEffect(() => {
        if (!data.matches) {
            loadData().then();
        }
    }, []);

    return (
        <>
            {data.matches ? (
                renderMatchList(data.matches)
            ) : (
                <KasSwitch>
                    <KasSwitchWhen condition={state.loading}>
                        <KasInfoPreviewLoading />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!state.error}>
                        <KasLoadingError view='contained' error={state.error} onTryAgain={loadData} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!state.data && !state.data.length}>
                        <KasNoResults text='No records found' />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!state.data}>
                        {state.data?.length && renderMatchList(state.data[0].matches || [])}
                    </KasSwitchWhen>
                </KasSwitch>
            )}
        </>
    );
};
