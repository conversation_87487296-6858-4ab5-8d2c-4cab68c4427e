import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    description: Yup.string().required(DEFAULT_VALIDATION_MSG),
    active: Yup.boolean().required(DEFAULT_VALIDATION_MSG),
    addressStart: Yup.string().required(DEFAULT_VALIDATION_MSG),
    addressEnd: Yup.string().required(DEFAULT_VALIDATION_MSG),
});

export type AddToBlacklistFormValues = Yup.Asserts<typeof validationSchema>;
