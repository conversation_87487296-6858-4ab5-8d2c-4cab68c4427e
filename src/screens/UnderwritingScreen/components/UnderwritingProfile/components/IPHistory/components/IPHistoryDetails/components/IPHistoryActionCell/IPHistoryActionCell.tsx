import React, {useState} from 'react';
import {UnderwritingIPHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {Place} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {KasModal} from '@/components';
import {AddToBlacklistForm} from './components';

export const IPHistoryActionCell = ({data}: {data: UnderwritingIPHistoryModel}) => {
    const [open, setOpen] = useState(false);

    const onClose = () => setOpen(false);

    return (
        <>
            {!data.blacklist && !data.whitelist && (
                <ActionCell
                    testid='uw-ip-history-blacklist'
                    Icon={<Place color='error' titleAccess='Blacklist IP' />}
                    onClick={() => setOpen(true)}
                />
            )}
            <KasModal title='Add to Blacklist' size='small' open={open} onClose={onClose} data-testid='uw-ip-history-blacklist'>
                <AddToBlacklistForm ip={data.ip_address} onClose={onClose} />
            </KasModal>
        </>
    );
};
