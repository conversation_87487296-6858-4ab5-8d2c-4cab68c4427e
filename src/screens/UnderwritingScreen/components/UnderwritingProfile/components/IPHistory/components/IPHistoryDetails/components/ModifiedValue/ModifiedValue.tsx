import React from 'react';
import {Completable} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {UnderwritingModificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasMaskedSSN} from '@/components';

interface ModifiedValueProps {
    data: UnderwritingModificationHistoryModel;
    value: string;
}

export const ModifiedValue = ({data, value}: ModifiedValueProps) => {
    const {showMessage} = useSnackbar();
    const isSSN = data.field.toLowerCase() === 'ssn' && !!value;

    const loadUnmaskedSSN = async () => {
        const url = `/api/secured/underwriting/employee-profile/${data.entity_id}/ssn`;
        const response: Completable<string> = await apiRequest(url);

        if (!response.value) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        return response;
    };

    return isSSN ? <KasMaskedSSN ssn={value} onLoanSSN={loadUnmaskedSSN} /> : value;
};
