import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {IPHistoryDetails, IPHistoryNoResults} from './components';
import {UnderwritingProfileItemModel} from '@/models';
import {useUnderwritingIPHistory} from './useUnderwritingIPHistory';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const IPHistory = ({item}: {item: UnderwritingProfileItemModel}) => {
    const {profile} = useUnderwritingProfile();
    const {
        state: {data, loading, error},
        loadData,
    } = useUnderwritingIPHistory();

    useEffect(() => {
        if (profile.user_id) {
            loadData().then();
        }
    }, []);

    return (
        <ProfileItem
            onRefresh={profile.user_id ? loadData : undefined}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={profile.user_id ? <IPHistoryDetails /> : <IPHistoryNoResults />}
        />
    );
};
