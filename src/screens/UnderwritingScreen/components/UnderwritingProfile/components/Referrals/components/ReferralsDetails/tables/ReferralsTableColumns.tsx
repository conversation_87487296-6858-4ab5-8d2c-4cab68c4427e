import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingReferralsModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {ReferralsActionCell, ReferralsLinkCell} from './../components';

const columnHelper = createColumnHelper<UnderwritingReferralsModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingReferralsModel>;
const _defaultAmountColumn = defaultAmountColumn<UnderwritingReferralsModel>;

export const ReferralsTableColumns = [
    columnHelper.accessor('referred_id', {
        id: 'referred_id',
        header: 'Link',
        cell: (props) => <ReferralsLinkCell data={props.row.original} />,
    }),
    _defaultAmountColumn('amount', 'Amount', false),
    _defaultInfoColumn('referral_date', 'Referred'),
    _defaultInfoColumn('compensation_date', 'Compensated'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingReferralsModel, string>) => (
            <ReferralsActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
