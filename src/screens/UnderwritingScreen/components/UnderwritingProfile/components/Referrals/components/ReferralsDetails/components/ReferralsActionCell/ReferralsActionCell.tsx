import React from 'react';
import {UnderwritingReferralsModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ActionCell} from '@/components/table/cells';
import {EmailOutlined} from '@mui/icons-material';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useSecured} from '@/hooks/useSecured';

export const ReferralsActionCell = ({data}: {data: UnderwritingReferralsModel}) => {
    const {setOpenActionModal} = useUnderwritingProfile();
    const {hasAnyRole} = useSecured();
    const isPowerUser = hasAnyRole(['KASH_POWERUSER', 'KASH_ADMIN']);

    if (!data.compensation_date && !isPowerUser) {
        return null;
    }

    return (
        <>
            {isPowerUser && !data.compensation_date ? <ActionCell
                data-testid='uw-referrals-action-email-force'
                Icon={<EmailOutlined fontSize='small' color='warning' titleAccess='[Power User] Send Referral Email' />}
                onClick={() =>
                    setOpenActionModal({
                        type: UnderwritingProfileAction.RESEND_REFERRAL_EMAIL,
                        props: {
                            id: data.compensation_id,
                            force: true,
                        },
                    })
                }
            /> : <ActionCell
                data-testid='uw-referrals-action-email'
                Icon={<EmailOutlined fontSize='small' titleAccess='Resend Referral Email' />}
                onClick={() =>
                    setOpenActionModal({
                        type: UnderwritingProfileAction.RESEND_REFERRAL_EMAIL,
                        props: {
                            id: data.compensation_id,
                        },
                    })
                }
            />
            }
        </>
    );
};
