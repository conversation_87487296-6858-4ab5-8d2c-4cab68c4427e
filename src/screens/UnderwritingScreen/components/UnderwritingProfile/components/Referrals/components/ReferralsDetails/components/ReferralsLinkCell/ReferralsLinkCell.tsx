import React from 'react';
import {UnderwritingReferralsModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasUnderwritingSharedLink, KasUnderwritingSharedLoanLink} from '@/components';

export const ReferralsLinkCell = ({data}: {data: UnderwritingReferralsModel}) => {
    return (
        <>
            {data.referred_class}:{' '}
            {data.referred_class === 'Loan' ? (
                <KasUnderwritingSharedLoanLink id={data.referred_id} />
            ) : (
                <KasUnderwritingSharedLink id={data.referred_id} />
            )}
        </>
    );
};
