import React from 'react';
import {UnderwritingReferralsModel} from '@/screens/UnderwritingScreen/interfaces';
import {ReferralsTableColumns} from './tables';
import {ColumnDef} from '@tanstack/react-table';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import Box from '@mui/material/Box';
import {TableView} from '@/views';

export const ReferralsDetails = () => {
    const {referralsState} = useUnderwritingProfile();

    return (
        <Box px={5} pb={1.5} data-testid='uw-referrals-details'>
            <TableView<UnderwritingReferralsModel>
                loading={referralsState.loading}
                error={referralsState.error}
                data={referralsState.data}
                columns={ReferralsTableColumns as ColumnDef<UnderwritingReferralsModel, unknown>[]}
                sortingColumns={[{id: 'referral_date', desc: true}]}
            />
        </Box>
    );
};
