import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ReferralsDetails} from './components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileItemModel} from '@/models';

interface ReferralsProps {
    item: UnderwritingProfileItemModel;
}

export const Referrals = ({item}: ReferralsProps) => {
    const {
        employeeProfileState,
        referralsState: {loading, error, data},
        loadReferralsData,
    } = useUnderwritingProfile();
    const referralCode = employeeProfileState.data?.referral_code?.toUpperCase();

    useEffect(() => {
        loadReferralsData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadReferralsData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            TitleComponent={`Referrals${referralCode ? ` (${referralCode})` : ''}`}
            DetailsComponent={<ReferralsDetails />}
        />
    );
};
