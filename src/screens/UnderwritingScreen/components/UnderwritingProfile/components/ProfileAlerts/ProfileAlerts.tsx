import React, {useEffect} from 'react';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {EmployeeAlert, UserAlert} from './components';
import {KasErrorBoundary} from '@/components';

export const ProfileAlerts = () => {
    const {employeeProfileState, userProfileState, loadEmployeeProfileData} = useUnderwritingProfile();

    useEffect(() => {
        if (!employeeProfileState.loading && !employeeProfileState.data) {
            loadEmployeeProfileData().then();
        }
    }, []);

    if (!(employeeProfileState.data || userProfileState.data)) {
        return null;
    }

    return (
        <KasErrorBoundary>
            {!!employeeProfileState.data && <EmployeeAlert data={employeeProfileState.data} />}
            {!!userProfileState.data && <UserAlert data={userProfileState.data} />}
        </KasErrorBoundary>
    );
};
