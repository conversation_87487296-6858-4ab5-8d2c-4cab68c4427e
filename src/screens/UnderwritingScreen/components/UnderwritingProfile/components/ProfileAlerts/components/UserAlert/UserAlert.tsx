import React, {useMemo} from 'react';
import {Alert, Grid2} from '@mui/material';
import {UnderwritingUserProfileItemModel} from '@/screens/UnderwritingScreen/interfaces';
import dayjs from 'dayjs';
import Box from '@mui/material/Box';

export const UserAlert = ({data}: {data: UnderwritingUserProfileItemModel}) => {
    const contentArr = useMemo(() => {
        const result = [];
        const now = dayjs();
        const reviewDate = dayjs(data.manual_review_date);
        const isManualReviewActive = data.manual_review_date && now.diff(reviewDate) < 0;
        const isManualReviewExpired = data.manual_review_date && now.diff(reviewDate) > 0;

        if (isManualReviewActive) {
            result.push(
                <p>
                    User is flagged for <b>MANUAL REVIEW</b> until {data.manual_review_date}
                </p>,
            );
        }

        if (isManualReviewExpired) {
            result.push(
                <p>
                    <b>MANUAL REVIEW</b> expired {data.manual_review_date}
                </p>,
            );
        }

        if (data.locked && data.password_reset_request_time) {
            result.push(
                <p>
                    User Account locked due to user requested password reset. [
                    {data.password_reset_request_time}]
                </p>,
            );
        }

        if (data.locked && data.login_attempts >= 10) {
            result.push(
                <p>
                    User Account locked due to {data.login_attempts} failed login attempts from{' '}
                    {data.last_login_ip}
                </p>,
            );
        }

        return result;
    }, [data]);

    if (!contentArr.length) {
        return null;
    }

    return (
        <Box mb={1}>
            <Alert severity='warning'>
                <Grid2 container rowSpacing={1} spacing={2}>
                    {contentArr.map((content, i) => (
                        <Grid2 key={i} size={12}>
                            {content}
                        </Grid2>
                    ))}
                </Grid2>
            </Alert>
        </Box>
    );
};
