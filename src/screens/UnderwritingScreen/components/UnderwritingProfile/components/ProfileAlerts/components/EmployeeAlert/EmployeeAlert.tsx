import React, {Fragment, useMemo} from 'react';
import {Alert, Grid2} from '@mui/material';
import {UnderwritingEmployeeProfileItemModel} from '@/screens/UnderwritingScreen/interfaces';
import dayjs from 'dayjs';
import {KasUnderwritingSharedLink} from '@/components';
import Box from '@mui/material/Box';

export const EmployeeAlert = ({data}: {data: UnderwritingEmployeeProfileItemModel}) => {
    const renderLinks = (id: number, index: number) => (
        <Fragment key={id}>
            {index ? ', ' : ''}
            <KasUnderwritingSharedLink id={id} />
        </Fragment>
    );

    const contentArr = useMemo(() => {
        const result = [];
        const now = dayjs();
        const reviewDate = data.manual_review_date ? dayjs(data.manual_review_date) : null;

        if (reviewDate) {
            if (now.diff(reviewDate) < 0) {
                result.push(
                    <p>
                        Employee is flagged for <b>MANUAL REVIEW</b> until {data.manual_review_date}
                    </p>,
                );
            }

            if (now.diff(reviewDate) > 0) {
                result.push(
                    <p>
                        <b>MANUAL REVIEW</b> expired {data.manual_review_date}
                    </p>,
                );
            }
        }

        if (data.review_matches_by_ip && data.review_matches_by_ip.length > 0) {
            result.push(
                <p>
                    IP address matches that of another Employee(s) under Review:{' '}
                    {data.review_matches_by_ip
                        ?.slice(0, 5)
                        .map(({match_id}, index) => renderLinks(match_id, index))}
                </p>,
            );
        }

        if (data.recent_account_change) {
            result.push(<p>Recent email/phone change</p>);
        }

        if (data.merge_id) {
            result.push(
                <p>
                    Employee has been <b>MERGED</b> with Employee [{renderLinks(data.merge_id, 0)}]
                </p>,
            );
        }

        if (data.merged_from && data.merged_from.length > 0) {
            result.push(
                <p>
                    Employee has been <b>MERGED</b> from Employee(s) [
                    {data.merged_from.map((id, index) => renderLinks(id, index))}]
                </p>,
            );
        }

        if (data.deceased) {
            result.push(
                <p>
                    Employee is marked <b>DECEASED</b>
                </p>,
            );
        }

        return result;
    }, [data]);

    if (!contentArr.length) {
        return null;
    }

    return (
        <Box mb={1}>
            <Alert severity='warning'>
                <Grid2 container rowSpacing={1} spacing={2}>
                    {contentArr.map((content, i) => (
                        <Grid2 key={i} size={12}>
                            {content}
                        </Grid2>
                    ))}
                </Grid2>
            </Alert>
        </Box>
    );
};
