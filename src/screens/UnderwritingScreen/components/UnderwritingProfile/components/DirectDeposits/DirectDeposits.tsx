import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {DirectDepositsDetails} from './components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileItemModel} from '@/models';

interface DirectDepositsProps {
    item: UnderwritingProfileItemModel;
}

export const DirectDeposits = ({item}: DirectDepositsProps) => {
    const {
        directDepositState: {data, loading, error},
        loadDirectDepositData,
    } = useUnderwritingProfile();

    useEffect(() => {
        loadDirectDepositData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadDirectDepositData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={<DirectDepositsDetails />}
        />
    );
};
