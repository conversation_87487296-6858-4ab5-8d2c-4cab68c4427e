import React from 'react';
import {UnderwritingDirectDepositsModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ActionCell} from '@/components/table/cells';
import {AssuredWorkload} from '@mui/icons-material';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';

export const DirectDepositsActionCell = ({data}: {data: UnderwritingDirectDepositsModel}) => {
    const {setOpenActionModal} = useUnderwritingProfile();

    if (data.primary) {
        return null;
    }

    return (
        <ActionCell
            Icon={<AssuredWorkload color='primary' titleAccess='Set Primary' />}
            onClick={() => {
                setOpenActionModal({
                    type: UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT,
                    props: {id: data.gid},
                });
            }}
        />
    );
};
