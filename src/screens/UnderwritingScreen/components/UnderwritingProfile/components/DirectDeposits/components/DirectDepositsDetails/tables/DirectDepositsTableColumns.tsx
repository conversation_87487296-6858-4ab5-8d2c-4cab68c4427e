import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingDirectDepositsModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {KasFlaggedIcon} from '@/components';
import {DirectDepositsActionCell} from './../components';

const columnHelper = createColumnHelper<UnderwritingDirectDepositsModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingDirectDepositsModel>;

export const DirectDepositsTableColumns = [
    _defaultInfoColumn('aba_number', 'ABA'),
    _defaultInfoColumn('account_number', 'Account'),
    _defaultInfoColumn('bank_name', 'Provider'),
    columnHelper.accessor('primary', {
        id: 'primary',
        header: 'Primary',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} testid='uw-direct-deposit-primary' />,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingDirectDepositsModel, string>) => (
            <DirectDepositsActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
