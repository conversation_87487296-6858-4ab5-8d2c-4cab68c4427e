import React from 'react';
import {UnderwritingDirectDepositsModel} from '@/screens/UnderwritingScreen/interfaces';
import {DirectDepositsTableColumns} from './tables';
import {ColumnDef} from '@tanstack/react-table';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {TableView} from '@/views';
import Box from '@mui/material/Box';

export const DirectDepositsDetails = () => {
    const {directDepositState} = useUnderwritingProfile();

    return (
        <Box px={5} pb={1.5} data-testid='uw-direct-deposits'>
            <TableView<UnderwritingDirectDepositsModel>
                loading={directDepositState.loading}
                error={directDepositState.error}
                data={directDepositState.data}
                columns={DirectDepositsTableColumns as ColumnDef<UnderwritingDirectDepositsModel, unknown>[]}
            />
        </Box>
    );
};
