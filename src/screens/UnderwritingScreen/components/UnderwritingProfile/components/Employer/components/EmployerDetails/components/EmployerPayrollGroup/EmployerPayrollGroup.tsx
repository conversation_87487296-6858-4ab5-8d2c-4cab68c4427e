import './styles.scss';

import React from 'react';
import {Paper, Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {UnderwritingEmployerModel} from '@/screens/UnderwritingScreen/interfaces';

interface EmployerPayrollGroupProps {
    data: UnderwritingEmployerModel;
}

export const EmployerPayrollGroup = ({data}: EmployerPayrollGroupProps) => {
    return (
        <Paper className='kas-underwriting-employer-payroll-group' data-testid='uw-employer-payroll-group'>
            <Stack direction='row' spacing={2} p={1.5}>
                <KasInfo label='ID'>{data.payroll_group_id}</KasInfo>
                <KasInfo label='Frequency'>{data.payroll_frequency}</KasInfo>
                <KasInfo label='Mode'>{data.mode}</KasInfo>
                <KasInfo label='DOW'>{data.sample_date}</KasInfo>
                <KasInfo label='Census Updated'>{data.census_upload_run_date}</KasInfo>
                <KasInfo label='Payroll Uploaded'>{data.payroll_upload_run_date}</KasInfo>
                <KasInfo label='Cash Applied'>{data.deduction_upload_run_date}</KasInfo>
                <KasInfo label='Deduction Lead Days'>{data.payroll_deduction_lead_days}</KasInfo>
            </Stack>
        </Paper>
    );
};
