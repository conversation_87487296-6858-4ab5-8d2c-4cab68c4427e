import './styles.scss';

import React from 'react';
import {Stack} from '@mui/material';
import {KasCopyText, KasInfo, KasInfoPreviewLoading} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const EmployerPreview = () => {
    const {
        employerState: {data, error},
    } = useUnderwritingProfile();

    if (error) {
        return null;
    }

    return (
        <div className='kas-underwriting-employer-preview' data-testid='uw-employer-preview'>
            {data ? (
                <Stack direction='row' spacing={4}>
                    <KasInfo label='Name:'>
                        <KasCopyText>{data.name}</KasCopyText>
                    </KasInfo>
                    <KasInfo label='ID'>
                        {data.gid} [{data.mnemonic}]
                    </KasInfo>
                    <KasInfo label='Industry'>{data.industry}</KasInfo>
                    <KasInfo label='Workflow'>{data.workflow}</KasInfo>
                    <KasInfo label='Deduction Mode'>{data.deduction_mode}</KasInfo>
                </Stack>
            ) : (
                <Stack direction='row' spacing={4}>
                    <KasInfoPreviewLoading />
                    <KasInfoPreviewLoading />
                    <KasInfoPreviewLoading />
                    <KasInfoPreviewLoading />
                </Stack>
            )}
        </div>
    );
};
