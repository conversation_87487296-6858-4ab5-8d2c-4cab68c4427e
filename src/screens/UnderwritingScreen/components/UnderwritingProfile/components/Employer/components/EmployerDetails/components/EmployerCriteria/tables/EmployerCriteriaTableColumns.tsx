import {UnderwritingEmployerCriteriaModel} from '@/screens/UnderwritingScreen/interfaces';
import {createColumnHelper} from '@tanstack/react-table';
import {CriteriaParams} from './../components';

const columnHelper = createColumnHelper<UnderwritingEmployerCriteriaModel>();

export const EmployerCriteriaTableColumns = [
    columnHelper.accessor('rule', {
        id: 'category',
        header: 'Category',
        cell: (props) => props.getValue().split('.').shift(),
    }),
    columnHelper.accessor('rule', {
        id: 'rule',
        header: 'Rule',
        cell: (props) => props.getValue().split('.').pop(),
    }),
    columnHelper.accessor('criteria', {
        id: 'params',
        header: 'Params',
        cell: (props) => <CriteriaParams data={props.getValue()} />,
    }),
];
