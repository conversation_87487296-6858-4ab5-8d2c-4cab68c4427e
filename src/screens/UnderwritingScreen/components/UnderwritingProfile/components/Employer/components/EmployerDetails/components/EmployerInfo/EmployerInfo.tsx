import './styles.scss';

import React from 'react';
import {Grid2} from '@mui/material';
import {KasCopyText, KasFlaggedIcon, KasInfo, KasPhoneTooltip} from '@/components';
import {UnderwritingEmployerModel} from '@/screens/UnderwritingScreen/interfaces';

interface EmployerInfoProps {
    data: UnderwritingEmployerModel;
}

export const EmployerInfo = ({data}: EmployerInfoProps) => {
    return (
        <div className='kas-underwriting-employer-info' data-testid='uw-employer-info'>
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='ID:' isInline>
                        {data.gid} [{data.mnemonic}]
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Name:' isInline>
                        <KasCopyText>{data.name}</KasCopyText>
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Active:' isInline>
                        <KasFlaggedIcon
                            flagged={data.active}
                            sx={{height: 16}}
                            testid='uw-employer-info-active'
                        />
                    </KasInfo>
                </Grid2>
            </Grid2>
            {data.primary_employer_id !== data.gid && (
                <Grid2 container spacing={2}>
                    <Grid2 size={4}></Grid2>
                    <Grid2 size={4}>
                        <KasInfo label='Primary Employer:' isInline>
                            {data.primary_employer_name}
                        </KasInfo>
                    </Grid2>
                </Grid2>
            )}
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Industry:' isInline>
                        {data.industry}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Workflow:' isInline>
                        {data.workflow}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Started:' isInline>
                        {data.start_date}
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='HR Name:' isInline>
                        <KasCopyText>{data.hr_name}</KasCopyText>
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='HR Phone:' isInline>
                        <KasPhoneTooltip phone={data.hr_phone} withCopy />
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='HR Email:' isInline>
                        <KasCopyText>{data.hr_email}</KasCopyText>
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Deduction Mode:' isInline>
                        {data.deduction_mode}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Deduction Export:' isInline>
                        {data.deduction_export}
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Deduction Consolidate:' isInline>
                        <KasFlaggedIcon
                            flagged={!!data.deduction_consolidate}
                            sx={{height: 16}}
                            testid='uw-employer-info-deduction-consolidate'
                        />
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Deduction Lead Days:' isInline>
                        {data.employer_deduction_lead_days}
                    </KasInfo>
                </Grid2>
            </Grid2>
        </div>
    );
};
