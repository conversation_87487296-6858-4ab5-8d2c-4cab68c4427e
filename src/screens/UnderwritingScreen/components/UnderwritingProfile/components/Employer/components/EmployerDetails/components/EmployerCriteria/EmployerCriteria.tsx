import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Typography} from '@mui/material';
import {KasExpandIcon, KasPureTable} from '@/components';
import {UnderwritingEmployerCriteriaModel} from '@/screens/UnderwritingScreen/interfaces';
import {ColumnDef} from '@tanstack/react-table';
import {EmployerCriteriaTableColumns} from './tables';
import {useSecured} from '@/hooks/useSecured';

export const EmployerCriteria = ({criteria}: {criteria: UnderwritingEmployerCriteriaModel[]}) => {
    const {hasAnyDesignation} = useSecured();
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    const data: UnderwritingEmployerCriteriaModel[] = hasAnyDesignation(['MANAGER'])
        ? criteria
        : criteria.filter((item) => {
              const rule = item.rule.split('.').shift();

              return rule === 'UWEmployerEligibility';
          });

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={
                    <KasExpandIcon
                        expanded={true}
                        onClick={handleAccordionChange}
                        testid='uw-employer-criteria'
                    />
                }
                onDoubleClick={handleAccordionChange}>
                <Typography variant='subtitle1' py={1} px={2}>
                    Employer-specific Criteria
                </Typography>
            </AccordionSummary>
            <AccordionDetails data-testid='uw-employer-criteria-details'>
                <Box px={3} pb={2}>
                    <KasPureTable<UnderwritingEmployerCriteriaModel>
                        columns={
                            EmployerCriteriaTableColumns as ColumnDef<
                                UnderwritingEmployerCriteriaModel,
                                unknown
                            >[]
                        }
                        data={data}
                    />
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
