import React, {Fragment} from 'react';
import {
    UnderwritingEmployerCriteriaItemModel,
    UnderwritingEmployerCriteriaPropertyModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {useTheme} from '@mui/material';

interface CriteriaPropertyProps {
    prop: string;
    value: UnderwritingEmployerCriteriaPropertyModel[];
}

const CriteriaProperty = ({prop, value}: CriteriaPropertyProps) => {
    const {
        palette: {error},
    } = useTheme();

    return (
        <div>
            {prop}:{' '}
            {value.map((item, itemIndex) => (
                <Fragment key={itemIndex}>
                    <code
                        style={{
                            backgroundColor: '#f9f2f4',
                            padding: '2px 4px',
                            whiteSpace: 'nowrap',
                            borderRadius: '4px',
                            color: error.main,
                        }}>
                        {item.op} {item.value}
                    </code>
                    {itemIndex < value.length - 1 && ' & '}
                </Fragment>
            ))}
        </div>
    );
};

export const CriteriaParams = ({data}: {data: UnderwritingEmployerCriteriaItemModel[]}) => {
    return data.map((criteria, index) => {
        if (criteria?.properties) {
            const properties = criteria.properties;
            const propertiesArr = Object.keys(properties).map((prop, propIndex) => (
                <CriteriaProperty key={propIndex} prop={prop} value={properties[prop]} />
            ));

            propertiesArr.push(
                <strong key={`value-${index}`}>
                    Value: {criteria.result}
                    {criteria?.step ? `Step: ${criteria.step}` : ''}
                </strong>,
            );

            return propertiesArr;
        } else {
            return <strong key={index}>Default: {criteria.result}</strong>;
        }
    });
};
