import './styles.scss';

import React from 'react';
import {EmployerCriteria, EmployerInfo, EmployerPayrollGroup} from './components';
import {KasLoadingBackDrop} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Typography} from '@mui/material';
import Box from '@mui/material/Box';

export const EmployerDetails = () => {
    const {
        employerState: {data, loading},
    } = useUnderwritingProfile();

    if (!data) {
        return null;
    }

    return (
        <div className='kas-underwriting-employer-details' data-testid='uw-employer-details'>
            {loading && <KasLoadingBackDrop />}
            <Box mb={1}>
                <EmployerInfo data={data} />
            </Box>
            <Typography variant='subtitle1' mb={1}>
                Payroll Group
            </Typography>
            <Box mb={1}>
                <EmployerPayrollGroup data={data} />
            </Box>
            <Box mb={1}>
                <EmployerCriteria criteria={data.criteria} />
            </Box>
        </div>
    );
};
