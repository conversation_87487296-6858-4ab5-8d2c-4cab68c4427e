import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {EmployerDetails, EmployerPreview} from './components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileItemModel} from '@/models';

interface EmployerProps {
    item: UnderwritingProfileItemModel;
}

export const Employer = ({item}: EmployerProps) => {
    const {
        employerState: {data, loading, error},
        loadEmployerData,
    } = useUnderwritingProfile();

    useEffect(() => {
        loadEmployerData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadEmployerData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={<EmployerDetails />}
            PreviewComponent={<EmployerPreview />}
        />
    );
};
