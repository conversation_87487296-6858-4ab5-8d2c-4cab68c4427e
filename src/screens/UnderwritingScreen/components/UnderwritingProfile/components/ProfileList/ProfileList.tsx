import './styles.scss';

import React from 'react';
import {
    Actions,
    Applications,
    Banks,
    DirectDeposits,
    EmployeeProfile,
    Employer,
    IPHistory,
    Loans,
    ModificationHistory,
    PayrollHistory,
    Referrals,
    Reports,
    Transactions,
    UnderwritingApplicationsProvider,
    UnderwritingBanksProvider,
    UnderwritingEmployeeProfileProvider,
    UnderwritingIPHistoryProvider,
    UnderwritingLoansProvider,
    UnderwritingModificationHistoryProvider,
    UnderwritingPayrollHistoryProvider,
    UnderwritingReportsProvider,
    UnderwritingTransactionsProvider,
} from './../../components';
import {SortableList} from '@/views';
import {useUnderwriting} from '@/screens/UnderwritingScreen/useUnderwriting';
import {UnderwritingProfileItemType, UnderwritingProfileItemModel} from '@/models';

export const ProfileList = () => {
    const {updateVisibleProfileItems, activeProfileVisibleItems} = useUnderwriting();

    const renderProfileItem = (item: UnderwritingProfileItemModel) => {
        switch (item.type) {
            case UnderwritingProfileItemType.Actions:
                return <Actions key={item.type} item={item} />;
            case UnderwritingProfileItemType.Employee_Profile:
                return (
                    <UnderwritingEmployeeProfileProvider key={item.type}>
                        <EmployeeProfile item={item} />
                    </UnderwritingEmployeeProfileProvider>
                );
            case UnderwritingProfileItemType.Employer:
                return <Employer key={item.type} item={item} />;
            case UnderwritingProfileItemType.Loans:
                return (
                    <UnderwritingLoansProvider key={item.type}>
                        <Loans item={item} />
                    </UnderwritingLoansProvider>
                );
            case UnderwritingProfileItemType.Reports:
                return (
                    <UnderwritingReportsProvider key={item.type}>
                        <Reports item={item} />
                    </UnderwritingReportsProvider>
                );
            case UnderwritingProfileItemType.Banks:
                return (
                    <UnderwritingBanksProvider key={item.type}>
                        <Banks item={item} />
                    </UnderwritingBanksProvider>
                );
            case UnderwritingProfileItemType.Transactions:
                return (
                    <UnderwritingTransactionsProvider key={item.type}>
                        <Transactions item={item} />
                    </UnderwritingTransactionsProvider>
                );
            case UnderwritingProfileItemType.Payroll_History:
                return (
                    <UnderwritingPayrollHistoryProvider key={item.type}>
                        <PayrollHistory item={item} />
                    </UnderwritingPayrollHistoryProvider>
                );
            case UnderwritingProfileItemType.Applications:
                return (
                    <UnderwritingApplicationsProvider key={item.type}>
                        <Applications item={item} />
                    </UnderwritingApplicationsProvider>
                );
            case UnderwritingProfileItemType.Modification_History:
                return (
                    <UnderwritingModificationHistoryProvider key={item.type}>
                        <ModificationHistory item={item} />
                    </UnderwritingModificationHistoryProvider>
                );
            case UnderwritingProfileItemType.IP_History:
                return (
                    <UnderwritingIPHistoryProvider key={item.type}>
                        <IPHistory item={item} />
                    </UnderwritingIPHistoryProvider>
                );
            case UnderwritingProfileItemType.Referrals:
                return <Referrals key={item.type} item={item} />;
            case UnderwritingProfileItemType.Direct_Deposits:
                return <DirectDeposits key={item.type} item={item} />;
            default:
                return null;
        }
    };

    return (
        <div className='kas-underwriting-profile-list'>
            <SortableList<UnderwritingProfileItemModel>
                items={activeProfileVisibleItems}
                updateItems={updateVisibleProfileItems}
                renderItem={renderProfileItem}
            />
        </div>
    );
};
