import {parseXmlString} from '@/utils/XmlUtils';

interface ClearFraudInsightReportDataModel {
    root?: Element;
    fraud?: Element | null;
    fraudAction?: string | null;
    fraudScore?: string | null;
    fraudCodes?: string | null;
    fraudDescriptions?: string[];
    identityVerification?: Element | null;
    identityVerificationSSN?: string | null;
    fraudIndicator?: Element | null;
    fraudIndicatorAddresses?: boolean;
    fraudIndicatorRisk?: boolean;
    fraudIndicatorResidential?: boolean;
    fraudIndicatorSSN?: boolean;
    fraudIndicatorIssueDate?: boolean;
    fraudIndicatorBelongs?: boolean;
    history?: Element | null;
    SSNValid?: boolean;
    SSNHasHistory?: boolean;
    SSNNotDeceased?: boolean;
    OFACMatch?: boolean;
    OFACScore?: string | null;
    totalHistoricalInquiries?: string | null;
    error: string | null;
}

export const useClearFraudInsightReport = (report: string): ClearFraudInsightReportDataModel => {
    const xmlDoc = parseXmlString(report);
    const root = xmlDoc.querySelector('xml-response');

    if (!root) {
        return {error: 'Error parsing XML'};
    }

    const fraud = root.querySelector('clear-fraud-insight');
    const fraudAction = fraud?.querySelector('action')?.textContent;
    const fraudScore = fraud?.querySelector('score')?.textContent;
    const fraudCodes = fraud?.querySelector('reason-codes')?.textContent?.replace(/,/g, '|');
    const fraudDescriptions = Array.from(
        fraud?.querySelectorAll(
            'non-scorable-reason-description, stability-non-scorable-reason-description',
        ) || [],
    ).flatMap((desc) => desc.textContent?.split('|') || []);

    const identityVerification = fraud?.querySelector('identity-verification');
    const identityVerificationSSN = fraud?.querySelector('ssn-name-address-match-description')?.textContent;

    const fraudIndicator = fraud?.querySelector('indicator');
    const fraudIndicatorAddresses = root.querySelector('inquiry-address-cautious')?.textContent === 'true';
    const fraudIndicatorRisk = root.querySelector('inquiry-address-high-risk')?.textContent !== 'true';
    const fraudIndicatorResidential =
        root.querySelector('inquiry-address-non-residential')?.textContent !== 'true';
    const fraudIndicatorSSN =
        root.querySelector('high-probability-ssn-belongs-to-another')?.textContent === 'true';
    const fraudIndicatorIssueDate =
        root.querySelector('input-ssn-issue-date-cannot-be-verified')?.textContent !== 'true';
    const fraudIndicatorBelongs =
        root.querySelector('high-probability-ssn-belongs-to-another')?.textContent !== 'true';

    const history = root.querySelector('clear-recent-history');

    const SSNValid = root.querySelector('social-security-valid')?.textContent === 'true';
    const SSNHasHistory = root.querySelector('ssn-first-appearance')?.textContent === 'false';
    const SSNNotDeceased = root.querySelector('social-security-deceased')?.textContent === 'false';

    const OFACMatch = root.querySelector('ofac-match')?.textContent === 'false';
    const OFACScore = root.querySelector('ofac-score')?.textContent || '';

    const totalHistoricalInquiries = root.querySelector('total-historical-inquiries')?.textContent || '';

    return {
        root,
        fraud,
        fraudAction,
        fraudScore,
        fraudCodes,
        fraudDescriptions,
        identityVerification,
        identityVerificationSSN,
        fraudIndicator,
        fraudIndicatorAddresses,
        fraudIndicatorRisk,
        fraudIndicatorResidential,
        fraudIndicatorSSN,
        fraudIndicatorIssueDate,
        fraudIndicatorBelongs,
        history,
        SSNValid,
        SSNHasHistory,
        SSNNotDeceased,
        OFACMatch,
        OFACScore,
        totalHistoricalInquiries,
        error: null,
    };
};
