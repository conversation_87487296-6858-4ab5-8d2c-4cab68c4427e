import React, {useMemo} from 'react';
import {ReportsTabType, UnderwritingReportModal} from '../../../../interfaces';
import {useSecured} from '@/hooks/useSecured';
import {Button} from '@mui/material';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

export const PullReport = ({activeTab}: {activeTab: ReportsTabType}) => {
    const {hasAnyRole} = useSecured();
    const {setOpenReportModal} = useUnderwritingReports();

    const showPullReport = useMemo(
        () => {
            if(activeTab === ReportsTabType.Skiptrace){
                return hasAnyRole(['KASH_COLLECTIONS', 'KASH_UNDERWRITING']);
            }

            return [
                ReportsTabType.Credit,
                ReportsTabType.Risk,
                ReportsTabType.Fraud,
                ReportsTabType.Employment,
                ReportsTabType.Bankruptcy,
            ].includes(activeTab) && hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER']);
        }, [activeTab],
    );

    const handlePullReport = async () => {
        setOpenReportModal({
            type: UnderwritingReportModal.Pull_Report,
            props: {report: activeTab, onClose: () => setOpenReportModal(null)},
        });
    };

    if (!showPullReport) {
        return null;
    }

    return (
        <Button variant='contained' color='success' onClick={handlePullReport} data-testid='uw-reports-pull-button'>
            Pull Report
        </Button>
    );
};
