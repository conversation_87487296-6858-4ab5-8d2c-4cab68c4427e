import './styles.scss';

import React, {useState} from 'react';
import {KasLink, KasModal} from '@/components';
import {Typography} from '@mui/material';
import {BankruptcyReportView} from '@/views';
import {BankruptcyReportModel} from '@/interfaces';

interface BankruptcyReportPreviewProps {
    data: BankruptcyReportModel;
}

export const BankruptcyReportPreview = ({data}: BankruptcyReportPreviewProps) => {
    const [openModal, setOpenModal] = useState(false);

    return (
        <>
            <KasLink onClick={() => setOpenModal(true)}>{data.gid}</KasLink>
            <KasModal title='Report Preview' open={openModal} onClose={() => setOpenModal(false)}>
                <div className='kas-underwriting-report-bankruptcy-preview'>
                    <Typography variant='subtitle1' mb={1.5}>
                        Bankruptcy Report {data.report_date}
                    </Typography>
                    {!!data.report?.records?.length && <BankruptcyReportView records={data.report.records} />}
                </div>
            </KasModal>
        </>
    );
};
