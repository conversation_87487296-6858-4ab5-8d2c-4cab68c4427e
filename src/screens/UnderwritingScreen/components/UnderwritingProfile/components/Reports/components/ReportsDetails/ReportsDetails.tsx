import './styles.scss';

import React, {useState} from 'react';
import {Button, ButtonGroup, Stack} from '@mui/material';
import {ReportsTabType} from '../../interfaces';
import {PullReport, ReportsModal, ReportsTabDetails} from './components';

export const ReportsDetails = () => {
    const [activeTab, setActiveTab] = useState<ReportsTabType>(ReportsTabType.Credit);

    return (
        <div className='kas-underwriting-reports-details' data-testid='uw-reports-details'>
            <Stack
                flexDirection='row'
                justifyContent='space-between'
                useFlexGap
                flexWrap='wrap'
                spacing={2}
                mb={2}>
                <ButtonGroup>
                    {Object.entries(ReportsTabType).map(([key, value]) => (
                        <Button
                            data-testid={`uw-reports-tab-${value}`}
                            key={key}
                            variant={activeTab === value ? 'contained' : 'text'}
                            onClick={() => setActiveTab(value)}>
                            {value} Reports
                        </Button>
                    ))}
                </ButtonGroup>
                <PullReport activeTab={activeTab} />
            </Stack>
            <ReportsTabDetails activeTab={activeTab} />
            <ReportsModal />
        </div>
    );
};
