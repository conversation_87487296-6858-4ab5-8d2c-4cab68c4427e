import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {ReportActions} from './../components';
import {RiskReportPreviewCell} from './cells';
import {ReportEntityClass} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {RiskReportModel} from '@/interfaces';

const columnHelper = createColumnHelper<RiskReportModel>();

const _defaultInfoColumn = defaultInfoColumn<RiskReportModel>;
const _strikeContent = (props: CellContext<RiskReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const RiskReportTableColumns = [
    _defaultInfoColumn('gid', 'ID', undefined, _strikeContent),
    columnHelper.accessor('reporting_agency', {
        id: 'reporting_agency',
        header: 'Provider',
        cell: (props) => <RiskReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    columnHelper.accessor('override', {
        id: 'override',
        header: 'Override',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
    _defaultInfoColumn('override_user_name', 'Override By', undefined, _strikeContent),
    columnHelper.accessor('comments', {
        id: 'comments',
        header: 'Comment',
        cell: (props: CellContext<RiskReportModel, string[]>) => {
            const {comments} = props.row.original;

            return comments.join(', ');
        },
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<RiskReportModel, string>) => (
            <ReportActions id={props.row.original.gid} entityClass={ReportEntityClass.RiskReport} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
