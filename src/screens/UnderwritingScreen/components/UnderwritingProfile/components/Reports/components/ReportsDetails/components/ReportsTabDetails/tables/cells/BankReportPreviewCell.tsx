import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, KasStrike} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {BankReportModel} from '@/interfaces';

interface BankReportPreviewCellProps {
    data: BankReportModel;
}

export const BankReportPreviewCell = ({data}: BankReportPreviewCellProps) => {
    const {setOpenReportModal} = useUnderwritingReports();
    return (
        <KasStrike isStrike={!data.active}>
            <KasLink
                onClick={() => {
                    setOpenReportModal({
                        type: UnderwritingReportModal.Bank_Report_Preview,
                        props: {gid: data.gid, employeeId: data.employee_id, applications: data.applications},
                    });
                }}>
                {data.gid}
            </KasLink>
        </KasStrike>
    );
};
