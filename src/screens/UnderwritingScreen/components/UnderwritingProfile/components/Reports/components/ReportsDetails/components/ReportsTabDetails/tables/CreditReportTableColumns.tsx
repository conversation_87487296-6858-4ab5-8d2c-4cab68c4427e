import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {ReportActions} from './../components';
import {CreditReportPreviewCell} from './cells';
import {ReportEntityClass} from './../../../../../interfaces';
import {CreditReportModel} from '@/interfaces';
import {ReportComment} from '@/views/reports';

const columnHelper = createColumnHelper<CreditReportModel>();

const _defaultInfoColumn = defaultInfoColumn<CreditReportModel>;
const _strikeContent = (props: CellContext<CreditReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const CreditReportTableColumns = [
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'ID',
        cell: (props) => <CreditReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('reporting_agency', 'Provider', undefined, _strikeContent),
    columnHelper.accessor('hard_inquiry', {
        id: 'hard_inquiry',
        header: 'Inquiry',
        cell: (props) => (props.getValue() ? 'HARD' : 'SOFT'),
    }),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    columnHelper.accessor('override', {
        id: 'override',
        header: 'Override',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
    _defaultInfoColumn('override_user_name', 'Override By'),
    columnHelper.accessor('comment', {
        id: 'comment',
        header: 'Comment',
        cell: (props: CellContext<CreditReportModel, string>) => (
            <ReportComment key={`credit-${props.row.original.gid}`} comment={props.getValue()} />
        ),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<CreditReportModel, string>) => (
            <ReportActions id={props.row.original.gid} entityClass={ReportEntityClass.CreditReport} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
