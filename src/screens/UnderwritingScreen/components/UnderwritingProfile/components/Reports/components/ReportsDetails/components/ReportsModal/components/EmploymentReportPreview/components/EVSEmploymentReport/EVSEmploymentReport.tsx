import React, {Fragment} from 'react';
import {Grid2, Paper, Typography} from '@mui/material';
import {KasInfo} from '@/components';
import {ReportDivider} from '@/components/reports';
import {ErrorView} from '@/views';
import {useEVSEmploymentReport} from './useEVSEmploymentReport';

export const EVSEmploymentReport = ({data}: {data: string}) => {
    const report = useEVSEmploymentReport(data);

    if (report?.error) {
        return <ErrorView error={report.error} />;
    }

    return (
        <Paper elevation={0}>
            <Grid2 container p={1.5} rowSpacing={1}>
                <Grid2 size={12}>
                    <Typography variant='subtitle1'>Employment Report</Typography>
                </Grid2>
                <ReportDivider />
                <Grid2 size={12}>
                    <KasInfo label='Transaction Code:' isInline>
                        {report?.transactionCode}
                    </KasInfo>
                </Grid2>
                {report?.items?.map((item, index) => (
                    <Fragment key={index}>
                        <ReportDivider />
                        <Grid2 size={12}>
                            <KasInfo label='Employee:' isInline>
                                {item.firstName} {item.middleName} {item.lastName}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={5}>
                            <KasInfo label='Employer:' isInline>
                                {item.employer}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={3}>
                            <KasInfo label='Status:' isInline>
                                <abbr title={`Code: ${item.statusCode}`}> {item.statusMessage}</abbr>
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={4}>
                            <KasInfo label='Hire Date:' isInline>
                                {item.hireDate}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={5}>
                            <KasInfo label='Projected Annual Income:' isInline>
                                {item.projectedIncome}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={3}></Grid2>
                        <Grid2 size={4}>
                            <KasInfo label='As Of:' isInline>
                                {item.infoDate}
                            </KasInfo>
                        </Grid2>
                    </Fragment>
                ))}
            </Grid2>
        </Paper>
    );
};
