import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasStrike} from '@/components';
import {ReportActions} from './../components';
import {ReportEntityClass} from './../../../../../interfaces';
import {ReportComment} from '@/views/reports';
import {EmploymentReportModel} from '@/interfaces';
import {EmploymentReportPreviewCell} from './cells';

const columnHelper = createColumnHelper<EmploymentReportModel>();

const _defaultInfoColumn = defaultInfoColumn<EmploymentReportModel>;
const _strikeContent = (props: CellContext<EmploymentReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const EmploymentReportTableColumns = [
    _defaultInfoColumn('gid', 'ID', undefined, _strikeContent),
    columnHelper.accessor('reporting_agency', {
        id: 'reporting_agency',
        header: 'Provider',
        cell: (props) => <EmploymentReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    _defaultInfoColumn('records', 'Records', undefined, _strikeContent),
    columnHelper.accessor('comment', {
        id: 'comment',
        header: 'Comment',
        cell: (props: CellContext<EmploymentReportModel, string>) => (
            <ReportComment key={`credit-${props.row.original.gid}`} comment={props.getValue()} />
        ),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<EmploymentReportModel, string>) => (
            <ReportActions id={props.row.original.gid} entityClass={ReportEntityClass.EmploymentReport} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
