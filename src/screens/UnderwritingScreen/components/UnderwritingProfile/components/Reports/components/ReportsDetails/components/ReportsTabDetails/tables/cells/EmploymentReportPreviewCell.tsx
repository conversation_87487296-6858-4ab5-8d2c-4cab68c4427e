import React from 'react';
import {Kas<PERSON>ink, KasStrike} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {EmploymentReportModel} from '@/interfaces';

export const EmploymentReportPreviewCell = ({data}: {data: EmploymentReportModel}) => {
    const {setOpenReportModal} = useUnderwritingReports();

    return (
        <KasStrike isStrike={!data.active}>
            {data.records > 0 ? (
                <KasLink
                    onClick={() => {
                        setOpenReportModal({
                            type: UnderwritingReportModal.Employment_Report_Preview,
                            props: {data, onClose: () => setOpenReportModal(null)},
                        });
                    }}>
                    {data.reporting_agency}
                </KasLink>
            ) : (
                data.reporting_agency
            )}
        </KasStrike>
    );
};
