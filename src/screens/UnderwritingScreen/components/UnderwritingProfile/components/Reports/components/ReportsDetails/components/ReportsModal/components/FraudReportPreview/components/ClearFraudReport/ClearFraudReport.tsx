import React from 'react';
import {Grid2, Paper} from '@mui/material';
import {KasInfo} from '@/components';
import {useClearFraudReport} from './useClearFraudReport';
import {
    ReportDescriptions,
    ReportDivider,
    ReportHeaderSection,
    ReportMainActionSection,
} from '@/components/reports';
import {ErrorView} from '@/views';
import {CreditPreviewReportModel} from '@/interfaces';

interface ClarityRiskReportProps {
    data: CreditPreviewReportModel;
}

export const ClearFraudReport = ({data}: ClarityRiskReportProps) => {
    const report = useClearFraudReport(data.report);

    if (report.error) {
        return <ErrorView error={report.error} />;
    }

    return (
        <>
            {report.root && <ReportHeaderSection root={report.root} />}
            <Paper elevation={0}>
                <Grid2 container p={1.5} rowSpacing={1}>
                    {report.root && <ReportMainActionSection root={report.root} />}
                    {report.fraud && (
                        <>
                            <ReportDivider />
                            <Grid2 size={4}>
                                <KasInfo label='Fraud Action:' isInline>
                                    {report.fraudAction}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={3}>
                                <KasInfo label='Fraud Score:' isInline>
                                    {report.fraudScore}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={5}>
                                <KasInfo label='Fraud Codes:' isInline>
                                    {report.fraudCodes}
                                </KasInfo>
                            </Grid2>
                            <ReportDescriptions
                                label='Descriptions:'
                                descriptions={report.fraudDescriptions}
                            />
                        </>
                    )}
                </Grid2>
            </Paper>
        </>
    );
};
