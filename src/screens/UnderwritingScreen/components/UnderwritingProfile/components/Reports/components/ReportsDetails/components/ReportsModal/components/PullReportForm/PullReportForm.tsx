import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Alert, Button, CircularProgress, Grid2} from '@mui/material';
import {PullReportProps, ReportsTabType} from './../../../../../../interfaces';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {useFormik} from 'formik';

type CreditReportInquiryType = 'SOFT_INQUIRY' | 'HARD_INQUIRY';

export const PullReportForm = ({report, onClose}: PullReportProps) => {
    const {gid} = useUnderwritingProfile();
    const {
        onSubmitReportAction,
        loadCreditReportData,
        loadRiskReportData,
        loadFraudReportData,
        loadEmploymentReportData,
        loadBankruptcyReportData,
        loadSkiptraceReportData,
    } = useUnderwritingReports();
    const [submitting, setSubmitting] = useState(false);
    const [inquiry, setInquiry] = useState<CreditReportInquiryType | undefined>();

    const updateTableData = async () => {
        switch (report) {
            case ReportsTabType.Credit:
                await loadCreditReportData();
                break;
            case ReportsTabType.Risk:
                await loadRiskReportData();
                break;
            case ReportsTabType.Fraud:
                await loadFraudReportData();
                break;
            case ReportsTabType.Employment:
                await loadEmploymentReportData();
                break;
            case ReportsTabType.Bankruptcy:
                await loadBankruptcyReportData();
                break;
            case ReportsTabType.Skiptrace:
                await loadSkiptraceReportData();
                break;
        }
    };

    const onSubmit = (inquiry?: CreditReportInquiryType) => async () => {
        if (inquiry) {
            setInquiry(inquiry);
        }
        const url = `/api/secured/underwriting/reports/${gid}/pull-report/${report.toLowerCase()}${inquiry ? `?inquiry=${inquiry}` : ''}`;

        setSubmitting(true);
        await onSubmitReportAction(url, '', 'post', updateTableData);
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit: () => {
            onSubmit()();
        },
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>Are you sure you want to pull a fresh report?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    {report === ReportsTabType.Credit ? (
                        <Grid2 container justifyContent='flex-end' spacing={2}>
                            <Grid2 size={3}>
                                <Button variant='outlined' fullWidth size='small' onClick={onClose}>
                                    Cancel
                                </Button>
                            </Grid2>
                            <Grid2 size={3}>
                                <Button
                                    variant='contained'
                                    fullWidth
                                    size='small'
                                    color='success'
                                    disabled={submitting}
                                    onClick={onSubmit('SOFT_INQUIRY')}>
                                    {submitting && inquiry === 'SOFT_INQUIRY' ? (
                                        <CircularProgress size={16} />
                                    ) : (
                                        'Soft Pull'
                                    )}
                                </Button>
                            </Grid2>
                            <Grid2 size={3}>
                                <Button
                                    variant='contained'
                                    fullWidth
                                    size='small'
                                    disabled={submitting}
                                    onClick={onSubmit('HARD_INQUIRY')}>
                                    {submitting && inquiry === 'HARD_INQUIRY' ? (
                                        <CircularProgress size={16} />
                                    ) : (
                                        'Hard Pull'
                                    )}
                                </Button>
                            </Grid2>
                        </Grid2>
                    ) : (
                        <KasModalFooter submitText='Pull' loading={submitting} onCancel={onClose} />
                    )}
                </Grid2>
            </Grid2>
        </form>
    );
};
