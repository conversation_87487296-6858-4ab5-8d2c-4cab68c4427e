import React from 'react';
import {Grid2} from '@mui/material';
import {EmploymentAtomicAddress, EmploymentAtomicEmploymentReport} from '@/interfaces';
import {KasInfo} from '@/components';
import dayjs from 'dayjs';

export const EmploymentInfo = ({item}: {item: EmploymentAtomicEmploymentReport}) => {
    const getFullAddress = (data: EmploymentAtomicAddress): string => {
        return `${data.line1 || ''} ${data.line2 || ''}, ${data.city || ''}, ${data.state || ''} ${data.postalCode?.substring(0, 5) || ''}`;
    };

    return (
        <Grid2 container rowSpacing={1}>
            <Grid2 size={6}>
                <KasInfo label='Type:' isInline>
                    {item.employment.employeeType}
                </KasInfo>
                <KasInfo label='Start Date:' isInline>
                    {dayjs(item.employment.startDate).format('YYYY-MM-DD')}
                </KasInfo>
                <KasInfo label='Min Months of Employment:' isInline>
                    {item.employment.minimumMonthsOfEmployment}
                </KasInfo>
            </Grid2>
            <Grid2 size={6}>
                <KasInfo label='Title:' isInline>
                    {item.employment.jobTitle}
                </KasInfo>
                <KasInfo label='Status:' isInline>
                    {item.employment.employmentStatus}
                </KasInfo>
            </Grid2>
            <Grid2 size={12}>
                <KasInfo label='Name:' isInline>
                    {item.employment.employer.name}
                </KasInfo>
                <KasInfo label='Address:' isInline>
                    {getFullAddress(item.employment.employer.address)}
                </KasInfo>
            </Grid2>
        </Grid2>
    );
};
