import {defaultInfoColumn} from '@/utils/TableUtils';
import {EmploymentPinwheelPaystubReportModel} from '@/interfaces';
import {createColumnHelper} from '@tanstack/react-table';
import {AmountCell} from '@/components/table/cells';

const columnHelper = createColumnHelper<EmploymentPinwheelPaystubReportModel>();

const _defaultInfoColumn = defaultInfoColumn<EmploymentPinwheelPaystubReportModel>;

export const PaystubReportTableColumns = [
    _defaultInfoColumn('id', 'ID'),
    _defaultInfoColumn('pay_date', 'Pay Date'),
    _defaultInfoColumn('pay_period_start', 'Pay Period Start'),
    _defaultInfoColumn('pay_period_end', 'Pay Period End'),
    columnHelper.accessor('gross_pay_amount', {
        id: 'gross_pay_amount',
        header: 'Gross Pay Amount',
        cell: (info) => {
            const amount = info.row.original.gross_pay_amount ? info.row.original.gross_pay_amount / 100 : 0;

            return <AmountCell data={amount} />;
        },
    }),
];
