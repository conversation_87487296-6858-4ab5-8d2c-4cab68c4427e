import React, {useEffect, useState} from 'react';
import {KasLoading, KasSwitch, KasSwitchWhen} from '@/components';
import {FraudReportPreviewProps} from './../../../../../../interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {ErrorView} from '@/views';
import {ClearFraudInsightReport, ClearFraudReport} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {CreditPreviewReportModel} from '@/interfaces';

export const FraudReportPreview = ({fraud}: FraudReportPreviewProps) => {
    const [state, setState] = useState(getDefaultState<CreditPreviewReportModel>());

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(
            `/api/secured/underwriting/reports/${fraud.employee_id}/fraud/${fraud.gid}`,
        );
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <ErrorView error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                {state.data && (
                    <KasSwitch>
                        <KasSwitchWhen condition={fraud.product === 'CLEAR_FRAUD'}>
                            <ClearFraudReport data={state.data} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={fraud.product === 'CLEAR_FRAUD_INSIGHT'}>
                            <ClearFraudInsightReport data={state.data} />
                        </KasSwitchWhen>
                    </KasSwitch>
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
