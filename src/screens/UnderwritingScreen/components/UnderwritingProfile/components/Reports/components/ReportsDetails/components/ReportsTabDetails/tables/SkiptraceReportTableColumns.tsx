import {defaultInfoColumn} from '@/utils/TableUtils';
import {createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {SkiptraceReportPreviewCell} from './cells';
import {SkiptraceReportModel} from '@/interfaces';

const columnHelper = createColumnHelper<SkiptraceReportModel>();

const _defaultInfoColumn = defaultInfoColumn<SkiptraceReportModel>;

export const SkiptraceReportTableColumns = [
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'ID',
        cell: (props) => <SkiptraceReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('agency', 'Agency'),
    _defaultInfoColumn('product', 'Type'),
    _defaultInfoColumn('search_params', 'Search'),
    _defaultInfoColumn('report_date', 'Report Date'),
];
