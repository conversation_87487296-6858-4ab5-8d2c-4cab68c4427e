import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {EmploymentAtomicStatementReport} from '@/interfaces';
import {CellContext} from '@tanstack/react-table';
import {DownloadCell} from '@/components/table/cells';

const _defaultInfoColumn = defaultInfoColumn<EmploymentAtomicStatementReport>;
const _defaultAmountColumn = defaultAmountColumn<EmploymentAtomicStatementReport>;

export const StatementReportTableColumns = (taskId: string) => [
    _defaultInfoColumn('date', 'Pay Date'),
    _defaultInfoColumn('hours', 'Hours'),
    _defaultAmountColumn('grossAmount', 'Gross Amount'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<EmploymentAtomicStatementReport, string>) => {
            const {paystub} = props.row.original;
            const params = {
                path: `/secured/user/atomic/download`,
                params: {
                    taskId,
                    fileId: paystub._id,
                },
            };

            return <DownloadCell params={JSON.stringify(params)} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
