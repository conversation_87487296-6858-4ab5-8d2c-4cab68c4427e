import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {ReportActions} from './../components';
import {BankReportPreviewCell} from './cells';
import {ReportEntityClass} from './../../../../../interfaces';
import {BankReportModel} from '@/interfaces';
import { WarningAmber } from '@mui/icons-material';
import { BankReportActions } from '../components/ReportActions/BankReportActions';
import { CommentDTO } from '@/models/restrictDTO';

const columnHelper = createColumnHelper<BankReportModel>();

const _defaultInfoColumn = defaultInfoColumn<BankReportModel>;
const _strikeContent = (props: CellContext<BankReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const BankReportTableColumns = [
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'ID',
        cell: (props) => <BankReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('reporting_agency', 'Provider', undefined, _strikeContent),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    columnHelper.accessor('override', {
        id: 'override',
        header: 'Override',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
    columnHelper.accessor('comments', {
        id: 'comments',
        header: 'Comment',
        cell: (props: CellContext<BankReportModel, CommentDTO[]>) => {
            const {comments} = props.row.original;

            return (
                <div>
                    {comments.map((comment, index) => (
                        <div key={index}>{comment.text}</div>
                    ))}
                </div>
            )
        },
    }),
    _defaultInfoColumn('override_user_name', 'Override By'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<BankReportModel, string>) => (
            <BankReportActions
                id={props.row.original.gid}
                entityClass={ReportEntityClass.BankVerificationReport}
                active = {props.row.original.active}
            />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
