import React, {useEffect, useState} from 'react';
import {KasLoading, KasSwitch, KasSwitchWhen} from '@/components';
import {RiskReportPreviewProps} from './../../../../../../interfaces';
import {UnderwritingRiskPreviewReportModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {ErrorView} from '@/views';
import {ClarityRiskReport} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

export const RiskReportPreview = ({report}: RiskReportPreviewProps) => {
    const [state, setState] = useState(getDefaultState<UnderwritingRiskPreviewReportModel>());

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(
            `/api/secured/underwriting/reports/${report.employee_id}/risk/${report.gid}`,
        );
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <ErrorView error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                {state.data && <ClarityRiskReport data={state.data} />}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
