import React from 'react';
import {Payload} from 'recharts/types/component/DefaultLegendContent';
import Box from '@mui/material/Box';

interface CreditReportCustomLegendProps {
    payload?: Payload[];
    hiddenKeys: string[];
    onClick: (key: string) => void;
}

export const CreditReportCustomLegend = ({payload, hiddenKeys, onClick}: CreditReportCustomLegendProps) => {
    return (
        <Box display='flex' justifyContent='center' gap={2}>
            {payload?.map((item) => {
                const key = item.dataKey as string;
                const isHidden = hiddenKeys.includes(key);

                return (
                    <Box
                        key={key}
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            cursor: 'pointer',
                            color: isHidden ? 'text.disabled' : item.color,
                            '&:hover': {
                                textDecoration: 'underline',
                            },
                        }}
                        onClick={() => onClick(key)}>
                        <svg width='14' height='14' viewBox='0 0 32 32'>
                            <path
                                strokeWidth='4'
                                fill='none'
                                stroke={item.color}
                                d='M0,16h10.666666666666666
            A5.333333333333333,5.333333333333333,0,1,1,21.333333333333332,16
            H32M21.333333333333332,16
            A5.333333333333333,5.333333333333333,0,1,1,10.666666666666666,16'></path>
                        </svg>
                        <span>{item.value}</span>
                    </Box>
                );
            })}
        </Box>
    );
};
