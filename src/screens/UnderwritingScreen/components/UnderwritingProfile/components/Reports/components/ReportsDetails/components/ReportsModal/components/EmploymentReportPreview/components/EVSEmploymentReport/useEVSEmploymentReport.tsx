import {parseXmlString} from '@/utils/XmlUtils';
import dayjs from 'dayjs';
import {toCurrency} from '@/utils/FormatUtils';

interface EmploymentInfoData {
    firstName: string;
    middleName?: string;
    lastName: string;
    employer: string;
    statusMessage: string;
    statusCode: string;
    hireDate?: string;
    projectedIncome: string;
    infoDate?: string;
}
interface EmploymentInfoModel {
    items?: EmploymentInfoData[];
    transactionCode?: string;
    error?: string;
}

export const useEVSEmploymentReport = (report: string): EmploymentInfoModel => {
    const xmlDoc = parseXmlString(report);
    const root = xmlDoc.querySelector('TSVTWNSELECTRS');

    if (!root) {
        return {error: 'Error parsing XML'};
    }

    const transactionCode = xmlDoc.querySelector('TRNUID')?.textContent || 'N/A';
    const items: EmploymentInfoData[] = Array.from(root.querySelectorAll('TSVRESPONSE_V100')).map((item) => ({
        firstName: item.querySelector('FIRSTNAME')?.textContent || '',
        middleName: item.querySelector('MIDDLENAME')?.textContent || '',
        lastName: item.querySelector('LASTNAME')?.textContent || '',
        employer: item.querySelector('NAME1')?.textContent || 'N/A',
        statusMessage: item.querySelector('EMPLOYEESTATUS MESSAGE')?.textContent || 'N/A',
        statusCode: item.querySelector('EMPLOYEESTATUS CODE')?.textContent || 'N/A',
        hireDate: item.querySelector('DTMOSTRECENTHIRE')?.textContent
            ? dayjs(item.querySelector('DTMOSTRECENTHIRE')?.textContent).format('YYYY-MM-DD')
            : 'N/A',
        projectedIncome: toCurrency(item.querySelector('TSVPROJINCOME')?.textContent || '0'),
        infoDate: item.querySelector('DTINFO')?.textContent
            ? dayjs(item.querySelector('DTINFO')?.textContent).format('YYYY-MM-DD')
            : 'N/A',
    }));

    return {transactionCode, items};
};
