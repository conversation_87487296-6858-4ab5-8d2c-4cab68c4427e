import React from 'react';
import {Grid2} from '@mui/material';
import {EmploymentPinwheelPaystubReportModel, EmploymentPinwheelReportModel} from '@/interfaces';
import {KasInfo, KasPureTable} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import {ColumnDef} from '@tanstack/react-table';
import {PaystubReportTableColumns} from './tables/PaystubReportTableColumns';
import {EmploymentReportCard} from './../../components';

export const PinwheelEmploymentReport = ({data}: {data: EmploymentPinwheelReportModel}) => {
    return (
        <Grid2 container spacing={1}>
            <EmploymentReportCard title='Employment Report'>
                <Grid2 container rowSpacing={1}>
                    <Grid2 size={6}>
                        <KasInfo label='Employer Name:' isInline>
                            {data.employmentReport.employer_name}
                        </KasInfo>
                        <KasInfo label='Start Date:' isInline>
                            {data.employmentReport.start_date}
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={6}>
                        <KasInfo label='Title:' isInline>
                            {data.employmentReport.title}
                        </KasInfo>
                        <KasInfo label='Status:' isInline>
                            {data.employmentReport.status}
                        </KasInfo>
                    </Grid2>
                </Grid2>
            </EmploymentReportCard>
            <EmploymentReportCard title='Income Report'>
                <Grid2 container rowSpacing={1}>
                    <Grid2 size={12}>
                        <KasInfo label='Compensation:' isInline>
                            {toCurrency(data.incomeReport.compensation_amount / 100)}
                        </KasInfo>
                        <KasInfo label='Pay Rate:' isInline>
                            {data.incomeReport.compensation_unit}
                        </KasInfo>
                    </Grid2>
                </Grid2>
            </EmploymentReportCard>
            <EmploymentReportCard title='Pay Stubs Report'>
                <Grid2 container rowSpacing={1}>
                    <Grid2 size={12}>
                        <KasPureTable<EmploymentPinwheelPaystubReportModel>
                            data={data.paystubsReport}
                            columns={
                                PaystubReportTableColumns as ColumnDef<
                                    EmploymentPinwheelPaystubReportModel,
                                    unknown
                                >[]
                            }
                            sortingColumns={[{id: 'pay_period_end', desc: true}]}
                        />
                    </Grid2>
                </Grid2>
            </EmploymentReportCard>
        </Grid2>
    );
};
