import React, {useMemo, useState} from 'react';
import {Button, ButtonGroup, Grid2} from '@mui/material';
import {ReportDivider} from '@/components/reports';
import {EmploymentAtomicIncomeReport} from '@/interfaces';
import {EmploymentReportCard} from './../../../../components';
import {IncomeInfo} from './components';

interface IncomeReportProps {
    data: EmploymentAtomicIncomeReport[];
    uid: string;
}

export const IncomeReport = ({data, uid}: IncomeReportProps) => {
    const [activeTab, setActiveTab] = useState<string>(!!data.length ? data[0].linkedAccount : '');
    const primaryIncome = useMemo(() => data.find((item) => item.uid === uid), [data]);

    if (!data.length) {
        return null;
    }

    return (
        <EmploymentReportCard title='Income Report'>
            <Grid2 container rowSpacing={1}>
                {primaryIncome && (
                    <EmploymentReportCard title=' Primary Income' primary={true}>
                        <IncomeInfo item={primaryIncome} />
                    </EmploymentReportCard>
                )}
                {!!data.length && (
                    <EmploymentReportCard>
                        <Grid2 container rowSpacing={1}>
                            <Grid2 size={12}>
                                <ButtonGroup>
                                    {data.map((item) => (
                                        <Button
                                            key={item.linkedAccount}
                                            variant={activeTab === item.linkedAccount ? 'contained' : 'text'}
                                            onClick={() => setActiveTab(item.linkedAccount)}>
                                            Account: {item.linkedAccount}
                                        </Button>
                                    ))}
                                </ButtonGroup>
                            </Grid2>
                            <ReportDivider />
                            {data.map((item, index) => (
                                <Grid2 key={index} hidden={activeTab !== item.linkedAccount} size={12}>
                                    <IncomeInfo item={item} />
                                </Grid2>
                            ))}
                        </Grid2>
                    </EmploymentReportCard>
                )}
            </Grid2>
        </EmploymentReportCard>
    );
};
