import React from 'react';
import {Kas<PERSON>ink, KasStrike} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {FraudReportModel} from '@/interfaces';

export const FraudReportPreviewCell = ({data}: {data: FraudReportModel}) => {
    const {setOpenReportModal} = useUnderwritingReports();
    return (
        <KasStrike isStrike={!data.active}>
            <KasLink
                onClick={() => {
                    setOpenReportModal({
                        type: UnderwritingReportModal.Fraud_Report_Preview,
                        props: {fraud: data, onClose: () => setOpenReportModal(null)},
                    });
                }}>
                {data.gid}
            </KasLink>
        </KasStrike>
    );
};
