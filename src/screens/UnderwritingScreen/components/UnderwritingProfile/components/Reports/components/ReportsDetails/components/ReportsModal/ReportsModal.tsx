import './styles.scss';

import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {
    RiskReportPreview,
    ActionReportForm,
    FraudReportPreview,
    PullReportForm,
    EmploymentReportPreview,
} from './components';
import {UnderwritingReportModal} from './../../../../interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {BankReportView, CreditReportView, SkiptraceReportView} from '@/views';

export const ReportsModal = () => {
    const {profile} = useUnderwritingProfile();
    const {openReportModal, setOpenReportModal, getActionReportTitle} = useUnderwritingReports();

    const title = useMemo(() => {
        switch (openReportModal?.type) {
            case UnderwritingReportModal.Action_Report:
                return getActionReportTitle(openReportModal.props.action, openReportModal.props.entityClass);
            case UnderwritingReportModal.Credit_Report_Preview:
            case UnderwritingReportModal.Risk_Report_Preview:
            case UnderwritingReportModal.Fraud_Report_Preview:
            case UnderwritingReportModal.Employment_Report_Preview:
                return 'Report Preview';
            case UnderwritingReportModal.Skiptrace_Report_Preview:
                return 'Skiptrace Report';
            case UnderwritingReportModal.Bank_Report_Preview:
                return 'Plaid Report';
            case UnderwritingReportModal.Pull_Report:
                return 'Pull Report';
            default:
                return 'Empty Report Modal';
        }
    }, [openReportModal]);

    const renderModalContent = useMemo(() => {
        switch (openReportModal?.type) {
            case UnderwritingReportModal.Action_Report:
                return <ActionReportForm {...openReportModal.props} />;
            case UnderwritingReportModal.Credit_Report_Preview:
                return <CreditReportView {...openReportModal.props} config={{experian: {modalView: true}}} />;
            case UnderwritingReportModal.Risk_Report_Preview:
                return <RiskReportPreview {...openReportModal.props} />;
            case UnderwritingReportModal.Fraud_Report_Preview:
                return <FraudReportPreview {...openReportModal.props} />;
            case UnderwritingReportModal.Skiptrace_Report_Preview:
                return <SkiptraceReportView employeeId={profile.employee_id} {...openReportModal.props} />;
            case UnderwritingReportModal.Bank_Report_Preview:
                return <BankReportView {...openReportModal.props} />;
            case UnderwritingReportModal.Pull_Report:
                return <PullReportForm {...openReportModal.props} />;
            case UnderwritingReportModal.Employment_Report_Preview:
                return <EmploymentReportPreview {...openReportModal.props} />;
            default:
                return 'No Available Reports';
        }
    }, [openReportModal?.type]);

    const size = useMemo(() => {
        switch (openReportModal?.type) {
            case UnderwritingReportModal.Credit_Report_Preview:
            case UnderwritingReportModal.Risk_Report_Preview:
            case UnderwritingReportModal.Fraud_Report_Preview:
            case UnderwritingReportModal.Skiptrace_Report_Preview:
            case UnderwritingReportModal.Bank_Report_Preview:
            case UnderwritingReportModal.Employment_Report_Preview:
                return 'large';
            default:
                return 'medium';
        }
    }, [openReportModal?.type]);

    return (
        <KasModal
            title={title}
            size={size}
            className='kas-underwriting-reports-modal'
            open={!!openReportModal}
            onClose={() => setOpenReportModal(null)}
            data-testid='uw-reports-modal'>
            {renderModalContent}
        </KasModal>
    );
};
