import {defaultInfoColumn} from '@/utils/TableUtils';
import {WarningAmber} from '@mui/icons-material';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {BankruptcyReportPreview, ReportActions} from './../components';
import {ReportEntityClass} from './../../../../../interfaces';
import {BankruptcyReportModel} from '@/interfaces';
import {ReportComment} from '@/views/reports';

const columnHelper = createColumnHelper<BankruptcyReportModel>();

const _defaultInfoColumn = defaultInfoColumn<BankruptcyReportModel>;
const _strikeContent = (props: CellContext<BankruptcyReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const BankruptcyReportTableColumns = [
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'ID',
        cell: (props) => {
            const {gid, records} = props.row.original;

            return (
                <KasStrike isStrike={!props.row.original.active}>
                    {!gid || !(records || 0) ? gid : <BankruptcyReportPreview data={props.row.original} />}
                </KasStrike>
            );
        },
    }),
    _defaultInfoColumn('reporting_agency', 'Provider', undefined, _strikeContent),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    _defaultInfoColumn('records', 'Records'),
    columnHelper.accessor('override', {
        id: 'override',
        header: 'Override',
        cell: (props) => {
            const {errors, override, error_message} = props.row.original;

            if (errors > 0) {
                return <WarningAmber color='error' fontSize='small' titleAccess={error_message} />;
            } else {
                return <KasFlaggedIcon flagged={override} />;
            }
        },
    }),
    _defaultInfoColumn('override_user_name', 'Override By'),
    columnHelper.accessor('comment', {
        id: 'comment',
        header: 'Comment',
        cell: (props: CellContext<BankruptcyReportModel, string>) => (
            <ReportComment key={`bankruptcy-${props.row.original.gid}`} comment={props.getValue()} />
        ),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<BankruptcyReportModel, string>) => (
            <ReportActions id={props.row.original.gid} entityClass={ReportEntityClass.BankruptcyReport} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
