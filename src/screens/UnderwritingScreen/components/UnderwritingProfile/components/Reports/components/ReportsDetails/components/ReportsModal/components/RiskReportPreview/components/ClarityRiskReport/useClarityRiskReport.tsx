import {parseXmlString} from '@/utils/XmlUtils';

interface ClarityRiskReportDataModel {
    root?: Element;
    risk?: Element | null;
    riskAction?: string | null;
    riskScore?: string | null;
    riskCodes?: string | null;
    riskDescriptions?: string[];
    reasonCodeDescriptions? :string[];
    SSNValid?: boolean;
    SSNHasHistory?: boolean;
    SSNNotDeceased?: boolean;
    OFACMatch?: boolean;
    OFACScore?: string | null;
    totalHistoricalInquiries?: string | null;
    error: string | null;
}

export const useClarityRiskReport = (report: string): ClarityRiskReportDataModel => {
    const xmlDoc = parseXmlString(report);
    const root = xmlDoc.querySelector('xml-response');

    if (!root) {
        return {error: 'Error parsing XML'};
    }

    const risk = root.querySelector('clear-credit-risk');
    const riskAction = risk?.querySelector('action')?.textContent || '';
    const riskScore =
        risk?.querySelector('score2')?.textContent || risk?.querySelector('score')?.textContent || '';
    const reasonCodesElements = Array.from(risk?.querySelectorAll('reason-codes2') || risk?.querySelectorAll('reason-codes') || []);
    const riskCodes =
        reasonCodesElements
            .filter((element) => !element.getAttribute('nil'))
            .map((element) => element.textContent)
            .join('|') || '';

    const reasonCodeDescriptions = Array.from(
        risk?.querySelectorAll('reason-code-description2') || risk?.querySelectorAll('reason-code-description')  || [],
    ).flatMap((desc) => desc.textContent?.split('|') || []);

    const riskDescriptions = Array.from(
        risk?.querySelectorAll('non-scorable-reason-description2') || risk?.querySelectorAll('non-scorable-reason-description')  || [],
    ).flatMap((desc) => desc.textContent?.split('|') || []);

    const SSNValid = root.querySelector('social-security-valid')?.textContent === 'true';
    const SSNHasHistory = root.querySelector('ssn-first-appearance')?.textContent === 'false';
    const SSNNotDeceased = root.querySelector('social-security-deceased')?.textContent === 'false';

    const OFACMatch = root.querySelector('ofac-match')?.textContent === 'false';
    const OFACScore = root.querySelector('ofac-score')?.textContent || '';

    const totalHistoricalInquiries = root.querySelector('total-historical-inquiries')?.textContent || '';

    return {
        root,
        risk,
        riskAction,
        riskScore,
        riskCodes,
        riskDescriptions,
        reasonCodeDescriptions,
        SSNValid,
        SSNHasHistory,
        SSNNotDeceased,
        OFACMatch,
        OFACScore,
        totalHistoricalInquiries,
        error: null,
    };
};
