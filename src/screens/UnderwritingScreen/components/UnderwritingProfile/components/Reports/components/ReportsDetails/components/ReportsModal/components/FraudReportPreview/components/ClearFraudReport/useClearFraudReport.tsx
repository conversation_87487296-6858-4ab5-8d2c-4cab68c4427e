import {parseXmlString} from '@/utils/XmlUtils';

interface ClearFraudReportDataModel {
    root?: Element;
    fraud?: Element | null;
    fraudAction?: string | null;
    fraudScore?: string | null;
    fraudCodes?: string | null;
    fraudDescriptions?: string[];
    history?: Element | null;
    historyAction?: string | null;
    historyCodes?: string | null;
    historyDesc?: string | null;
    error: string | null;
}

export const useClearFraudReport = (report: string): ClearFraudReportDataModel => {
    const xmlDoc = parseXmlString(report);
    const root = xmlDoc.querySelector('xml-response');

    if (!root) {
        return {error: 'Error parsing XML'};
    }

    const fraud = root.querySelector('clear-fraud');
    const fraudAction = fraud?.querySelector('action')?.textContent;
    const fraudScore = fraud?.querySelector('clear-fraud-score')?.textContent;
    const fraudCodes = fraud?.querySelector('clear-fraud-reason-codes')?.textContent?.replace(/,/g, '|');
    const fraudDescriptions = Array.from(
        fraud?.querySelectorAll('non-scorable-reason-description, clear-fraud-reason-code-description') || [],
    ).flatMap((desc) => desc.textContent?.split('|') || []);

    const history = root.querySelector('clear-recent-history');
    const historyAction = history?.querySelector('action')?.textContent;
    const historyCodes = history?.querySelector('deny-codes')?.textContent;
    const historyDesc = history?.querySelector('deny-descriptions')?.textContent?.replace(/,/g, '|');

    return {
        root,
        fraud,
        fraudAction,
        fraudScore,
        fraudCodes,
        fraudDescriptions,
        history,
        historyAction,
        historyCodes,
        historyDesc,
        error: null,
    };
};
