import React from 'react';
import {Grid2} from '@mui/material';
import {
    EmploymentAtomicReportModel,
    EmploymentAtomicStatementReport,
    EmploymentAtomicTimesheetReport,
} from '@/interfaces';
import {EmploymentReport, IncomeReport} from './components';
import {ColumnDef} from '@tanstack/react-table';
import {StatementReportTableColumns, TimesheetReportTableColumns} from './tables';
import {KasPureTable} from '@/components';
import {EmploymentReportCard} from './../../components';

export const AtomicEmploymentReport = ({data}: {data: EmploymentAtomicReportModel}) => {
    return (
        <Grid2 container spacing={1}>
            <EmploymentReport data={data.employmentReports || []} uid={data.emplymentReportUuid} />
            <IncomeReport data={data.incomeReports || []} uid={data.incomeReportUuid} />
            {data.statementsReport && (
                <EmploymentReportCard title='Statements Report'>
                    <KasPureTable<EmploymentAtomicStatementReport>
                        data={data.statementsReport.statements}
                        columns={
                            StatementReportTableColumns(data.statementsReport?.task) as ColumnDef<
                                EmploymentAtomicStatementReport,
                                unknown
                            >[]
                        }
                        sortingColumns={[{id: 'date', desc: true}]}
                    />
                </EmploymentReportCard>
            )}
            {data.timesheetsReport && (
                <EmploymentReportCard title='Timesheets Report'>
                    <KasPureTable<EmploymentAtomicTimesheetReport>
                        data={data.timesheetsReport}
                        columns={
                            TimesheetReportTableColumns as ColumnDef<
                                EmploymentAtomicTimesheetReport,
                                unknown
                            >[]
                        }
                        sortingColumns={[{id: 'date', desc: true}]}
                    />
                </EmploymentReportCard>
            )}
        </Grid2>
    );
};
