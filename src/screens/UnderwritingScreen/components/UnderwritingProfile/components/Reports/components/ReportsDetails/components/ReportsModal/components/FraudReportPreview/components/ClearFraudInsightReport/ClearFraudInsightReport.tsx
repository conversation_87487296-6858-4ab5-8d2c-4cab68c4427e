import React from 'react';
import {Grid2, Paper, Typography} from '@mui/material';
import {KasFlaggedIcon, KasInfo} from '@/components';
import {useClearFraudInsightReport} from './useClearFraudInsightReport';
import {ErrorView} from '@/views';
import {
    ReportDescriptions,
    ReportDivider,
    ReportHeaderSection,
    ReportHistorySection,
    ReportMainActionSection,
} from '@/components/reports';
import {CreditPreviewReportModel} from '@/interfaces';

interface ClarityRiskReportProps {
    data: CreditPreviewReportModel;
}

export const ClearFraudInsightReport = ({data}: ClarityRiskReportProps) => {
    const report = useClearFraudInsightReport(data.report);

    if (report.error) {
        return <ErrorView error={report.error} />;
    }

    return (
        <>
            {report.root && (
                <ReportHeaderSection reportingAgency={data.reporting_agency} root={report.root} />
            )}
            <Paper elevation={0}>
                <Grid2 container p={1.5} rowSpacing={1}>
                    {report.root && <ReportMainActionSection root={report.root} />}
                    {report.fraud && (
                        <>
                            <ReportDivider />
                            <Grid2 size={4}>
                                <KasInfo label='Fraud Action:' isInline>
                                    {report.fraudAction}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={3}>
                                <KasInfo label='Fraud Score:' isInline>
                                    {report.fraudScore}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={5}>
                                <KasInfo label='Fraud Codes:' isInline>
                                    {report.fraudCodes}
                                </KasInfo>
                            </Grid2>
                            <ReportDescriptions
                                label='Descriptions:'
                                descriptions={report.fraudDescriptions}
                            />
                        </>
                    )}

                    <ReportDivider />
                    <Grid2 size={4}>
                        <Typography variant='subtitle1'>SSN</Typography>
                        <KasInfo label='Valid:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNValid} />
                        </KasInfo>
                        <KasInfo label='Has History:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNHasHistory} />
                        </KasInfo>
                        <KasInfo label='Not Deceased:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNNotDeceased} />
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>OFAC</Typography>
                        <KasInfo label='Match:' isInline>
                            <KasFlaggedIcon flagged={!!report.OFACMatch} />
                        </KasInfo>
                        <KasInfo label='Score:' isInline>
                            {report.OFACScore}
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={5}>
                        <KasInfo label='Inquiries:' isInline>
                            {report.totalHistoricalInquiries}
                        </KasInfo>
                    </Grid2>

                    <ReportDivider />
                    <Grid2 size={12}>
                        <KasInfo label='SSN-Name-Address Match:'>{report.identityVerificationSSN}</KasInfo>
                    </Grid2>

                    {report.fraudIndicator && (
                        <>
                            <ReportDivider />
                            <Grid2 size={12}>
                                <Typography variant='subtitle1'>Fraud Indicators</Typography>
                            </Grid2>
                            <Grid2 size={4}>
                                <KasInfo label='Address:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorAddresses} />
                                </KasInfo>
                                <KasInfo label='Risk:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorRisk} />
                                </KasInfo>
                                <KasInfo label='Residential:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorResidential} />
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={4}>
                                <KasInfo label='SSN:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorSSN} />
                                </KasInfo>
                                <KasInfo label='Issue Date Known:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorIssueDate} />
                                </KasInfo>
                                <KasInfo label='Belongs to Applicant:' isInline>
                                    <KasFlaggedIcon flagged={!!report.fraudIndicatorBelongs} />
                                </KasInfo>
                            </Grid2>
                        </>
                    )}
                    {report.history && <ReportHistorySection history={report.history} />}
                </Grid2>
            </Paper>
        </>
    );
};
