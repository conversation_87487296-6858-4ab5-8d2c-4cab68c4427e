import React from 'react';
import {Kas<PERSON>ink, KasStrike} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {RiskReportModel} from '@/interfaces';

export const RiskReportPreviewCell = ({data}: {data: RiskReportModel}) => {
    const {setOpenReportModal} = useUnderwritingReports();
    return (
        <KasStrike isStrike={!data.active}>
            <KasLink
                onClick={() => {
                    setOpenReportModal({
                        type: UnderwritingReportModal.Risk_Report_Preview,
                        props: {report: data, onClose: () => setOpenReportModal(null)},
                    });
                }}>
                {data.reporting_agency}
            </KasLink>
        </KasStrike>
    );
};
