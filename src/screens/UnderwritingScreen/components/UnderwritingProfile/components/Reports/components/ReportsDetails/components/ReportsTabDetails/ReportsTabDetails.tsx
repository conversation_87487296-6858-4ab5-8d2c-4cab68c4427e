import './styles.scss';

import React, {useEffect, useMemo} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {
    BankruptcyReportTableColumns,
    CreditReportTableColumns,
    EmploymentReportTableColumns,
    FraudReportTableColumns,
    RiskReportTableColumns,
    BankReportTableColumns,
    SkiptraceReportTableColumns,
} from './tables';
import {ReportsTabType} from '../../../../interfaces';
import {PayrollReportsTableView, TableView} from '@/views';
import {useUnderwritingReports} from './../../../../../../components';
import {CreditReportChart} from './../../components';
import {CreditReportModel} from '@/interfaces';
import {
    BankReportModel,
    BankruptcyReportModel,
    EmploymentReportModel,
    FraudReportModel,
    RiskReportModel,
    SkiptraceReportModel,
} from '@/interfaces';

export const ReportsTabDetails = ({activeTab}: {activeTab: ReportsTabType}) => {
    const {
        loadBankruptcyReportData,
        bankruptcyReportState,
        loadCreditReportData,
        creditReportState,
        loadEmploymentReportData,
        employmentReportState,
        loadRiskReportData,
        riskReportState,
        loadFraudReportData,
        fraudReportState,
        loadSkiptraceReportData,
        skiptraceReportState,
        loadBankReportData,
        bankReportState,
        payrollReportState,
        loadPayrollReportData,
    } = useUnderwritingReports();

    const columns = useMemo(() => {
        switch (activeTab) {
            case ReportsTabType.Credit:
                return CreditReportTableColumns;
            case ReportsTabType.Risk:
                return RiskReportTableColumns;
            case ReportsTabType.Fraud:
                return FraudReportTableColumns;
            case ReportsTabType.Employment:
                return EmploymentReportTableColumns;
            case ReportsTabType.Bankruptcy:
                return BankruptcyReportTableColumns;
            case ReportsTabType.Skiptrace:
                return SkiptraceReportTableColumns;
            case ReportsTabType.Digital_Identity:
                return null;
            case ReportsTabType.Bank:
                return BankReportTableColumns;
            default:
                return null;
        }
    }, [activeTab]);

    const loadData = () => {
        switch (activeTab) {
            case ReportsTabType.Credit:
                if (!creditReportState.loading && !creditReportState.data) {
                    loadCreditReportData().then();
                }
                break;
            case ReportsTabType.Risk:
                if (!riskReportState.loading && !riskReportState.data) {
                    loadRiskReportData().then();
                }
                break;
            case ReportsTabType.Fraud:
                if (!fraudReportState.loading && !fraudReportState.data) {
                    loadFraudReportData().then();
                }
                break;
            case ReportsTabType.Employment:
                if (!employmentReportState.loading && !employmentReportState.data) {
                    loadEmploymentReportData().then();
                }
                break;
            case ReportsTabType.Bankruptcy:
                if (!bankruptcyReportState.loading && !bankruptcyReportState.data) {
                    loadBankruptcyReportData().then();
                }
                break;
            case ReportsTabType.Skiptrace:
                if (!skiptraceReportState.loading && !skiptraceReportState.data) {
                    loadSkiptraceReportData().then();
                }
                break;
            case ReportsTabType.Bank:
                if (!bankReportState.loading && !bankReportState.data) {
                    loadBankReportData().then();
                }
                break;
            case ReportsTabType.Payroll:
                if (!payrollReportState.loading && !payrollReportState.data) {
                    loadPayrollReportData().then();
                }
                break;
        }
    };

    const renderContent = () => {
        switch (activeTab) {
            case ReportsTabType.Credit:
                return (
                    <div>
                        {!!creditReportState.data?.length && (
                            <CreditReportChart data={creditReportState.data} />
                        )}
                        <TableView<CreditReportModel>
                            loading={creditReportState.loading}
                            error={creditReportState.error}
                            data={creditReportState.data}
                            columns={columns as ColumnDef<CreditReportModel, unknown>[]}
                            onRetry={loadCreditReportData}
                        />
                    </div>
                );
            case ReportsTabType.Risk:
                return (
                    <TableView<RiskReportModel>
                        loading={riskReportState.loading}
                        error={riskReportState.error}
                        data={riskReportState.data}
                        columns={columns as ColumnDef<RiskReportModel, unknown>[]}
                        onRetry={loadRiskReportData}
                    />
                );
            case ReportsTabType.Fraud:
                return (
                    <TableView<FraudReportModel>
                        loading={fraudReportState.loading}
                        error={fraudReportState.error}
                        data={fraudReportState.data}
                        columns={columns as ColumnDef<FraudReportModel, unknown>[]}
                        onRetry={loadFraudReportData}
                    />
                );
            case ReportsTabType.Employment:
                return (
                    <TableView<EmploymentReportModel>
                        loading={employmentReportState.loading}
                        error={employmentReportState.error}
                        data={employmentReportState.data}
                        columns={columns as ColumnDef<EmploymentReportModel, unknown>[]}
                        onRetry={loadEmploymentReportData}
                    />
                );
            case ReportsTabType.Bankruptcy:
                return (
                    <TableView<BankruptcyReportModel>
                        loading={bankruptcyReportState.loading}
                        error={bankruptcyReportState.error}
                        data={bankruptcyReportState.data}
                        columns={columns as ColumnDef<BankruptcyReportModel, unknown>[]}
                        onRetry={loadBankruptcyReportData}
                    />
                );
            case ReportsTabType.Skiptrace:
                return (
                    <TableView<SkiptraceReportModel>
                        loading={skiptraceReportState.loading}
                        error={skiptraceReportState.error}
                        data={skiptraceReportState.data}
                        columns={columns as ColumnDef<SkiptraceReportModel, unknown>[]}
                        onRetry={loadSkiptraceReportData}
                    />
                );
            case ReportsTabType.Digital_Identity:
                return null;
            case ReportsTabType.Bank:
                return (
                    <TableView<BankReportModel>
                        loading={bankReportState.loading}
                        error={bankReportState.error}
                        data={bankReportState.data}
                        columns={columns as ColumnDef<BankReportModel, unknown>[]}
                        onRetry={loadBankReportData}
                    />
                );
            case ReportsTabType.Payroll:
                return (
                    <PayrollReportsTableView
                        payrollReportState={payrollReportState}
                        loadPayrollReportData={loadPayrollReportData}
                    />
                );
        }
    };

    useEffect(loadData, [activeTab]);

    return (
        <div className='kas-underwriting-reports-tab-details' data-testid='uw-reports-tab-details'>
            {renderContent()}
        </div>
    );
};
