import React, {PropsWithChildren} from 'react';
import {Grid2, Paper, Typography} from '@mui/material';
import {ReportDivider} from '@/components/reports';

interface AtomicEmploymentReportItemProps extends PropsWithChildren {
    title?: string;
    primary?: boolean;
}

export const EmploymentReportCard = ({title, primary = false, children}: AtomicEmploymentReportItemProps) => {
    return (
        <Grid2 size={12}>
            <Paper elevation={0}>
                <Grid2 container p={1.5} rowSpacing={1}>
                    {title && (
                        <>
                            <Grid2 size={12}>
                                <Typography variant='subtitle1' color={primary ? 'primary' : undefined}>
                                    {title}
                                </Typography>
                            </Grid2>
                            <ReportDivider />
                        </>
                    )}
                    <Grid2 size={12}>{children}</Grid2>
                </Grid2>
            </Paper>
        </Grid2>
    );
};
