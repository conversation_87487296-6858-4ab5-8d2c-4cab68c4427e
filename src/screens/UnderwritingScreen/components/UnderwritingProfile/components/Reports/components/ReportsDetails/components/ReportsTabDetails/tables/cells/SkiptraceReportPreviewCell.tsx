import React from 'react';
import {KasLink} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {SkiptraceReportModel} from '@/interfaces';

interface SkiptraceReportPreviewCellProps {
    data: SkiptraceReportModel;
}

export const SkiptraceReportPreviewCell = ({data}: SkiptraceReportPreviewCellProps) => {
    const {setOpenReportModal} = useUnderwritingReports();
    return (
        <KasLink
            onClick={() => {
                setOpenReportModal({
                    type: UnderwritingReportModal.Skiptrace_Report_Preview,
                    props: {skiptrace: data},
                });
            }}>
            {data.gid}
        </KasLink>
    );
};
