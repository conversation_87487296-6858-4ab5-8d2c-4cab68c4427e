import React, {useEffect, useState} from 'react';
import {KasLoading, KasSwitch, KasSwitchWhen} from '@/components';
import {EmploymentReportProps} from './../../../../../../interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {ErrorView} from '@/views';
import {AtomicEmploymentReport, EVSEmploymentReport, PinwheelEmploymentReport} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {
    EmploymentAtomicReportModel,
    EmploymentPinwheelReportModel,
    EmploymentReportDetailModel,
} from '@/interfaces';

export const EmploymentReportPreview = ({data}: EmploymentReportProps) => {
    const [state, setState] = useState(getDefaultState<EmploymentReportDetailModel>());

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(
            `/api/secured/underwriting/reports/${data.employee_id}/employment/${data.gid}`,
        );
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <ErrorView error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                {state.data && (
                    <KasSwitch>
                        <KasSwitchWhen condition={data.reporting_agency === 'EVS'}>
                            <EVSEmploymentReport data={state.data as string} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={data.reporting_agency === 'PINWHEEL'}>
                            <PinwheelEmploymentReport data={state.data as EmploymentPinwheelReportModel} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={data.reporting_agency === 'ATOMIC'}>
                            <AtomicEmploymentReport data={state.data as EmploymentAtomicReportModel} />
                        </KasSwitchWhen>
                    </KasSwitch>
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
