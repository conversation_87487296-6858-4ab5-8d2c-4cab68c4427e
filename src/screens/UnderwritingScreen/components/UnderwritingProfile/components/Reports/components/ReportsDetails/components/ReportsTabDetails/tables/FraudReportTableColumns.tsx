import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {ReportActions} from './../components';
import {FraudReportPreviewCell} from './cells';
import {ReportEntityClass} from './../../../../../interfaces';
import {FraudReportModel} from '@/interfaces';

const columnHelper = createColumnHelper<FraudReportModel>();

const _defaultInfoColumn = defaultInfoColumn<FraudReportModel>;
const _strikeContent = (props: CellContext<FraudReportModel, string>) => (
    <KasStrike isStrike={!props.row.original.active}>{props.getValue()}</KasStrike>
);

export const FraudReportTableColumns = [
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'ID',
        cell: (props) => <FraudReportPreviewCell data={props.row.original} />,
    }),
    _defaultInfoColumn('reporting_agency', 'Provider', undefined, _strikeContent),
    _defaultInfoColumn('report_date', 'Date', undefined, _strikeContent),
    columnHelper.accessor('override', {
        id: 'override',
        header: 'Override',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
    _defaultInfoColumn('override_user_name', 'Override By'),
    columnHelper.accessor('comments', {
        id: 'comments',
        header: 'Comment',
        cell: (props: CellContext<FraudReportModel, string[]>) => {
            const {comments} = props.row.original;

            return comments.join(', ');
        },
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<FraudReportModel, string>) => (
            <ReportActions id={props.row.original.gid} entityClass={ReportEntityClass.FraudReport} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
