import './styles.scss';

import React from 'react';
import {TooltipProps} from 'recharts';
import {KasInfo} from '@/components';

export const CreditReportCustomTooltip: React.FC<TooltipProps<number, string>> = ({active, payload}) => {
    if (active && payload && payload.length) {
        return (
            <div className='credit-report-custom-tooltip'>
                <KasInfo label='ID:' isInline>
                    {payload[0].payload.gid}
                </KasInfo>
                {payload.map((item) => (
                    <div key={item.dataKey} className='credit-report-custom-tooltip__item'>
                        <span className='credit-report-custom-tooltip__label' style={{color: item.stroke}}>
                            {item.name}
                        </span>
                        : {item.value}
                    </div>
                ))}
            </div>
        );
    }

    return null;
};
