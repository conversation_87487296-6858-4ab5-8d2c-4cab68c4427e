import React from 'react';
import {Ka<PERSON><PERSON><PERSON>, KasStrike} from '@/components';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingReportModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Reports/interfaces';
import {CreditReportModel} from '@/interfaces';

interface CreditReportPreviewCellProps {
    data: CreditReportModel;
}

export const CreditReportPreviewCell = ({data}: CreditReportPreviewCellProps) => {
    const {setOpenReportModal} = useUnderwritingReports();
    return (
        <KasStrike isStrike={!data.active}>
            <KasLink
                onClick={() => {
                    setOpenReportModal({
                        type: UnderwritingReportModal.Credit_Report_Preview,
                        props: {credit: data},
                    });
                }}>
                {data.gid}
            </KasLink>
        </KasStrike>
    );
};
