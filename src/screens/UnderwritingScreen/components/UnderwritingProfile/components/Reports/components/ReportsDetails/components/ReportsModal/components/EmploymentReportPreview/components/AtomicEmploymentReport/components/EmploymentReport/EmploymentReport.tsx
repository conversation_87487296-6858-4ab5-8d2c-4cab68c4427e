import React, {useMemo, useState} from 'react';
import {Button, ButtonGroup, Grid2} from '@mui/material';
import {ReportDivider} from '@/components/reports';
import {EmploymentAtomicEmploymentReport} from '@/interfaces';
import {EmploymentInfo} from './components';
import {EmploymentReportCard} from './../../../../components';

interface EmploymentReportProps {
    data: EmploymentAtomicEmploymentReport[];
    uid: string;
}

export const EmploymentReport = ({data, uid}: EmploymentReportProps) => {
    const [activeTab, setActiveTab] = useState<string>(!!data.length ? data[0].linkedAccount : '');
    const primaryEmployment = useMemo(() => data.find((item) => item.uid === uid), [data]);

    if (!data.length) {
        return null;
    }

    return (
        <EmploymentReportCard title='Employment Report'>
            <Grid2 container rowSpacing={1}>
                {primaryEmployment && (
                    <EmploymentReportCard title=' Primary Employment' primary={true}>
                        <EmploymentInfo item={primaryEmployment} />
                    </EmploymentReportCard>
                )}
                {!!data?.length && (
                    <EmploymentReportCard>
                        <Grid2 container rowSpacing={1}>
                            <Grid2 size={12}>
                                <ButtonGroup>
                                    {data.map((item) => (
                                        <Button
                                            key={item.linkedAccount}
                                            variant={activeTab === item.linkedAccount ? 'contained' : 'text'}
                                            onClick={() => setActiveTab(item.linkedAccount)}>
                                            Account: {item.linkedAccount}
                                        </Button>
                                    ))}
                                </ButtonGroup>
                            </Grid2>
                            <ReportDivider />
                            {data.map((item, index) => (
                                <Grid2 key={index} hidden={activeTab !== item.linkedAccount} size={12}>
                                    <EmploymentInfo key={index} item={item} />
                                </Grid2>
                            ))}
                        </Grid2>
                    </EmploymentReportCard>
                )}
            </Grid2>
        </EmploymentReportCard>
    );
};
