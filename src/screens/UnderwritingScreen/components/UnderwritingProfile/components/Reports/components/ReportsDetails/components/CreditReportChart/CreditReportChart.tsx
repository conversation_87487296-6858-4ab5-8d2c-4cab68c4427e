import React, {useMemo, useState, useCallback} from 'react';
import {useTheme} from '@mui/material';
import {
    CartesianGrid,
    ResponsiveContainer,
    Tooltip as ChartTooltip,
    XAxis,
    <PERSON>Axis,
    <PERSON><PERSON>hart,
    Legend,
    Line,
} from 'recharts';
import {CreditReportCustomLegend, CreditReportCustomTooltip} from './components';
import {CreditReportChartData} from './interfaces';
import {CreditReportModel} from '@/interfaces';

export const CreditReportChart = ({data}: {data: CreditReportModel[]}) => {
    const theme = useTheme();
    const [hiddenLines, setHiddenLines] = useState<string[]>([]);

    const handleLegendClick = useCallback((dataKey: string) => {
        setHiddenLines((prev) =>
            prev.includes(dataKey) ? prev.filter((key) => key !== dataKey) : [...prev, dataKey],
        );
    }, []);

    const chartDataArr: CreditReportChartData[] = useMemo(() => {
        const result: CreditReportChartData[] = [];

        data.forEach(({gid, report_date, fico_score, vantage_score}) => {
            result.push({
                gid,
                date: report_date,
                fico: fico_score || 0,
                vantage: vantage_score || 0,
            });
        });

        result.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

        return result;
    }, [data]);

    if (!chartDataArr.length) {
        return null;
    }

    return (
        <ResponsiveContainer width='100%' height={400}>
            <LineChart
                data={chartDataArr}
                margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                }}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis
                    dataKey='date'
                    padding={{left: 30, right: 30}}
                    style={{fontSize: 'var(--small-text-size)'}}
                />
                <YAxis
                    name='Credit Score'
                    style={{fontSize: 'var(--small-text-size)'}}
                    ticks={[0, 200, 400, 600, 800, 1000]}
                />
                <ChartTooltip content={<CreditReportCustomTooltip />} cursor={{strokeDasharray: '3 3'}} />
                <Legend
                    content={() => (
                        <CreditReportCustomLegend
                            payload={[
                                {
                                    value: 'Fico',
                                    type: 'line',
                                    id: 'Fico',
                                    color: theme.palette.primary.main,
                                    dataKey: 'fico',
                                },
                                {
                                    value: 'Vantage',
                                    type: 'line',
                                    id: 'Vantage',
                                    color: theme.palette.text.primary,
                                    dataKey: 'vantage',
                                },
                            ]}
                            hiddenKeys={hiddenLines}
                            onClick={handleLegendClick}
                        />
                    )}
                />
                {!hiddenLines.includes('fico') && (
                    <Line type='monotone' name='Fico' dataKey='fico' stroke={theme.palette.primary.main} />
                )}
                {!hiddenLines.includes('vantage') && (
                    <Line
                        type='monotone'
                        name='Vantage'
                        dataKey='vantage'
                        stroke={theme.palette.text.primary}
                    />
                )}
            </LineChart>
        </ResponsiveContainer>
    );
};
