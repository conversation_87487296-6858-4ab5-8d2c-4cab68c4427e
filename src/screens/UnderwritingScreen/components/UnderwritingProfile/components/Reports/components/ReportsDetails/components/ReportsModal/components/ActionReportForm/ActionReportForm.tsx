import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Grid2, TextField} from '@mui/material';
import {useFormik} from 'formik';
import {ActionReportFormValues, validationSchema} from './schema';
import {ActionReportProps, ReportEntityClass} from './../../../../../../interfaces';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

export const ActionReportForm = ({id, action, entityClass, onClose}: ActionReportProps) => {
    const {
        onSubmitReportAction,
        loadBankruptcyReportData,
        loadCreditReportData,
        loadEmploymentReportData,
        loadRiskReportData,
        loadBankReportData,
    } = useUnderwritingReports();
    const [submitting, setSubmitting] = useState(false);

    const updateTableData = async () => {
        switch (entityClass) {
            case ReportEntityClass.BankruptcyReport:
                await loadBankruptcyReportData();
                break;
            case ReportEntityClass.CreditReport:
                await loadCreditReportData();
                break;
            case ReportEntityClass.EmploymentReport:
                await loadEmploymentReportData();
                break;
            case ReportEntityClass.RiskReport:
                await loadRiskReportData();
                break;
            case ReportEntityClass.BankVerificationReport:
                await loadBankReportData();
                break;
        }
    };

    const onSubmit = async (values: ActionReportFormValues) => {
        const url = `/api/secured/underwriting/reports/${action}`;
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: id,
            comment: values.comment,
        });

        setSubmitting(true);
        await onSubmitReportAction(url, body, 'put', updateTableData);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={1}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
