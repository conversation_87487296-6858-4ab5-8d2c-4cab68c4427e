import React from 'react';
import {Stack} from '@mui/material';
import {ActionCell} from '@/components/table/cells';
import {AssuredWorkload, Delete} from '@mui/icons-material';
import {useUnderwritingReports} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ReportEntityClass, UnderwritingReportModal} from './../../../../../../interfaces';
import {useSecured} from '@/hooks/useSecured';

interface ReportActionsProps {
    id: number;
    entityClass: ReportEntityClass;
}

export const ReportActions = ({id, entityClass}: ReportActionsProps) => {
    const {hasAnyRole} = useSecured();
    const {setOpenReportModal} = useUnderwritingReports();
    const onClose = () => setOpenReportModal(null);
    const showOverride =
        hasAnyRole(['KASH_ADMIN', 'KASH_SUPPORT:MANAGER']);

    return (
        <Stack direction='row' spacing={1}>
            {showOverride && (
                <ActionCell
                    data-testid='uw-report-actions-override'
                    Icon={<AssuredWorkload color='primary' titleAccess='Override Report' />}
                    onClick={() => {
                        setOpenReportModal({
                            type: UnderwritingReportModal.Action_Report,
                            props: {id, action: 'override', entityClass, onClose},
                        });
                    }}
                />
            )}
            <ActionCell
                data-testid='uw-report-actions-delete'
                Icon={<Delete color='error' titleAccess='Delete Report' />}
                onClick={() => {
                    setOpenReportModal({
                        type: UnderwritingReportModal.Action_Report,
                        props: {id, action: 'flag', entityClass, onClose},
                    });
                }}
            />
        </Stack>
    );
};
