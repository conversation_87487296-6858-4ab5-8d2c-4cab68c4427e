import React from 'react';
import {Grid2} from '@mui/material';
import {EmploymentAtomicIncomeReport} from '@/interfaces';
import {KasInfo} from '@/components';
import dayjs from 'dayjs';
import {toCurrency} from '@/utils/FormatUtils';

export const IncomeInfo = ({item}: {item: EmploymentAtomicIncomeReport}) => {
    return (
        <Grid2 container rowSpacing={1}>
            <Grid2 size={6}>
                <KasInfo label='Income:' isInline>
                    {toCurrency(item.income.income)}
                </KasInfo>
                <KasInfo label='Type:' isInline>
                    {item.income.incomeType}
                </KasInfo>
                <KasInfo label='Annual:' isInline>
                    {toCurrency(item.income.annualIncome)}
                </KasInfo>
                <KasInfo label='Next Pay Date:' isInline>
                    {dayjs(item.income.nextExpectedPayDate).format('YYYY-MM-DD')}
                </KasInfo>
            </Grid2>
            <Grid2 size={6}>
                <KasInfo label='Hourly Income:' isInline>
                    {toCurrency(item.income.hourlyIncome)}
                </KasInfo>
                <KasInfo label='Net Hourly Rate:' isInline>
                    {toCurrency(item.income.netHourlyRate)}
                </KasInfo>
                <KasInfo label='Pay Cycle:' isInline>
                    {item.income.payCycle}
                </KasInfo>
            </Grid2>
        </Grid2>
    );
};
