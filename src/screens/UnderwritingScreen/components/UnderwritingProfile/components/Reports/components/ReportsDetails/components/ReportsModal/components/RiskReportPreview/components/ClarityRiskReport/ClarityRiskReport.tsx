import React from 'react';
import {UnderwritingRiskPreviewReportModel} from '@/screens/UnderwritingScreen/interfaces';
import {Grid2, Paper, Typography} from '@mui/material';
import {KasFlaggedIcon, KasInfo} from '@/components';
import {useClarityRiskReport} from './useClarityRiskReport';
import {
    ReportDescriptions,
    ReportDivider,
    ReportHeaderSection,
    ReportMainActionSection,
} from '@/components/reports';
import {ErrorView} from '@/views';

interface ClarityRiskReportProps {
    data: UnderwritingRiskPreviewReportModel;
}

export const ClarityRiskReport = ({data}: ClarityRiskReportProps) => {
    const report = useClarityRiskReport(data.report);

    if (report.error) {
        return <ErrorView error={report.error} />;
    }

    return (
        <>
            {report.root && (
                <ReportHeaderSection reportingAgency={data.reporting_agency} root={report.root} />
            )}
            <Paper elevation={0}>
                <Grid2 container p={1.5} rowSpacing={1}>
                    {report.root && <ReportMainActionSection root={report.root} />}
                    {report.risk && (
                        <>
                            <ReportDivider />
                            <Grid2 size={4}>
                                <KasInfo label='Risk Action:' isInline>
                                    {report.riskAction}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={3}>
                                <KasInfo label='Risk Score:' isInline>
                                    {report.riskScore}
                                </KasInfo>
                            </Grid2>
                            <Grid2 size={5}>
                                <KasInfo label='Risk Codes:' isInline>
                                    {report.riskCodes}
                                </KasInfo>
                            </Grid2>
                            <ReportDescriptions
                                label='Descriptions:'
                                descriptions={report.riskDescriptions}
                            />
                            <ReportDescriptions
                                label='Reason Code Descriptions:'
                                descriptions={report.reasonCodeDescriptions}
                            />
                        </>
                    )}
                    <ReportDivider />
                    <Grid2 size={4}>
                        <Typography variant='subtitle1'>SSN</Typography>
                        <KasInfo label='Valid:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNValid} />
                        </KasInfo>
                        <KasInfo label='Has History:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNHasHistory} />
                        </KasInfo>
                        <KasInfo label='Not Deceased:' isInline>
                            <KasFlaggedIcon flagged={!!report.SSNNotDeceased} />
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={3}>
                        <Typography variant='subtitle1'>OFAC</Typography>
                        <KasInfo label='Match:' isInline>
                            <KasFlaggedIcon flagged={!!report.OFACMatch} />
                        </KasInfo>
                        <KasInfo label='Score:' isInline>
                            {report.OFACScore}
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={5}>
                        <KasInfo label='Inquiries:' isInline>
                            {report.totalHistoricalInquiries}
                        </KasInfo>
                    </Grid2>
                </Grid2>
            </Paper>
        </>
    );
};
