import React from 'react';
import {
    ProfileItem,
    useUnderwritingReports,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ReportsDetails} from './components';
import {UnderwritingProfileItemModel} from '@/models';

interface ReportsProps {
    item: UnderwritingProfileItemModel;
}

export const Reports = ({item}: ReportsProps) => {
    const {
        bankruptcyReportState,
        loadBankruptcyReportData,
        creditReportState,
        loadCreditReportData,
        employmentReportState,
        loadEmploymentReportData,
        riskReportState,
        loadRiskReportData,
        fraudReportState,
        loadFraudReportData,
        skiptraceReportState,
        loadSkiptraceReportData,
        bankReportState,
        loadBankReportData,
        payrollReportState,
        loadPayrollReportData,
    } = useUnderwritingReports();

    const onRefreshHandler = () => {
        if (creditReportState.data || creditReportState.error) {
            loadCreditReportData().then();
        }
        if (riskReportState.data || riskReportState.error) {
            loadRiskReportData().then();
        }
        if (fraudReportState.data || fraudReportState.error) {
            loadFraudReportData().then();
        }
        if (employmentReportState.data || employmentReportState.error) {
            loadEmploymentReportData().then();
        }
        if (bankruptcyReportState.data || bankruptcyReportState.error) {
            loadBankruptcyReportData().then();
        }
        if (skiptraceReportState.data || skiptraceReportState.error) {
            loadSkiptraceReportData().then();
        }
        if (bankReportState.data || bankReportState.error) {
            loadBankReportData().then();
        }
        if (payrollReportState.data || payrollReportState.error) {
            loadPayrollReportData().then();
        }
    };

    return (
        <ProfileItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={false}
            loadingError={''}
            loaded={true}
            DetailsComponent={<ReportsDetails />}
        />
    );
};
