import React, {createContext, useContext, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {
    BankReportModel,
    BankruptcyReportModel,
    Completable,
    CreditReportModel,
    EmploymentReportModel,
    FraudReportModel,
    PayrollReportModel,
    RiskReportModel,
    SkiptraceReportModel,
} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ReportActionType, ReportEntityClass, UnderwritingReportModalProps} from './interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';

interface UnderwritingReportsContextModel {
    openReportModal: UnderwritingReportModalProps | null;
    setOpenReportModal: (value: UnderwritingReportModalProps | null) => void;
    bankruptcyReportState: DataStateInterface<BankruptcyReportModel[]>;
    loadBankruptcyReportData: () => Promise<void>;
    creditReportState: DataStateInterface<CreditReportModel[]>;
    loadCreditReportData: () => Promise<void>;
    employmentReportState: DataStateInterface<EmploymentReportModel[]>;
    loadEmploymentReportData: () => Promise<void>;
    riskReportState: DataStateInterface<RiskReportModel[]>;
    loadRiskReportData: () => Promise<void>;
    fraudReportState: DataStateInterface<FraudReportModel[]>;
    loadFraudReportData: () => Promise<void>;
    skiptraceReportState: DataStateInterface<SkiptraceReportModel[]>;
    loadSkiptraceReportData: () => Promise<void>;
    bankReportState: DataStateInterface<BankReportModel[]>;
    loadBankReportData: () => Promise<void>;
    payrollReportState: DataStateInterface<PayrollReportModel[]>;
    loadPayrollReportData: () => Promise<void>;
    onSubmitReportAction: (
        url: string,
        body: string,
        method: 'post' | 'put' | 'delete',
        onSuccess: () => Promise<void>,
    ) => Promise<void>;
    getActionReportTitle: (action: ReportActionType, entityClass: ReportEntityClass) => string;
}

const UnderwritingReportsContext = createContext<UnderwritingReportsContextModel | undefined>(undefined);

interface UnderwritingReportsProviderProps {
    children: React.ReactNode;
}

export const UnderwritingReportsProvider: React.FC<UnderwritingReportsProviderProps> = ({children}) => {
    const {showMessage} = useSnackbar();
    const {gid} = useUnderwritingProfile();
    const baseUrl = useMemo(() => `/api/secured/underwriting/reports/${gid}`, [gid]);
    const [openReportModal, setOpenReportModal] = useState<UnderwritingReportModalProps | null>(null);
    const [bankruptcyReportState, setBankruptcyReportState] =
        useState(getDefaultState<BankruptcyReportModel[]>());
    const [creditReportState, setCreditReportState] = useState(getDefaultState<CreditReportModel[]>());
    const [employmentReportState, setEmploymentReportState] =
        useState(getDefaultState<EmploymentReportModel[]>());
    const [riskReportState, setRiskReportState] = useState(getDefaultState<RiskReportModel[]>());
    const [fraudReportState, setFraudReportState] = useState(getDefaultState<FraudReportModel[]>());
    const [skiptraceReportState, setSkiptraceReportState] =
        useState(getDefaultState<SkiptraceReportModel[]>());
    const [bankReportState, setBankReportState] = useState(getDefaultState<BankReportModel[]>());
    const [payrollReportState, setPayrollReportState] = useState(getDefaultState<PayrollReportModel[]>());

    const loadBankruptcyReportData = async () => {
        const url = `${baseUrl}/bankruptcy`;

        setBankruptcyReportState(getLoadingState(bankruptcyReportState));
        const response: Completable<BankruptcyReportModel[]> = await apiRequest(url);
        setBankruptcyReportState(getLoadedState(response));
    };

    const loadCreditReportData = async () => {
        const url = `${baseUrl}/credit`;

        setCreditReportState(getLoadingState(creditReportState));
        const response: Completable<CreditReportModel[]> = await apiRequest(url);
        setCreditReportState(getLoadedState(response));
    };

    const loadEmploymentReportData = async () => {
        const url = `${baseUrl}/employment`;

        setEmploymentReportState(getLoadingState(employmentReportState));
        const response: Completable<EmploymentReportModel[]> = await apiRequest(url);
        setEmploymentReportState(getLoadedState(response));
    };

    const loadRiskReportData = async () => {
        const url = `${baseUrl}/risk`;

        setRiskReportState(getLoadingState(riskReportState));
        const response: Completable<RiskReportModel[]> = await apiRequest(url);
        setRiskReportState(getLoadedState(response));
    };

    const loadFraudReportData = async () => {
        const url = `${baseUrl}/fraud`;

        setFraudReportState(getLoadingState(fraudReportState));
        const response: Completable<FraudReportModel[]> = await apiRequest(url);
        setFraudReportState(getLoadedState(response));
    };

    const loadPayrollReportData = async () => {
        const url = `${baseUrl}/payroll`;

        setPayrollReportState(getLoadingState(payrollReportState));
        const response: Completable<PayrollReportModel[]> = await apiRequest(url);
        setPayrollReportState(getLoadedState(response));
    };

    const loadSkiptraceReportData = async () => {
        const url = `${baseUrl}/skiptrace`;

        setSkiptraceReportState(getLoadingState(skiptraceReportState));
        const response: Completable<SkiptraceReportModel[]> = await apiRequest(url);
        setSkiptraceReportState(getLoadedState(response));
    };

    const loadBankReportData = async () => {
        const url = `${baseUrl}/bank/verification`;

        setBankReportState(getLoadingState(bankReportState));
        const response: Completable<BankReportModel[]> = await apiRequest(url);
        setBankReportState(getLoadedState(response));
    };

    const onSubmitReportAction = async (
        url: string,
        body: string,
        method: 'post' | 'put' | 'delete',
        onSuccess: () => Promise<void>,
    ) => {
        const response = await apiRequest(url, {method, body});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await onSuccess();
            setOpenReportModal(null);
        }

        return response;
    };

    const getActionReportTitle = (action: ReportActionType, entityClass: ReportEntityClass) => {
        const selectTitle = (
            overrideTitle = 'Override Report',
            flagTitle = 'Mark Report Inactive',
            reactivateTitle = 'Reactivate Report',
        ) => {
            switch (action) {
                case 'override':
                    return overrideTitle;
                case 'flag':
                    return flagTitle;
                case 'reactivate':
                    return reactivateTitle;
                default:
                    return flagTitle;
            }
        };

        switch (entityClass) {
            case ReportEntityClass.BankruptcyReport:
                return selectTitle('Override Bankruptcy Report', 'Mark Bankruptcy Report Inactive');
            case ReportEntityClass.CreditReport:
                return selectTitle('Override Credit Report', 'Mark Credit Report Inactive');
            case ReportEntityClass.EmploymentReport:
                return selectTitle('Override Employment Report', 'Mark Employment Report Inactive');
            case ReportEntityClass.RiskReport:
                return selectTitle('Override Risk Report', 'Mark Risk Report Inactive');
            case ReportEntityClass.BankVerificationReport:
                return selectTitle(
                    'Override Bank Verification Report',
                    'Mark Bank Verification Report Inactive',
                    'Reactivate Bank Verification Report',
                );
            default:
                return selectTitle();
        }
    };

    const value: UnderwritingReportsContextModel = {
        openReportModal,
        setOpenReportModal,
        bankruptcyReportState,
        loadBankruptcyReportData,
        creditReportState,
        loadCreditReportData,
        employmentReportState,
        loadEmploymentReportData,
        riskReportState,
        loadRiskReportData,
        fraudReportState,
        loadFraudReportData,
        skiptraceReportState,
        loadSkiptraceReportData,
        bankReportState,
        loadBankReportData,
        payrollReportState,
        loadPayrollReportData,
        onSubmitReportAction,
        getActionReportTitle,
    };

    return (
        <UnderwritingReportsContext.Provider value={value}>{children}</UnderwritingReportsContext.Provider>
    );
};

export function useUnderwritingReports() {
    const context = useContext(UnderwritingReportsContext);
    if (!context) {
        throw new Error('useUnderwritingReports must be used within UnderwritingReportsProvider');
    }
    return context;
}
