import {
    CreditReportModel,
    EmploymentReportModel,
    FraudReportModel,
    RiskReportModel,
    SkiptraceReportModel,
} from '@/interfaces';

export enum ReportsTabType {
    Credit = 'Credit',
    Risk = 'Risk',
    Fraud = 'Fraud',
    Employment = 'Employment',
    Bankruptcy = 'Bankruptcy',
    Skiptrace = 'Skiptrace',
    Digital_Identity = 'Digital Identity',
    Bank = 'Bank',
    Payroll = 'Payroll',
}

export enum ReportEntityClass {
    BankruptcyReport = 'BankruptcyReport',
    CreditReport = 'CreditReport',
    EmploymentReport = 'EmploymentReport',
    RiskReport = 'RiskReport',
    FraudReport = 'FraudReport',
    BankVerificationReport = 'BankVerificationReport',
}

export type ReportActionType = 'override' | 'flag' | 'reactivate';

interface BaseReportActionProps {
    onClose: () => void;
}

export interface ActionReportProps extends BaseReportActionProps {
    id: number;
    action: ReportActionType;
    entityClass: ReportEntityClass;
}

export interface CreditReportPreviewProps {
    credit: CreditReportModel;
}

export interface RiskReportPreviewProps extends BaseReportActionProps {
    report: RiskReportModel;
}

export interface FraudReportPreviewProps extends BaseReportActionProps {
    fraud: FraudReportModel;
}

export interface SkiptraceReportPreviewProps {
    skiptrace: SkiptraceReportModel;
}

export interface BankReportPreviewProps {
    gid: number;
    employeeId: number;
    applications: number[];
}

export interface PullReportProps extends BaseReportActionProps {
    report: ReportsTabType;
}

export interface EmploymentReportProps extends BaseReportActionProps {
    data: EmploymentReportModel;
}

export type UnderwritingReportModalProps =
    | {type: UnderwritingReportModal.Action_Report; props: ActionReportProps}
    | {type: UnderwritingReportModal.Credit_Report_Preview; props: CreditReportPreviewProps}
    | {type: UnderwritingReportModal.Risk_Report_Preview; props: RiskReportPreviewProps}
    | {type: UnderwritingReportModal.Fraud_Report_Preview; props: FraudReportPreviewProps}
    | {type: UnderwritingReportModal.Skiptrace_Report_Preview; props: SkiptraceReportPreviewProps}
    | {type: UnderwritingReportModal.Bank_Report_Preview; props: BankReportPreviewProps}
    | {type: UnderwritingReportModal.Pull_Report; props: PullReportProps}
    | {type: UnderwritingReportModal.Employment_Report_Preview; props: EmploymentReportProps};

export enum UnderwritingReportModal {
    Action_Report = 'Action_Report',
    Credit_Report_Preview = 'Credit_Report_Preview',
    Risk_Report_Preview = 'Risk_Report_Preview',
    Fraud_Report_Preview = 'Fraud_Report_Preview',
    Skiptrace_Report_Preview = 'Skiptrace_Report_Preview',
    Bank_Report_Preview = 'Bank_Report_Preview',
    Pull_Report = 'Pull_Report',
    Employment_Report_Preview = 'Employment_Report_Preview',
}
