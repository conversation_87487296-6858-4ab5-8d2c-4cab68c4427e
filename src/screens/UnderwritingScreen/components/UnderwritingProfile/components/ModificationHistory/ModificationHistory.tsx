import React, {useEffect} from 'react';
import {ProfileItem} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {useUnderwritingModificationHistory} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ModificationHistoryDetails} from './components';
import {UnderwritingProfileItemModel} from '@/models';

interface ModificationHistoryProps {
    item: UnderwritingProfileItemModel;
}

export const ModificationHistory = ({item}: ModificationHistoryProps) => {
    const {
        state: {data, loading, error},
        loadData,
    } = useUnderwritingModificationHistory();

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={<ModificationHistoryDetails />}
        />
    );
};
