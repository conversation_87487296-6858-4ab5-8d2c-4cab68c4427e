import React, {createContext, useContext, useState} from 'react';
import {UnderwritingModificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface UnderwritingModificationHistoryContextModel {
    state: DataStateInterface<UnderwritingModificationHistoryModel[]>;
    loadData: () => Promise<void>;
}

const UnderwritingModificationHistoryContext = createContext<
    UnderwritingModificationHistoryContextModel | undefined
>(undefined);

interface UnderwritingModificationHistoryProviderProps {
    children: React.ReactNode;
}

export const UnderwritingModificationHistoryProvider: React.FC<
    UnderwritingModificationHistoryProviderProps
> = ({children}) => {
    const {profile} = useUnderwritingProfile();
    const [state, setState] = useState(getDefaultState<UnderwritingModificationHistoryModel[]>());

    const loadData = async () => {
        const params:string[] = [];
        if(profile.user_id){
            params.push(`user_id=${profile.user_id}`);
        }

        if(profile.employee_id){
            params.push(`employee_id=${profile.employee_id}`);
        }

        const url = `/api/secured/underwriting/modification-history?${params.join('&')}`;

        setState(getLoadingState(state));

        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    const value: UnderwritingModificationHistoryContextModel = {
        state,
        loadData,
    };

    return (
        <UnderwritingModificationHistoryContext.Provider value={value}>
            {children}
        </UnderwritingModificationHistoryContext.Provider>
    );
};

export function useUnderwritingModificationHistory() {
    const context = useContext(UnderwritingModificationHistoryContext);
    if (!context) {
        throw new Error(
            'useUnderwritingModificationHistory must be used within UnderwritingModificationHistoryProvider',
        );
    }
    return context;
}
