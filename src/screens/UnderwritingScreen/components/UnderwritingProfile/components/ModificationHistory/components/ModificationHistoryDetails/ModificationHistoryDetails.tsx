import React from 'react';
import {UnderwritingModificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingModificationHistory} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ModificationHistoryTableColumns} from './tables/ModificationHistoryTableColumns';
import {ColumnDef} from '@tanstack/react-table';
import {TableView} from '@/views';
import Box from '@mui/material/Box';

export const ModificationHistoryDetails = () => {
    const {state} = useUnderwritingModificationHistory();

    return (
        <Box px={5} pb={1.5} data-testid='uw-modification-history-details'>
            <TableView<UnderwritingModificationHistoryModel>
                loading={state.loading}
                error={state.error}
                data={state.data}
                sortingColumns={[{id: 'archive_time', desc: true}]}
                columns={
                    ModificationHistoryTableColumns as ColumnDef<
                        UnderwritingModificationHistoryModel,
                        unknown
                    >[]
                }
            />
        </Box>
    );
};
