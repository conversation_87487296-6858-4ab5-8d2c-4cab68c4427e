import React from 'react';
import {Completable} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {UnderwritingModificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasMaskedSSN, KasPhoneTooltip} from '@/components';

interface ModifiedValueProps {
    data: UnderwritingModificationHistoryModel;
    value: string;
}

export const ModifiedValue = ({data, value}: ModifiedValueProps) => {
    const {showMessage} = useSnackbar();
    const isSSN = data.field.toLowerCase() === 'ssn' && !!value;
    const isPhone = data.field.toLowerCase() === 'phone' && !!value;

    const loadUnmaskedSSN = async () => {
        const url = `/api/secured/underwriting/employee-profile/${data.entity_id}/ssn`;
        const response: Completable<string> = await apiRequest(url);

        if (!response.value) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        return response;
    };

    if (isPhone) {
        return <KasPhoneTooltip phone={value} withCopy />;
    }

    return isSSN ? <KasMaskedSSN ssn={value} onLoanSSN={loadUnmaskedSSN} /> : value;
};
