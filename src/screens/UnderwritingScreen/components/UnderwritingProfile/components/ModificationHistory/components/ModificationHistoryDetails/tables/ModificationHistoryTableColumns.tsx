import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingModificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {ModifiedValue} from './../components';

const columnHelper = createColumnHelper<UnderwritingModificationHistoryModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingModificationHistoryModel>;

export const ModificationHistoryTableColumns = [
    _defaultInfoColumn('field', 'Field'),
    columnHelper.accessor('old_value', {
        id: 'old_value',
        header: 'Old Value',
        cell: (props) => <ModifiedValue data={props.row.original} value={props.getValue()} />,
    }),
    columnHelper.accessor('value', {
        id: 'value',
        header: 'Value',
        cell: (props) => <ModifiedValue data={props.row.original} value={props.getValue()} />,
    }),
    _defaultInfoColumn('archive_time', 'Date'),
];
