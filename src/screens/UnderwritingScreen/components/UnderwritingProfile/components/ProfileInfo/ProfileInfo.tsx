import './styles.scss';

import React, {useEffect} from 'react';
import {Paper} from '@mui/material';
import {KasLoadingBackDrop, KasLoadingErro<PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {
    ProfileInfoContent,
    ProfileInfoLoading,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/ProfileInfo/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {useEventSubscription} from '@/utils/EventUtils';
import {EventName} from '@/lib/slices/eventSlice';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';

export const ProfileInfo = () => {
    const {activeProfile} = useAppSelector(selectUnderwritingState);
    const {profile, loadUserProfileData, userProfileState} = useUnderwritingProfile();
    const isProfileActive = activeProfile?.uid === profile.uid;

    const updateUserProfile = () => {
        if (userProfileState.data || userProfileState.error) {
            loadUserProfileData().then();
        }
    };

    useEffect(() => {
        loadUserProfileData().then();
    }, []);

    useEventSubscription(isProfileActive, EventName.UpdateEmployeeEmails, updateUserProfile, [
        userProfileState,
    ]);

    return (
        <Paper className='kas-underwriting-profile-info' elevation={0}>
            <KasSwitch>
                <KasSwitchWhen condition={userProfileState.loading && !userProfileState.data}>
                    <ProfileInfoLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!userProfileState.error}>
                    <KasLoadingError error={userProfileState.error} onTryAgain={loadUserProfileData} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={true}>
                    {userProfileState.loading && <KasLoadingBackDrop />}
                    <ProfileInfoContent />
                </KasSwitchWhen>
            </KasSwitch>
        </Paper>
    );
};
