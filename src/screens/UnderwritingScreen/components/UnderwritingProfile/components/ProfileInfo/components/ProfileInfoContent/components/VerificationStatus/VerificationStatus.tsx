import React, {useMemo, useState} from 'react';
import {KasLink, KasLoading} from '@/components';
import {Stack, useTheme} from '@mui/material';
import {Email, Phone} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {capitalizeWords} from '@/utils/TextUtils';

interface VerificationStatusProps {
    gid: number;
    pending: boolean;
    type: 'email' | 'phone';
    verifiedDate: string | null;
}

export const VerificationStatus = ({gid, pending, type, verifiedDate}: VerificationStatusProps) => {
    const {setOpenActionModal, loadUserProfileData} = useUnderwritingProfile();
    const {palette} = useTheme();
    const [loading, setLoading] = useState(false);
    const titleAccess: string = `Re Send ${capitalizeWords(type)}`;

    const renderResendIcon = useMemo(() => {
        const IconComponent = type === 'email' ? Email : Phone;

        return <IconComponent color='primary' titleAccess={titleAccess} />;
    }, [type]);

    const onVerify = async () => {
        setOpenActionModal({
            type: UnderwritingProfileAction.VERIFICATION_STATUS,
            props: {
                gid,
                type,
                verifiedDate,
            },
        });
    };

    const onResendVerify = async () => {
        setLoading(true);

        const url = `/api/secured/underwriting/actions/verification/${gid}/${type}/resend`;
        const response = await apiRequest(url);

        if (response.value) {
            await loadUserProfileData();
        }
        setLoading(false);
    };

    return (
        <Stack direction='row' useFlexGap flexWrap='wrap' spacing={1}>
            <KasLink style={{...(!verifiedDate && {color: palette.error.main})}} onClick={onVerify}>
                {verifiedDate || 'Not Verified'}
            </KasLink>
            {!verifiedDate && pending && (
                <ActionCell
                    disabled={loading}
                    Icon={loading ? <KasLoading size={18} /> : renderResendIcon}
                    onClick={onResendVerify}
                />
            )}
        </Stack>
    );
};
