import './styles.scss';

import React from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {UnderwritingUserProfileSocialModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasContactRestricted, KasInfo, KasSocialImage} from '@/components';
import Box from '@mui/material/Box';

interface ProfileSocialsProps {
    socials: UnderwritingUserProfileSocialModel[];
    restricted: string | null;
}

export const ProfileSocials = ({socials, restricted}: ProfileSocialsProps) => {
    return (
        <div className='kas-underwriting-profile-socials'>
            <Box mb={1}>
                <Typography variant='subtitle1'>
                    <KasContactRestricted restricted={restricted}>Socials</KasContactRestricted>
                </Typography>
            </Box>
            <Stack
                flexDirection='row'
                alignItems='center'
                columnGap={2}
                rowGap={1}
                useFlexGap
                flexWrap='wrap'>
                {socials.map((item) => (
                    <Paper key={item.type} data-testid={`uw-profile-social-${item.type}-info`}>
                        <Stack flexDirection='row' alignItems='center' py={1} px={2}>
                            <KasSocialImage type={item.type} />
                            <Box pl={1}>
                                <KasInfo label='Name:' isInline>
                                    {item.name}
                                </KasInfo>
                                <KasInfo label='Email:' isInline>
                                    {item.email}
                                </KasInfo>
                                {item.phone && (
                                    <KasInfo label='Phone:' isInline>
                                        {item.phone}
                                    </KasInfo>
                                )}
                                {item.dob && (
                                    <KasInfo label='DOB:' isInline>
                                        {item.dob}
                                    </KasInfo>
                                )}
                                <KasInfo label='First Login:' isInline>
                                    {item.created_date}
                                </KasInfo>
                                <KasInfo label='Last Updated:' isInline>
                                    {item.last_changed}
                                </KasInfo>
                            </Box>
                        </Stack>
                    </Paper>
                ))}
            </Stack>
        </div>
    );
};
