import React from 'react';
import {Typography} from '@mui/material';
import {underwritingFullName, underwritingId} from '@/utils/UnderwritingUtils';
import {KasCopyText} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const ProfileInfoTitle = () => {
    const {profile} = useUnderwritingProfile();

    return (
        <Typography variant='h3' py={0.5} data-testid='uw-profile-info-title'>
            <KasCopyText textToCopy={`${underwritingFullName(profile)} [${underwritingId(profile)}]`}>
                <span style={{wordWrap: 'break-word', wordBreak: 'break-word'}}>
                    {underwritingFullName(profile)}
                </span>
            </KasCopyText>
        </Typography>
    );
};
