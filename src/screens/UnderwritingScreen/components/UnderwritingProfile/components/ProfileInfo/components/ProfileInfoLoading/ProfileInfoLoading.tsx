'use client';
import './styles.scss';

import React from 'react';
import {Skeleton, Stack} from '@mui/material';
import {KasInfoPreviewLoading} from '@/components';

export const ProfileInfoLoading = () => (
    <div className='kas-underwriting-profile-info-loading'>
        <div className='kas-underwriting-profile-info-loading__head'>
            <Skeleton variant='rounded' animation='wave' width={300} height={32} />
        </div>
        <Stack direction='row' spacing={4}>
            <KasInfoPreviewLoading />
            <KasInfoPreviewLoading />
            <KasInfoPreviewLoading />
            <KasInfoPreviewLoading />
            <KasInfoPreviewLoading />
        </Stack>
    </div>
);
