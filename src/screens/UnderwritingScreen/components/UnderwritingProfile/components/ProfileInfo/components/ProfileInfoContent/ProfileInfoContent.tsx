import './styles.scss';

import React, {useMemo} from 'react';
import {Chip, CircularProgress, Stack, useTheme} from '@mui/material';
import {
    underwritingApplicationStatus,
    underwritingBackupAchStatusColor,
    underwritingFirstName,
    underwritingIsUserAndEmployeeNotMatch,
    underwritingLastName,
    underwritingProfileEmail,
    underwritingProfilePhone,
    underwritingStatusColor,
} from '@/utils/UnderwritingUtils';
import {
    KasContactRestricted,
    KasCopyText,
    KasInfo,
    KasLoadingStatusIcon,
    KasPhoneTooltip,
} from '@/components';
import {UnderwritingUserActions, useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileInfoTitle, ProfileSocials, VerificationStatus} from './components';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import {Refresh} from '@mui/icons-material';

export const ProfileInfoContent = () => {
    const {
        profile,
        userProfileState: {data, loading},
        employeeProfileState,
        loadUserProfileData,
        loadEmployeeProfileData,
        checkIsContactRestricted,
    } = useUnderwritingProfile();
    const {palette} = useTheme();

    const renderPhone = useMemo(() => {
        const phone = data && underwritingProfilePhone(data);

        if (!phone) {
            return null;
        }

        return (
            <KasPhoneTooltip
                phone={phone}
                caller={data.phone_cnam}
                addedDate={data?.phone_added}
                withCopy
                restricted={checkIsContactRestricted('PHONE')}
            />
        );
    }, [data, profile]);

    return (
        <div className='kas-underwriting-profile-info-content'>
            <Stack direction='row' justifyContent='space-between' mb={3} mt={-0.5}>
                <ProfileInfoTitle />
                <Stack direction='row' spacing={1} pl={1} alignItems='flex-start'>
                    <UnderwritingUserActions
                        uid={profile.uid}
                        userId={profile.user_id}
                        employmentVerified={!!data?.employment_verified}
                        locked={!!data?.locked}
                        onSuccess={loadUserProfileData}
                    />
                    {employeeProfileState.data?.backup_ach_status && (
                        <Chip
                            data-testid='uw-profile-info-backup-ach-status'
                            label={`Backup ACH: ${employeeProfileState.data.backup_ach_status}`}
                            variant='outlined'
                            color={underwritingBackupAchStatusColor(
                                employeeProfileState.data.backup_ach_status,
                            )}
                        />
                    )}
                    <Chip
                        data-testid='uw-profile-info-app-status'
                        label={underwritingApplicationStatus(profile)}
                        variant='outlined'
                        icon={employeeProfileState.loading ? <CircularProgress size={16} /> : undefined}
                        color={underwritingStatusColor(profile)}
                    />
                    {underwritingIsUserAndEmployeeNotMatch(profile) && (
                        <Chip
                            label='User and Employer Data doesn’t match'
                            variant='outlined'
                            color='warning'
                        />
                    )}
                    {loading ? (
                        <KasLoadingStatusIcon loading={loading} />
                    ) : (
                        <IconButton
                            title={'Refresh'}
                            disabled={loading}
                            onClick={() => {
                                loadUserProfileData();
                                loadEmployeeProfileData();
                            }}
                            data-testid='uw-profile-info-refresh'>
                            <Refresh />
                        </IconButton>
                    )}
                </Stack>
            </Stack>
            <Stack
                direction='row'
                columnGap={4}
                rowGap={2}
                useFlexGap
                flexWrap='wrap'
                data-testid='uw-profile-info'>
                <KasInfo label='User ID'>
                    {profile.user_id ? <KasCopyText>{profile.user_id}</KasCopyText> : profile.user_id}
                </KasInfo>
                <KasInfo label='First Name'>
                    <KasCopyText>{data?.first_name || underwritingFirstName(profile)}</KasCopyText>
                </KasInfo>
                <KasInfo label='Last Name'>
                    <KasCopyText>{data?.last_name || underwritingLastName(profile)}</KasCopyText>
                </KasInfo>
                <KasInfo label='Last Login'>{data?.last_login_time}</KasInfo>
                <KasInfo label='Employment verified'>
                    {data?.employment_verified_date ? (
                        data?.employment_verified_date
                    ) : (
                        <span style={{color: palette.error.main}}>Not Verified</span>
                    )}
                </KasInfo>
                <KasInfo label='Phone'>{renderPhone}</KasInfo>
                <KasInfo label='Phone verified'>
                    {data ? (
                        <VerificationStatus
                            gid={data?.gid}
                            pending={!!data?.phone_pending}
                            type='phone'
                            verifiedDate={data?.phone_verified_date}
                        />
                    ) : null}
                </KasInfo>
                <KasInfo label='Email'>
                    {data && underwritingProfileEmail(data) ? (
                        <KasCopyText textToCopy={underwritingProfileEmail(data)}>
                            <KasContactRestricted restricted={checkIsContactRestricted('EMAIL')}>
                                {underwritingProfileEmail(data)}
                            </KasContactRestricted>
                        </KasCopyText>
                    ) : null}
                </KasInfo>
                <KasInfo label='Email verified'>
                    {data ? (
                        <VerificationStatus
                            gid={data?.gid}
                            pending={!!data?.email_pending}
                            type='email'
                            verifiedDate={data?.email_verified_date}
                        />
                    ) : null}
                </KasInfo>
                {data?.build_version ? <KasInfo label='Build version'>{data?.build_version}</KasInfo> : null}
            </Stack>
            {data && data?.socials.length > 0 && (
                <Box width='100%' py={2}>
                    <ProfileSocials socials={data?.socials} restricted={checkIsContactRestricted('SOCIAL')} />
                </Box>
            )}
        </div>
    );
};
