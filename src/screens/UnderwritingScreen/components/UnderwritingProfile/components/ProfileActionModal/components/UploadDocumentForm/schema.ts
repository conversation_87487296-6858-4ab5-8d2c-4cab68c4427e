import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    associateDocument: Yup.string().required(DEFAULT_VALIDATION_MSG),
    loanId: Yup.string().when('associateDocument', (associateDocument: string[], schema) => {
        if (associateDocument.includes('LOAN')) {
            return schema.required(DEFAULT_VALIDATION_MSG);
        }
        return schema;
    }),
    queueId: Yup.string().when('associateDocument', (associateDocument: string[], schema) => {
        if (associateDocument.includes('QUEUE')) {
            return schema.required(DEFAULT_VALIDATION_MSG);
        }
        return schema;
    }),
    files: Yup.array().of(Yup.mixed().required('A file is required')).min(1, DEFAULT_VALIDATION_MSG),
});

export type UploadDocumentValues = Yup.Asserts<typeof validationSchema>;
