import './styles.scss';

import React, {ChangeEvent, useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {LetterPreviewValues, validationSchema} from './schema';
import {Checkbox, FormControlLabel, Grid2, Typography} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {RecipientDTO} from '@/models';
import {KasAutocompleteField, KasLoadingBackDrop, KasModalFooter} from '@/components';
import {SelectModel} from '@/interfaces';
import {EmailPreview} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingLoanLetterPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {usePrint} from '@/hooks/usePrint';

export const LetterPreviewForm = () => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal, letterLoanId} = useUnderwritingProfile();
    const [types, setTypes] = useState<UnderwritingLoanLetterPreviewModel[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedAddresses, setSelectedAddresses] = useState<string[]>([]);
    const {handlePrint, handleLetterPrintContent} = usePrint();

    const typeOptions: SelectModel<string>[] = useMemo(() => {
        return types.map(({letter_type}) => ({
            id: letter_type,
            label: letter_type,
            value: letter_type,
        }));
    }, [types]);

    const handlePrintContent = (value: string) => {
        handleLetterPrintContent(value);
    };

    const onSubmit = async (values: LetterPreviewValues) => {
        const url = '/api/secured/underwriting/actions/letter-information';
        const recipients = selectedType?.recipients
            ? selectedType.recipients.filter(
                  (item) => item.address_1 && selectedAddresses.includes(item.address_1),
              )
            : [];
        const body = JSON.stringify({
            ...selectedType,
            recipients,
        });
        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            type: '',
        },
        onSubmit,
        validationSchema,
    });

    const loadLetterSettings = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/underwriting/actions/letter-information/${letterLoanId}`;
            const response = await apiRequest(url);

            setTypes(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    const selectedType = useMemo(() => {
        const type = formik.values.type;

        if (type) {
            const letterType = types.find(({letter_type}) => letter_type === type);

            setSelectedAddresses(
                letterType?.recipients ? letterType.recipients.map((item) => item.address_1 || '') : [],
            );
            return types.find(({letter_type}) => letter_type === type);
        }

        return undefined;
    }, [formik.values]);

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedAddresses.includes(value)) {
            setSelectedAddresses(selectedAddresses.filter((email) => email !== value));
        } else {
            setSelectedAddresses([...selectedAddresses, value]);
        }
    };

    const getRecipientAddress = (value: RecipientDTO): string => {
        return `${value.address_1} ${value.city}, ${value.zip}`;
    };

    useEffect(loadLetterSettings, [letterLoanId]);

    return (
        <form className='kas-underwriting-actions-letter-preview-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <KasAutocompleteField name='type' label='Type' options={typeOptions} formik={formik} />
                </Grid2>
                {selectedType && (
                    <Grid2 size={12}>
                        <Typography variant='subtitle1'>Recipients</Typography>
                        {selectedType.recipients.map((item, index) => (
                            <div key={index}>
                                <FormControlLabel
                                    label={getRecipientAddress(item)}
                                    control={
                                        <Checkbox
                                            size='small'
                                            value={item.address_1}
                                            checked={
                                                !!item.address_1 && selectedAddresses.includes(item.address_1)
                                            }
                                            onChange={handleChangeRecipients}
                                            inputProps={{'aria-label': 'controlled'}}
                                        />
                                    }
                                />
                            </div>
                        ))}
                        <Typography variant='subtitle1'>Preview</Typography>
                        <EmailPreview
                            url='/api/secured/underwriting/actions/letter-information?preview=true'
                            payload={JSON.stringify(selectedType)}
                            isLetter={true}
                            onContent={handlePrintContent}
                        />
                    </Grid2>
                )}
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Send Letter'
                        customDisable={!formik.isValid || submittingAction || selectedAddresses.length === 0}
                        disabled={!formik.isValid || submittingAction || selectedAddresses.length === 0}
                        onCancel={() => setOpenActionModal(null)}
                        onCustomClick={handlePrint}
                        customButtonText='Print'
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
