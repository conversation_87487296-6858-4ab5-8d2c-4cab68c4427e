import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {RequestLetterValues, validationSchema} from './schema';
import {FormControl, FormControlLabel, Grid2, Radio, RadioGroup, TextField, Typography} from '@mui/material';
import {KasAutocompleteField, KasDatePickerFormField, KasLoadingBackDrop, KasModalFooter} from '@/components';
import {LookupDTO} from '@/models';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {SelectModel} from '@/interfaces';
import dayjs from 'dayjs';
import {
    ProfileActionModalFormProps,
    UnderwritingProfileAction,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const RequestLetterForm = ({entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal, setLetterLoanId} = useUnderwritingProfile();
    const [openFileEver, setOpenFileEver] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loans, setLoans] = useState<LookupDTO[]>([]);

    const onSubmit = async (values: RequestLetterValues) => {
        const url = '/api/secured/underwriting/actions/request-letter';
        const formData = new FormData();

        if (values.files) {
            Object.values(values.files).forEach((file) => {
                formData.append('files', file as Blob);
            });
        }

        formData.append('entity_id', values.loanId);
        formData.append('entity_class', 'Loan');
        formData.append('comment', values.comment);
        formData.append('effective_date', dayjs(values.requestedDate).format('YYYYMMDD'));
        formData.append('request_letter_type', values.type);

        setLetterLoanId(values.loanId);

        const response = await onSubmitAction(url, formData, 'post');

        if (response.value) {
            setOpenActionModal({type: UnderwritingProfileAction.LETTER_PREVIEW});
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            loanId: '',
            requestedDate: '',
            type: 'REQUEST_DV',
            files: [],
        },
        onSubmit,
        validationSchema,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        formik.setFieldValue('files', event.target.files);
    };

    const loanOptions: SelectModel<string>[] = useMemo(() => {
        return loans.map(({id, text}) => ({
            id,
            label: text,
            value: id,
        }));
    }, [loans]);

    const loadLoans = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/ui/lookup/employee-loan/${entityId}?direction=balance`;
            const response = await apiRequest(url);

            setLoans(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    useEffect(loadLoans, []);

    return (
        <form className='kas-underwriting-actions-request-letter-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={8}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submittingAction}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='requestedDate'
                        label='Requested Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={8}>
                    <Typography variant='body1'>Request Letter Type</Typography>
                    <FormControl disabled={submittingAction}>
                        <RadioGroup
                            name='type'
                            defaultValue='REQUEST_DV'
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <FormControlLabel
                                value='REQUEST_DV'
                                control={<Radio size='small' />}
                                label='Debt Validation'
                            />
                            <FormControlLabel
                                value='REQUEST_GW'
                                control={<Radio size='small' />}
                                label='Goodwill'
                            />
                        </RadioGroup>
                    </FormControl>
                </Grid2>
                <Grid2 size={4}>
                    <KasAutocompleteField
                        name='loanId'
                        label='Loan ID'
                        options={loanOptions}
                        formik={formik}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        type='file'
                        name='files'
                        variant='outlined'
                        onBlur={formik.handleBlur}
                        onFocus={() => {
                            setOpenFileEver(!!formik.touched.files);
                        }}
                        onChange={handleFileChange}
                        slotProps={{
                            htmlInput: {accept: '.jpg,.png,.pdf', multiple: true},
                        }}
                        error={openFileEver && !!formik.errors.files}
                        helperText={openFileEver && formik.errors.files}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
