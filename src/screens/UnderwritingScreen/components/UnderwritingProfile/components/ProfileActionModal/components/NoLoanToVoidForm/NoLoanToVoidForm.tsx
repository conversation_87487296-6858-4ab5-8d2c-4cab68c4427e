import React from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const NoLoanToVoidForm = () => {
    const {setOpenActionModal} = useUnderwritingProfile();

    const onClose = () => {
        setOpenActionModal(null);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {},
        onSubmit: onClose,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='error'>User does not have an active loan to void</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter submitText='OK' onCancel={onClose} />
                </Grid2>
            </Grid2>
        </form>
    );
};
