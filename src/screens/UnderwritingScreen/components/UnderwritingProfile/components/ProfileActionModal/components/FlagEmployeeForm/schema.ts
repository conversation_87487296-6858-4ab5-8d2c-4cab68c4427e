import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    actionType: Yup.string().required(DEFAULT_VALIDATION_MSG),
    effectiveDate: Yup.string().when('actionType', (actionType: string[], schema) => {
        if (actionType.includes('CUSTOM')) {
            return schema.required(DEFAULT_VALIDATION_MSG);
        }
        return schema;
    }),
});

export type FlagEmployeeValues = Yup.Asserts<typeof validationSchema>;
