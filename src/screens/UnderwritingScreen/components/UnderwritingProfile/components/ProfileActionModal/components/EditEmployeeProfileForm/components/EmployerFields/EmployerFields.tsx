import React, {useEffect, useMemo, useState} from 'react';
import {FormikValues} from 'formik';
import {FormControl, Grid2, InputLabel, Select, TextField} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasAsyncSearchAutocompleteField, KasSwitch, KasSwitchWhen} from '@/components';
import {SelectModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {PayrollDTO} from '@/models/payrollDTO';

interface EmployerFieldsProps {
    formik: FormikValues;
}

export const EmployerFields = ({formik}: EmployerFieldsProps) => {
    const {
        employeeProfileState: {data},
    } = useUnderwritingProfile();
    const [selectedEmployer, setSelectedEmployer] = useState<SelectModel<string> | null>(null);
    const [payroll, setPayroll] = useState<PayrollDTO>();

    const searchUrl = useMemo(() => {
        if (data?.workflow) {
            return `/api/secured/ui/lookup/employer/name?length=7&flow=${data.workflow.toLowerCase()}&name=`;
        } else {
            return '/api/secured/ui/lookup/employer/name?length=7&name=';
        }
    }, [data?.workflow]);

    const onSelectEmployerHandler = async (value: SelectModel<string> | null) => {
        setPayroll(undefined);
        await formik.setFieldValue('payrollGroupId', '');
        setSelectedEmployer(value);
        await formik.setFieldValue('employerId', value?.value || '');
    };

    const getPayroll = async () => {
        const url = `/api/secured/ui/employer/${selectedEmployer?.id}/payroll`;
        const response = await apiRequest(url);

        setPayroll(response.value ? response.value : undefined);
        if (response.value?.groups) {
            const groupsKey = Object.keys(response.value.groups);

            if (!!groupsKey.length) {
                await formik.setFieldValue('payrollGroupId', groupsKey[0]);
            }
        }
    };

    const renderPayrollGroups = useMemo(() => {
        if (payroll?.groups) {
            const groupsKey = Object.keys(payroll.groups);

            return groupsKey.map((key) => (
                <MenuItem key={key} value={key}>
                    {payroll.groups[key].frequency}
                </MenuItem>
            ));
        }
        return null;
    }, [payroll]);

    useEffect(() => {
        if (selectedEmployer) {
            getPayroll().then();
        }
    }, [selectedEmployer]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!!data?.employer_locked}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        name='employerName'
                        size='small'
                        disabled={true}
                        defaultValue={data?.employer_name}
                        label='Employer'
                        variant='outlined'
                    />
                </Grid2>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!data?.employer_locked}>
                <Grid2 size={8}>
                    <KasAsyncSearchAutocompleteField
                        value={selectedEmployer}
                        label='Select Employer'
                        searchUrl={searchUrl}
                        onSelect={onSelectEmployerHandler}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <FormControl fullWidth disabled={!selectedEmployer} size='small' variant='outlined'>
                        <InputLabel id='select-payroll-label'>Payroll Group</InputLabel>
                        <Select
                            labelId='select-payroll-label'
                            id='select-payroll-field'
                            name='payrollGroupId'
                            label='Payroll Group'
                            value={formik.values.payrollGroupId}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            {renderPayrollGroups}
                        </Select>
                    </FormControl>
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
