import './styles.scss';

import React, {useEffect} from 'react';
import {useFormik} from 'formik';
import {CloseBankruptcyValues, validationSchema} from './schema';
import {FormControl, FormHelperText, Grid2, InputLabel, Select, TextField} from '@mui/material';
import dayjs from 'dayjs';
import MenuItem from '@mui/material/MenuItem';
import {KasDatePickerForm<PERSON>ield, KasModalFooter} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {
    ProfileActionModalFormEntityClass,
    ProfileActionModalFormProps,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';

interface CloseBankruptcyPayloadModel {
    entity_class: ProfileActionModalFormEntityClass;
    entity_id: number;
    effective_date?: string;
    code: string;
    comment: string;
}

export const CloseBankruptcyForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();

    const onSubmit = async (values: CloseBankruptcyValues) => {
        let url = '/api/secured/power/actions/flag-bankruptcy';
        const payload: CloseBankruptcyPayloadModel = {
            entity_class: entityClass,
            entity_id: entityId,
            code: values.code,
            comment: values.comment,
        };

        if (values.code !== 'CLEAR') {
            url = '/api/secured/underwriting/actions/close-bankruptcy';
            payload.effective_date = dayjs(values.effectiveDate).format('YYYYMMDD');
        }

        const body = JSON.stringify(payload);

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            code: '',
            effectiveDate: '',
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (formik.values.code === 'CLEAR') {
            const today = dayjs().startOf('day').toDate();

            formik.setFieldValue('effectiveDate', today);
        }
    }, [formik.values.code]);

    return (
        <form className='kas-underwriting-actions-close-bankruptcy-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <FormControl
                        fullWidth
                        size='small'
                        variant='outlined'
                        error={formik.touched.code && !!formik.errors.code}>
                        <InputLabel id='select-label'>Resolution</InputLabel>
                        <Select
                            labelId='select-label'
                            id='select-field'
                            name='code'
                            label='Resolution'
                            value={formik.values.code}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <MenuItem value='DISMISS'>Dismissed</MenuItem>
                            <MenuItem value='DISCHARGE'>Discharged</MenuItem>
                            <MenuItem value='CLEAR'>Clear</MenuItem>
                        </Select>
                        {formik.touched.code && !!formik.errors.code && (
                            <FormHelperText error={true}>{formik.errors.code}</FormHelperText>
                        )}
                    </FormControl>
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='effectiveDate'
                        label='Bankruptcy Close Date'
                        disabled={submittingAction || formik.values.code === 'CLEAR'}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
