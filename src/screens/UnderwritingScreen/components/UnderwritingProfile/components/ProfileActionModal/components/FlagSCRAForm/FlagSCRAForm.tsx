import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {FlagSCRAValues, validationSchema} from './schema';
import {Grid2, TextField} from '@mui/material';
import dayjs from 'dayjs';
import {KasDate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KasModalFooter} from '@/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const FlagSCRAForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();

    const onSubmit = async (values: FlagSCRAValues) => {
        const url = '/api/secured/power/actions/flag-SCRA';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            effective_start_date: dayjs(values.effectiveStartDate).format('YYYYMMDD'),
            effective_end_date: dayjs(values.effectiveEndDate).format('YYYYMMDD'),
            comment: values.comment,
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            effectiveStartDate: '',
            effectiveEndDate: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-flag-SCRA-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='effectiveStartDate'
                        label='Effective Start Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='effectiveEndDate'
                        label='Effective End Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
