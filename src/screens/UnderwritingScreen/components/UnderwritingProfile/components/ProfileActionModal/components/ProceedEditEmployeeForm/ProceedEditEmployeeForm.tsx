import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Alert, Grid2} from '@mui/material';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {KasModalFooter} from '@/components';

export const ProceedEditEmployeeForm = () => {
    const {setOpenActionModal} = useUnderwritingProfile();

    const formik = useFormik({
        initialValues: {},
        onSubmit: () => setOpenActionModal({type: UnderwritingProfileAction.EDIT_EMPLOYEE_PROFILE}),
    });

    return (
        <form className='kas-underwriting-actions-proceed-edit-employee-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='info'>Proceed with EDIT EMPLOYEE?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter submitText='OK' onCancel={() => setOpenActionModal(null)} />
                </Grid2>
            </Grid2>
        </form>
    );
};
