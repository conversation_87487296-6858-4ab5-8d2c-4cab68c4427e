import React, {useState} from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';

export const UpdateDirectDepositForm = ({id}: {id: number}) => {
    const {showMessage} = useSnackbar();
    const {
        gid,
        setOpenActionModal,
        loadDirectDepositData,
        employeeProfileState,
        loadEmployeeProfileData,
        loadUserProfileData,
    } = useUnderwritingProfile();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const url = `/api/secured/underwriting/actions/update-direct-deposit/${id}?employeeId=${gid}`;

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'put', body: ''});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenActionModal(null);
            updateContent();
        }
        setSubmitting(false);
    };

    const updateContent = () => {
        loadDirectDepositData().then();
        loadUserProfileData().then();

        if (employeeProfileState.data) {
            loadEmployeeProfileData().then();
        }
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='info'>Are you sure you want to mark direct deposit as primary?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={submitting}
                        submitText='OK'
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
