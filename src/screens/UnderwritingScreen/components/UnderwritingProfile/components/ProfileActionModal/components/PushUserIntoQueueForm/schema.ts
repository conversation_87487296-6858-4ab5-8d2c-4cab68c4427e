import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    selectedQueues: Yup.array()
        .of(Yup.string())
        .min(1, DEFAULT_VALIDATION_MSG)
        .required(DEFAULT_VALIDATION_MSG),
});

export type PushUserIntoQueueValues = Yup.Asserts<typeof validationSchema>;
