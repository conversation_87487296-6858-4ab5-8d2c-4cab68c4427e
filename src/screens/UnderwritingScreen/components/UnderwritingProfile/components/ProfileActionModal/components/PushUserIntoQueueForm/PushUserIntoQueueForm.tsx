import './styles.scss';

import React, {Fragment, useEffect, useState} from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {
    Checkbox,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    Grid2,
    TextField,
    Typography,
} from '@mui/material';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {PushUserIntoQueueValues, validationSchema} from './schema';
import {UnderwritingEmployeeQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {LoadingQueue} from './components/LoadingQueue/LoadingQueue';
import {KasModalFooter} from '@/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const PushUserIntoQueueForm = ({entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [loading, setLoading] = useState(true);
    const [queues, setQueues] = useState<UnderwritingEmployeeQueueModel[]>([]);

    const onSubmit = async (values: PushUserIntoQueueValues) => {
        const url = `/api/secured/underwriting/applications/${entityId}/queue`;
        const body = JSON.stringify({
            comment: values.comment,
            summaries: values.selectedQueues.map((code) => {
                const gid = queues.find((item) => item.code === code)?.gid || '';

                return {gid, code};
            }),
        });

        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik<PushUserIntoQueueValues>({
        validateOnMount: true,
        initialValues: {
            comment: '',
            selectedQueues: [],
        },
        onSubmit,
        validationSchema,
    });

    const loadQueues = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/underwriting/applications/${entityId}/queue`;
            const response = await apiRequest(url);

            setQueues(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    useEffect(loadQueues, []);

    return (
        <form className='kas-underwriting-actions-send-cta-email-form' onSubmit={formik.handleSubmit}>
            {loading ? (
                <LoadingQueue />
            ) : (
                <Grid2 container spacing={2}>
                    <Grid2 size={4}>
                        <Typography variant='subtitle1'>Queue</Typography>
                    </Grid2>
                    <Grid2 size={4}>
                        <Typography variant='subtitle1'>Date Pushed</Typography>
                    </Grid2>
                    <Grid2 size={4}>
                        <Typography variant='subtitle1'>Date Verified</Typography>
                    </Grid2>
                    {queues.map((queue) => (
                        <Fragment key={queue.code}>
                            <Grid2 size={4} style={{paddingTop: 0}}>
                                <FormGroup onBlur={formik.handleBlur}>
                                    <FormControlLabel
                                        disabled={submittingAction}
                                        control={
                                            <Checkbox
                                                name='selectedQueues'
                                                value={queue.code}
                                                size='small'
                                                checked={formik.values.selectedQueues.includes(queue.code)}
                                                onChange={formik.handleChange}
                                            />
                                        }
                                        label={queue.code}
                                    />
                                </FormGroup>
                            </Grid2>
                            <Grid2 size={4} style={{paddingTop: 0}}>
                                {queue.queue_time}
                            </Grid2>
                            <Grid2 size={4} style={{paddingTop: 0}}>
                                {queue.verify_time}
                            </Grid2>
                        </Fragment>
                    ))}
                    <Grid2 size={12} style={{paddingTop: 0}}>
                        {formik.touched.selectedQueues && !!formik.errors.selectedQueues && (
                            <FormHelperText error={true}>{formik.errors.selectedQueues}</FormHelperText>
                        )}
                    </Grid2>
                    <Grid2 size={12}>
                        <TextField
                            fullWidth
                            size='small'
                            name='comment'
                            disabled={submittingAction}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            label='Comment'
                            variant='outlined'
                            error={!!formik.errors.comment && formik.touched.comment}
                            helperText={formik.touched.comment && formik.errors.comment}
                        />
                    </Grid2>
                    <Grid2 size={12}>
                        <KasModalFooter
                            submitText='Push'
                            disabled={!formik.isValid || submittingAction}
                            onCancel={() => setOpenActionModal(null)}
                        />
                    </Grid2>
                </Grid2>
            )}
        </form>
    );
};
