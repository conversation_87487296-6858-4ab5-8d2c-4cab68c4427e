import './styles.scss';

import React, {useState} from 'react';
import {useFormik} from 'formik';
import {UploadACHValues, validationSchema} from './schema';
import {Grid2, TextField} from '@mui/material';
import dayjs from 'dayjs';
import {KasDatePicker<PERSON><PERSON><PERSON>ield, KasModalFooter} from '@/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const UploadACHForm = ({entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();
    const [openFileEver, setOpenFileEver] = useState(false);

    const onSubmit = async (values: UploadACHValues) => {
        const date = dayjs(values.date).format('YYYYMMDD');
        const url = `/api/secured/underwriting/actions/upload-ACH?id=${entityId}&date=${date}&amount=${values.amount}`;
        const formData = new FormData();

        formData.append('file', values.file);

        await onSubmitAction(url, formData, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            amount: '',
            date: '',
            file: '',
        },
        onSubmit,
        validationSchema,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            formik.setFieldValue('file', selectedFile);
        }
    };

    return (
        <form className='kas-underwriting-actions-upload-ACH-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='date'
                        label='Enter Date Signed'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        type='file'
                        name='file'
                        variant='outlined'
                        onBlur={formik.handleBlur}
                        onFocus={() => {
                            setOpenFileEver(!!formik.touched.file);
                        }}
                        onChange={handleFileChange}
                        slotProps={{htmlInput: {accept: '.jpg,.png,.pdf'}}}
                        error={openFileEver && !!formik.errors.file}
                        helperText={openFileEver && formik.errors.file}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Upload Agreement'
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
