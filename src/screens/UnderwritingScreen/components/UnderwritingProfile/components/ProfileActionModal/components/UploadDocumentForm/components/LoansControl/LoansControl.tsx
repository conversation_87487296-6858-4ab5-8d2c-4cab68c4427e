import React, {useMemo} from 'react';
import {FormikValues} from 'formik';
import {FormControlLabel, Grid2, Radio} from '@mui/material';
import {KasAutocompleteField, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {DataStateInterface, SelectModel} from '@/interfaces';
import {LookupDTO} from '@/models';

interface LoansControlProps {
    formik: FormikValues;
    loans: DataStateInterface<LookupDTO[]>;
    onLoadLoans: () => void;
}

export const LoansControl = ({formik, loans, onLoadLoans}: LoansControlProps) => {
    const isNoLoans = useMemo(() => loans.loading || !loans.data?.length, [loans]);
    const loanOptions: SelectModel<string>[] = useMemo(() => {
        return (loans.data || []).map(({id, text}) => ({
            id,
            label: text,
            value: id,
        }));
    }, [loans.data]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!!loans.error}>
                <Grid2 size={12}>
                    <KasLoadingError error={loans.error} view='contained' onTryAgain={onLoadLoans} />
                </Grid2>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!loans.data || loans.loading}>
                <Grid2 size={6}>
                    <FormControlLabel
                        disabled={isNoLoans}
                        value='LOAN'
                        control={<Radio size='small' />}
                        label='Loan'
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasAutocompleteField
                        name='loanId'
                        label='Loan ID'
                        disabled={formik.values.associateDocument !== 'LOAN' || isNoLoans}
                        options={loanOptions}
                        formik={formik}
                    />
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
