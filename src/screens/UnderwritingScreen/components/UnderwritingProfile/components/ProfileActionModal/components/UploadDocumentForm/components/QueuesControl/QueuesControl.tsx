import React, {useMemo} from 'react';
import {FormikValues} from 'formik';
import {FormControlLabel, Grid2, Radio} from '@mui/material';
import {KasAutocompleteField, KasLoadingError, KasS<PERSON>, KasSwitchWhen} from '@/components';
import {DataStateInterface, SelectModel} from '@/interfaces';
import {LookupDTO} from '@/models';

interface QueuesControlProps {
    formik: FormikValues;
    queues: DataStateInterface<LookupDTO[]>;
    onLoadQueues: () => void;
}

export const QueuesControl = ({formik, queues, onLoadQueues}: QueuesControlProps) => {
    const isNoQueues = useMemo(() => queues.loading || !queues.data?.length, [queues]);
    const queueOptions: SelectModel<string>[] = useMemo(() => {
        return (queues.data || []).map(({id, text}) => ({
            id,
            label: text,
            value: id,
        }));
    }, [queues.data]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!!queues.error}>
                <Grid2 size={12}>
                    <KasLoadingError error={queues.error} view='contained' onTryAgain={onLoadQueues} />
                </Grid2>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!queues.data || queues.loading}>
                <Grid2 size={6}>
                    <FormControlLabel
                        value='QUEUE'
                        disabled={isNoQueues}
                        control={<Radio size='small' />}
                        label='Verification Queue'
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasAutocompleteField
                        name='queueId'
                        label='Queue'
                        disabled={formik.values.associateDocument !== 'QUEUE' || isNoQueues}
                        options={queueOptions}
                        formik={formik}
                    />
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
