import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {AddReferralValues, validationSchema} from './schema';
import {TextField, Grid2} from '@mui/material';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasModalFooter} from '@/components';

export const AddReferralForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();

    const onSubmit = async (values: AddReferralValues) => {
        const url = '/api/secured/underwriting/actions/add-referral';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            comment: values.comment,
            code: values.code,
        });
        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            code: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-add-referral-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='code'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Referral Code'
                        variant='outlined'
                        error={!!formik.errors.code && formik.touched.code}
                        helperText={formik.touched.code && formik.errors.code}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
            </Grid2>
            <Grid2 size={12}>
                <KasModalFooter
                    disabled={!formik.isValid || submittingAction}
                    onCancel={() => setOpenActionModal(null)}
                />
            </Grid2>
        </form>
    );
};
