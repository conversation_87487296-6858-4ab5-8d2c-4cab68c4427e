import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {BenefitfocusValues, validationSchema} from './schema';
import {TextField, Grid2} from '@mui/material';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasModalFooter} from '@/components';

export const BenefitfocusForm = ({entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();

    const onSubmit = async (values: BenefitfocusValues) => {
        const url = `/api/secured/power/actions/benefitfocus/${entityId}`;
        const body = JSON.stringify(values.comment);

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-benefitfocus-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
