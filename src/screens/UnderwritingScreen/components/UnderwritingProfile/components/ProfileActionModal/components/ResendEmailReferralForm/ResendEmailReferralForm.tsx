import React, {useState} from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';

export const ResendEmailReferralForm = ({id, force}: {id: number, force?: boolean}) => {
    const {showMessage} = useSnackbar();
    const {setOpenActionModal} = useUnderwritingProfile();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const url = `/api/secured/underwriting/actions/compensation/${id}/email${force ? '?force=1' : ''}`;

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'put', body: ''});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenActionModal(null);
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='info'>{force ? '[Power User] ' : ''}Are you sure you want to resend email?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={submitting}
                        submitText='OK'
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
