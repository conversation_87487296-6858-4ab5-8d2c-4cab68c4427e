import './styles.scss';

import React, {ChangeEvent, useEffect, useState} from 'react';
import {useFormik} from 'formik';
import {LetterPreviewValues, validationSchema} from './schema';
import {Checkbox, FormControlLabel, Grid2, TextField, Typography} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasLoadingBackDrop, KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {RestrictDTO} from '@/models/restrictDTO';
import {ProfileActionModalFormProps, UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const RestrictContactForm = ({entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [restrict, setRestrict] = useState<RestrictDTO>();
    const [loading, setLoading] = useState(true);
    const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

    const onSubmit = async (values: LetterPreviewValues) => {
        const url = `/api/secured/support/actions/restrict/${entityId}?entity=employee`;
        const permissions = restrict?.permissions ? Object.keys(restrict.permissions) : [];
        const body = JSON.stringify({
            comment: values.comment,
            copy_to_comments: values.copyToComments ? 1 : 0,
            restrictions: permissions.map((type) => ({
                type,
                restrict: selectedPermissions.includes(type),
            })),
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            copyToComments: false,
        },
        onSubmit,
        validationSchema,
    });

    const loadRestrict = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/support/employee/${entityId}/restrict`;
            const response = await apiRequest(url);

            setRestrict(response.value);

            if (response?.value?.permissions) {
                const permissions = response.value.permissions;

                setSelectedPermissions(Object.keys(permissions).filter((key) => permissions[key]));
            }

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedPermissions.includes(value)) {
            setSelectedPermissions(selectedPermissions.filter((item) => item !== value));
        } else {
            setSelectedPermissions([...selectedPermissions, value]);
        }
    };

    useEffect(loadRestrict, []);

    return (
        <form className='kas-underwriting-actions-restrict-contact-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                {restrict && (
                    <>
                        <Grid2 size={12}>
                            {restrict.last_updated_by && (
                                <Typography variant='subtitle1'>
                                    Last updated by {restrict.last_updated_by}
                                </Typography>
                            )}
                            {Object.keys(restrict.permissions).map((item: string) => (
                                <FormControlLabel
                                    key={item}
                                    label={item}
                                    control={
                                        <Checkbox
                                            size='small'
                                            value={item}
                                            checked={!!item && selectedPermissions.includes(item)}
                                            onChange={handleChangeRecipients}
                                            inputProps={{'aria-label': 'controlled'}}
                                        />
                                    }
                                />
                            ))}
                        </Grid2>
                        {!!restrict?.comments?.length && (
                            <Grid2 size={12}>
                                <Typography variant='subtitle1'>Comments:</Typography>
                                {restrict.comments.map((comment) => (
                                    <Typography variant='body1' key={comment.gid}>
                                        &#x25CF; {comment.timestamp}
                                        {comment.user_name ? ` [${comment.user_name}]` : ''}: {comment.text}
                                    </Typography>
                                ))}
                            </Grid2>
                        )}
                    </>
                )}
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                    <FormControlLabel
                        label='Copy to comments'
                        control={
                            <Checkbox
                                size='small'
                                name='copyToComments'
                                onChange={formik.handleChange}
                                checked={formik.values.copyToComments}
                                inputProps={{'aria-label': 'controlled'}}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                        customButtonText='Send Opt Out Email'
                        onCustomClick={() => {setOpenActionModal({type: UnderwritingProfileAction.PRIVACY_OPT_OUT_EMAIL})}}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
