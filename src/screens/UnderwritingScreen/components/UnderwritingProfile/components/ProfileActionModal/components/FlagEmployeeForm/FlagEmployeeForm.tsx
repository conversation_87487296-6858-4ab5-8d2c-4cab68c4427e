import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {FlagEmployeeValues, validationSchema} from './schema';
import {FormControl, FormControlLabel, Radio, RadioGroup, TextField, Typography, Grid2} from '@mui/material';
import dayjs from 'dayjs';
import {KasDatePickerFormField, KasModalFooter} from '@/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const FlagEmployeeForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();

    const onSubmit = async (values: FlagEmployeeValues) => {
        const url = '/api/secured/power/actions/flag';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            effective_date:
                values.actionType === 'CUSTOM' ? dayjs(values.effectiveDate).format('YYYYMMDD') : '',
            action_type: values.actionType,
            comment: values.comment,
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            effectiveDate: '',
            actionType: 'CUSTOM',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-flag-employee-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Typography variant='body1'>Action to Apply:</Typography>
                </Grid2>
                <Grid2 size={12}>
                    <FormControl disabled={submittingAction}>
                        <RadioGroup
                            name='actionType'
                            defaultValue='CUSTOM'
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <FormControlLabel
                                value='FRAUD'
                                control={<Radio size='small' />}
                                label='Fraud (10 yrs)'
                            />
                            <FormControlLabel
                                value='REVIEW'
                                control={<Radio size='small' />}
                                label='Review (2 yrs)'
                            />
                            <Grid2 container spacing={2} rowSpacing={2}>
                                <Grid2 size={12}>
                                    <FormControlLabel
                                        value='CUSTOM'
                                        control={<Radio size='small' />}
                                        label='Custom'
                                    />
                                </Grid2>
                                <Grid2 size={12}>
                                    <KasDatePickerFormField
                                        formik={formik}
                                        name='effectiveDate'
                                        label='Date'
                                        disablePast={true}
                                        disabled={submittingAction}
                                    />
                                </Grid2>
                            </Grid2>
                        </RadioGroup>
                    </FormControl>
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
