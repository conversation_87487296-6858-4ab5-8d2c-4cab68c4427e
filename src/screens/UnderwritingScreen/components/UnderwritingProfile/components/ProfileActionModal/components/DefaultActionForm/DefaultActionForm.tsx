import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {DefaultActionValues, validationSchema} from './schema';
import {Grid2, TextField} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileActionModalFormProps} from '../../../../interfaces';
import {KasModalFooter} from '@/components';

interface DefaultProfileActionModal extends ProfileActionModalFormProps {
    path: string;
    method: 'post' | 'put';
    actionType?: string;
    apiRest?: any;
}

export const DefaultActionForm = ({
    entityClass,
    entityId,
    path,
    method,
    actionType,
    apiRest = {},
}: DefaultProfileActionModal) => {
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();

    const onSubmit = async (values: DefaultActionValues) => {
        const url = `/api/secured/${path}`;
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            comment: values.comment,
            action_type: actionType,
            ...apiRest,
        });
        await onSubmitAction(url, body, method);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-default-action-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submittingAction}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
