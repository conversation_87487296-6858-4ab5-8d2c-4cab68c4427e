import React from 'react';
import {Grid2, Skeleton} from '@mui/material';

export const LoadingQueue = () => {
    return (
        <Grid2 container spacing={2}>
            <Grid2 size={4}>
                <Skeleton variant='rounded' animation='wave' width='70%' height={28} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
            </Grid2>
            <Grid2 size={4}>
                <Skeleton variant='rounded' animation='wave' width='70%' height={28} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
            </Grid2>
            <Grid2 size={4}>
                <Skeleton variant='rounded' animation='wave' width='70%' height={28} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
                <br />
                <Skeleton variant='rounded' animation='wave' width='50%' height={18} />
            </Grid2>
        </Grid2>
    );
};
