import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {DeclineApplicationValues, validationSchema} from './schema';
import {Grid2, TextField, Typography} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {RecipientDTO} from '@/models';
import {KasAutocompleteField, KasLoadingBackDrop, KasModalFooter} from '@/components';
import {SelectModel} from '@/interfaces';
import {EmailPreview} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export interface DeclineModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    application_id: number;
}

export const DeclineApplicationForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [types, setTypes] = useState<DeclineModel[]>([]);
    const [loading, setLoading] = useState(true);

    const typeOptions: SelectModel<string>[] = useMemo(() => {
        return types.map(({email_type}) => ({
            id: email_type,
            label: email_type,
            value: email_type,
        }));
    }, [types]);

    const onSubmit = async (values: DeclineApplicationValues) => {
        const url = '/api/secured/power/actions/decline-application';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            comment: values.comment,
        });
        const response = await onSubmitAction(url, body, 'put', false);

        if (response.value) {
            await onDecline();
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            type: '',
        },
        onSubmit,
        validationSchema,
    });

    const onDecline = async () => {
        const url = '/api/secured/power/actions/decline-application';

        await onSubmitAction(url, JSON.stringify(selectedType), 'post');
    };

    const loadTypes = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/power/actions/decline-application?id=${entityId}`;
            const response = await apiRequest(url);

            setTypes(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    const selectedType = useMemo(() => {
        const type = formik.values.type;

        if (type) {
            return types.find(({email_type}) => email_type === type);
        }

        return undefined;
    }, [formik.values]);

    useEffect(loadTypes, []);

    return (
        <form className='kas-underwriting-actions-decline-application-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasAutocompleteField name='type' label='Type' options={typeOptions} formik={formik} />
                </Grid2>
                {selectedType && (
                    <Grid2 size={12}>
                        <Typography variant='subtitle1'>Preview</Typography>
                        <EmailPreview
                            url='/api/secured/power/actions/decline-application?preview=true'
                            payload={JSON.stringify(selectedType)}
                        />
                    </Grid2>
                )}
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Decline'
                        submitColor='warning'
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
