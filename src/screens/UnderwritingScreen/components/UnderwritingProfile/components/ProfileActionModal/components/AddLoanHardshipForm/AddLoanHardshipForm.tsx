import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {AddLoanHardshipValues, validationSchema} from './schema';
import {Autocomplete, TextField, Grid2} from '@mui/material';
import {KasLoadingBackDrop, KasModalFooter} from '@/components';
import {LookupDTO} from '@/models';
import {SelectModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const AddLoanHardshipForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [loading, setLoading] = useState(true);
    const [hardships, setHardships] = useState<LookupDTO[]>([]);

    const onSubmit = async (values: AddLoanHardshipValues) => {
        const url = '/api/secured/underwriting/actions/add-loan-hardship';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            comment: values.comment,
            hardship_code: values.hardshipCode,
        });
        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            hardshipCode: '',
        },
        onSubmit,
        validationSchema,
    });

    const loanHardships: SelectModel<string>[] = useMemo(() => {
        return hardships.map(({id, text}) => ({
            id,
            label: text,
            value: id,
        }));
    }, [hardships]);

    const loadHardships = () => {
        (async () => {
            setLoading(true);
            const url = '/api/secured/ui/lookup/hardship';
            const response = await apiRequest(url);

            setHardships(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    useEffect(loadHardships, []);

    return (
        <form className='kas-underwriting-actions-add-loan-hardship-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Autocomplete
                        size='small'
                        options={loanHardships}
                        isOptionEqualToValue={(option, value) => option.value === value.value}
                        onChange={(_, newValue) => {
                            formik.setFieldValue('hardshipCode', newValue?.value || '');
                        }}
                        onBlur={() => {
                            formik.setFieldTouched('hardshipCode');
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                variant='outlined'
                                label='Hardship'
                                error={!!formik.errors.hardshipCode && formik.touched.hardshipCode}
                                helperText={formik.touched.hardshipCode && formik.errors.hardshipCode}
                            />
                        )}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
