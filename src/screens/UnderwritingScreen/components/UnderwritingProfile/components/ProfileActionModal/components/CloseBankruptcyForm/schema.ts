import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    code: Yup.string().required(DEFAULT_VALIDATION_MSG),
    effectiveDate: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type CloseBankruptcyValues = Yup.Asserts<typeof validationSchema>;
