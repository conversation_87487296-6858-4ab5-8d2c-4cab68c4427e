import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {VoidLoanValues, validationSchema} from './schema';
import {Autocomplete, Grid2, TextField} from '@mui/material';
import {KasDatePickerFormField, KasLoadingBackDrop, KasModalFooter} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {LookupDTO} from '@/models';
import dayjs from 'dayjs';

interface VoidLoanFormProps extends ProfileActionModalFormProps {
    loanId: number;
}

export const VoidLoanForm = ({entityClass, entityId, loanId}: VoidLoanFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [loading, setLoading] = useState(true);
    const [reasons, setReasons] = useState<string[]>([]);
    const [transactionTypes, setTransactionTypes] = useState<LookupDTO[]>([]);

    const onSubmit = async (values: VoidLoanValues) => {
        const url = isFullForm ? '/api/secured/power/actions/void/post-funding' : '/api/secured/power/actions/void';
        const body = JSON.stringify({
            comment: values.comment,
            code: values.code,
            ...(isFullForm
                ? {
                      disbursement_date: dayjs(values.disbursementDate).format('YYYYMMDD'),
                      effective_date: dayjs(values.effectiveDate).format('YYYYMMDD'),
                      transaction_type: values.transactionType,
                      entity_id: entityId,
                      entity_class: 'Employee',
                      employee_id: entityId,
                  }
                : {entity_class: entityClass, entity_id: entityId}),
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            code: '',
            effectiveDate: null,
            disbursementDate: null,
            transactionType: null,
        },
        onSubmit,
        validationSchema,
    });

    const loadTransactionTypes = () => {
        (async () => {
            setLoading(true);
            const response = await apiRequest('/api/secured/ui/lookup/db/tx');

            setTransactionTypes(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    const loadReasons = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/underwriting/profile/${loanId}/void-reason`;
            const response = await apiRequest(url);

            setReasons(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    const isFullForm = useMemo(() => {
        switch (formik.values.code) {
            case 'Pre-funding':
                return false;
            default:
                return true;
        }
    }, [formik.values.code]);

    const loadData = () => {
        loadReasons();
        loadTransactionTypes();
    };

    useEffect(loadData, []);

    return (
        <form className='kas-underwriting-actions-void-loan-form' onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <Autocomplete
                        size='small'
                        options={reasons}
                        onChange={(_, newValue) => {
                            formik.setFieldValue('code', newValue || '');
                        }}
                        onBlur={() => {
                            formik.setFieldTouched('code');
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                variant='outlined'
                                label='Reason'
                                error={!!formik.errors.code && formik.touched.code}
                                helperText={formik.touched.code && formik.errors.code}
                            />
                        )}
                    />
                </Grid2>
                {isFullForm && (
                    <Grid2 size={6}>
                        <KasDatePickerFormField
                            disableFuture={true}
                            formik={formik}
                            name='disbursementDate'
                            label='Loan Disbursement (Loan Voided) Date'
                            disabled={submittingAction}
                        />
                    </Grid2>
                )}
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                {isFullForm && (
                    <>
                        <Grid2 size={6}>
                            <Autocomplete
                                size='small'
                                getOptionLabel={(option) => option?.text}
                                options={transactionTypes}
                                onChange={(_, newValue) => {
                                    formik.setFieldValue('transactionType', newValue?.id);
                                }}
                                onBlur={() => {
                                    formik.setFieldTouched('transactionType');
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        variant='outlined'
                                        label='Transaction Type'
                                        error={
                                            !!formik.errors.transactionType && formik.touched.transactionType
                                        }
                                        helperText={
                                            formik.touched.transactionType && formik.errors.transactionType
                                        }
                                    />
                                )}
                            />
                        </Grid2>
                        <Grid2 size={6}>
                            <KasDatePickerFormField
                                disableFuture={true}
                                formik={formik}
                                name='effectiveDate'
                                label='Effective Date'
                                disabled={submittingAction}
                            />
                        </Grid2>
                    </>
                )}

                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
