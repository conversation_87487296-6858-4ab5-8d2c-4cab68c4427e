import React from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {UnderwritingProfileVerificationStatusProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';

export const VerificationStatusForm = ({
    type,
    verifiedDate,
    gid,
}: UnderwritingProfileVerificationStatusProps) => {
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();

    const onSubmit = async () => {
        const url = `/api/secured/underwriting/actions/verification/${gid}/${type}`;
        const body = JSON.stringify(verifiedDate != null);

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>
                        Are you sure you want to{' '}
                        {verifiedDate ? `delete ${type} verification` : `verify ${type}`}?
                    </Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='OK'
                        disabled={submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
