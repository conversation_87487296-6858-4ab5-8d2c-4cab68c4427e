import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Alert, Grid2} from '@mui/material';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {KasModalFooter} from '@/components';

export const SendCTAEmailForm = ({entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();

    const onSubmit = async () => {
        const url = `/api/secured/underwriting/actions/cta-email?id=${entityId}`;
        const body = JSON.stringify({
            email_type: 'support_followup_cta',
        });

        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form className='kas-underwriting-actions-send-cta-email-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>Are you sure you want to send the Request Review email?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='OK'
                        disabled={submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
