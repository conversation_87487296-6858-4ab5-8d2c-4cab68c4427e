import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    loanId: Yup.string().required(DEFAULT_VALIDATION_MSG),
    requestedDate: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
    type: Yup.string().required(DEFAULT_VALIDATION_MSG),
    files: Yup.mixed(),
});

export type RequestLetterValues = Yup.Asserts<typeof validationSchema>;
