import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    loanId: Yup.string().required(DEFAULT_VALIDATION_MSG),
    amount: Yup.string().required(DEFAULT_VALIDATION_MSG),
    confirmationNumber: Yup.string().required(DEFAULT_VALIDATION_MSG),
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    processDate: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type ManualDebitCardPaymentValues = Yup.Asserts<typeof validationSchema>;
