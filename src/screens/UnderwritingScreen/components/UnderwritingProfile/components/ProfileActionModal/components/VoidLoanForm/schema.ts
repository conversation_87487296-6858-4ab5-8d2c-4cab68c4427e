import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

const requiredIfNotPreFunding = () =>
    Yup.string()
        .nullable()
        .when('code', {
            is: (code: string) => code !== 'Pre-funding',
            then: (schema) => schema.required(DEFAULT_VALIDATION_MSG),
            otherwise: (schema) => schema,
        });

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    code: Yup.string().required(DEFAULT_VALIDATION_MSG),
    effectiveDate: requiredIfNotPreFunding(),
    disbursementDate: requiredIfNotPreFunding(),
    transactionType: requiredIfNotPreFunding(),
});

export type VoidLoanValues = Yup.Asserts<typeof validationSchema>;
