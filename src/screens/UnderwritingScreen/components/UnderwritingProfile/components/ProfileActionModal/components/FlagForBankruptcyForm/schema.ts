import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    bankruptcyChapter: Yup.string().required(DEFAULT_VALIDATION_MSG),
    bankruptcyDate: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type FlagForBankruptcyValues = Yup.Asserts<typeof validationSchema>;
