import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {ChangeIdentityStatusValues, validationSchema} from './schema';
import {FormControl, InputLabel, Select, TextField, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {KasModalFooter} from '@/components';

export const ChangeIdentityStatusForm = ({entityId}: ProfileActionModalFormProps) => {
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();

    const onSubmit = async (values: ChangeIdentityStatusValues) => {
        const url = `/api/secured/power/actions/employee/${entityId}/identity-status?status=${values.status}`;
        const body = JSON.stringify(values.comment);

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            status: null,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-change-identity-status-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={4}>
                    <FormControl
                        fullWidth
                        size='small'
                        variant='outlined'
                        error={formik.touched.status && !!formik.errors.status}>
                        <InputLabel id='select-label'>Identity Status</InputLabel>
                        <Select
                            labelId='select-label'
                            id='select-field'
                            name='status'
                            label='Identity Status'
                            value={formik.values.status}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <MenuItem value='Unverified'>Unverified</MenuItem>
                            <MenuItem value='false'>High Risk</MenuItem>
                            <MenuItem value='true'>Verified</MenuItem>
                        </Select>
                    </FormControl>
                </Grid2>
                <Grid2 size={8}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
