import './styles.scss';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Button, Stack, Typography, Grid2} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasL<PERSON>ding, KasSearchAutocompleteSelect, KasSwitch, KasSwitchWhen} from '@/components';
import {EmailPreview, ErrorView, EmailRecipients, HTMLEmailPreview} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {UnderwritingEmailPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {Completable, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {usePrint} from '@/hooks/usePrint';

interface EmailPreviewFormProps {
    id: number;
    onClose: () => void;
    actionAfterSendEmail?: () => void;
    emailPreviewUrl: string;
    emailTemplatesList: string;
    loadEmailImmediately?: boolean;
}
export const EmailPreviewForm = ({
    id,
    emailPreviewUrl,
    emailTemplatesList,
    loadEmailImmediately = false,
    onClose,
    actionAfterSendEmail,
}: EmailPreviewFormProps) => {
    const {showMessage} = useSnackbar();
    const [originalPreviewOption, setOriginalPreviewOption] =
        useState<SelectModel<UnderwritingEmailPreviewModel> | null>(null);
    const [previewOption, setPreviewOption] = useState<UnderwritingEmailPreviewModel | null>(null);
    const [emailState, setEmailState] = useState(getDefaultState<UnderwritingEmailPreviewModel[]>());
    const [contentData, setContentData] = useState<string>('');
    const {handlePrint, handleEmailPrintContent} = usePrint('Send Email');


    const options = useMemo(() => {
        if (emailState.data) {
            return emailState.data.map((item) => ({
                id: item.email_type,
                value: item,
                label: `${item.email_type}${item.sent_date ? ` (Sent: ${item.sent_date})` : ''}`,
            }));
        }

        return [];
    }, [emailState.data]);
    const changeOriginalPreviewOption = (value: SelectModel<UnderwritingEmailPreviewModel> | null) => {
        setOriginalPreviewOption(value);
        setPreviewOption(value?.value || null);
    };

    const sendEmail = async () => {
        showMessage('Sending email...', 'info');
        const response = await apiRequest(
            `${emailPreviewUrl}` + (loadEmailImmediately ? '' : `?preview=false`),
            {
                method: 'POST',
                body: JSON.stringify(previewOption),
            },
        );
        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage('Email sent successfully', 'success');
        }
        if (actionAfterSendEmail) {
            actionAfterSendEmail();
        }
    };
    const loadData = async () => {
        setEmailState(getLoadingState(emailState));
        changeOriginalPreviewOption(null);

        const response = await apiRequest(emailTemplatesList);
        if (!response.value) {
            changeOriginalPreviewOption(null);
        }
        if (!loadEmailImmediately) {
            setEmailState(getLoadedState(response as Completable<UnderwritingEmailPreviewModel[]>));
        } else {
            setPreviewOption(response.value);
            setContentData(response.value.html ? response.value.html : '');
            setOriginalPreviewOption(response);
            setEmailState({...emailState, loading: false, error: response.error});
        }
    };

    useEffect(() => {
        handleEmailPrintContent(contentData, previewOption?.recipients, previewOption?.email_type);
    }, [previewOption?.recipients, contentData]); 

    useEffect(() => {
        loadData().then();
    }, [emailTemplatesList]);

    return (
        <div className='kas-email-preview-form'>
            <Grid2 container spacing={2} rowSpacing={2}>
                <KasSwitch>
                    <KasSwitchWhen condition={emailState.loading}>
                        <Grid2 size={12}>
                            <KasLoading />
                        </Grid2>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!emailState.error}>
                        <Grid2 size={12}>
                            <ErrorView error={emailState.error} />
                        </Grid2>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!options.length && !loadEmailImmediately}>
                        <Grid2 size={12}>
                            <Typography variant='body1'>No Available Options</Typography>
                        </Grid2>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!options.length && !loadEmailImmediately}>
                        <Grid2 size={6}>
                            <KasSearchAutocompleteSelect<UnderwritingEmailPreviewModel>
                                value={originalPreviewOption}
                                label='Select Preview Type'
                                disabled={emailState.loading}
                                autoFocus={true}
                                placeholder='Select Preview Type'
                                options={options}
                                onSelect={(value) => changeOriginalPreviewOption(value)}
                            />
                        </Grid2>
                    </KasSwitchWhen>
                </KasSwitch>
            </Grid2>
            {previewOption && originalPreviewOption && (
                <>
                    <Grid2 size={12}>
                        <EmailRecipients
                            recipients={originalPreviewOption.value.recipients}
                            onChange={(recipients) => {
                                setPreviewOption({...previewOption, recipients});
                            }}
                        />
                    </Grid2>
                    <Grid2 size={12}>
                        <Typography variant='subtitle1'>Preview</Typography>
                        {loadEmailImmediately ? (
                            <HTMLEmailPreview html={previewOption.html} />
                        ) : (
                            <EmailPreview
                                url={emailPreviewUrl + '?preview=true'}
                                payload={JSON.stringify(previewOption)}
                                onContent={(content) => setContentData(content)}
                            />
                        )}
                    </Grid2>
                </>
            )}
            <Stack
                className='kas-email-preview-form__footer'
                direction='row'
                justifyContent='flex-end'
                spacing={2}>
                <Button onClick={onClose}>Close</Button>
                <Button variant='outlined' disabled={!previewOption && emailState.loading} onClick={handlePrint}>
                    Print
                </Button>
                <Button variant='contained' disabled={!previewOption} onClick={sendEmail}>
                    Send Email
                </Button>
            </Stack>
        </div>
    );
};
