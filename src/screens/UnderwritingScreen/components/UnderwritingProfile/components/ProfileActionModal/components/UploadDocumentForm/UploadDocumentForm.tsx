import './styles.scss';

import React, {useEffect, useState} from 'react';
import {useFormik} from 'formik';
import {UploadDocumentValues, validationSchema} from './schema';
import {FormControl, FormControlLabel, Grid2, Radio, RadioGroup, TextField, Typography} from '@mui/material';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasLoadingBackDrop, KasModalFooter} from '@/components';
import {useUploadDocumentForm} from './useUploadDocumentForm';
import {LoansControl, QueuesControl} from './components';

export const UploadDocumentForm = ({entityId}: ProfileActionModalFormProps) => {
    const {queuesState, loadQueues, loansState, loadLoans} = useUploadDocumentForm(entityId);
    const {submittingAction, setOpenActionModal, onSubmitAction} = useUnderwritingProfile();
    const [openFileEver, setOpenFileEver] = useState(false);

    const onSubmit = async (values: UploadDocumentValues) => {
        const url = '/api/secured/underwriting/applications/document';

        const formData = new FormData();

        if (values.files) {
            Object.values(values.files).forEach((file) => {
                formData.append('files', file as Blob);
            });
        }

        formData.append('employee_id', entityId.toString());
        formData.append('type', values.associateDocument);
        formData.append('loan_id', values.loanId || '');
        formData.append('queue_id', values.queueId || '');

        await onSubmitAction(url, formData, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            associateDocument: '',
            loanId: '',
            queueId: '',
            files: [] as File[],
        },
        onSubmit,
        validationSchema,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.currentTarget.files ? Array.from(event.currentTarget.files) : [];

        formik.setFieldValue('files', files);
    };

    const fileError = Array.isArray(formik.errors.files)
        ? formik.errors.files.join(', ')
        : formik.errors.files;

    useEffect(() => {
        loadLoans().then();
        loadQueues().then();
    }, []);

    return (
        <form className='kas-underwriting-upload-document-form' onSubmit={formik.handleSubmit}>
            {(loansState.loading || queuesState.loading) && <KasLoadingBackDrop />}
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Typography variant='body1'>Associate document with:</Typography>
                </Grid2>
                <Grid2 size={12}>
                    <FormControl fullWidth disabled={submittingAction}>
                        <RadioGroup
                            name='associateDocument'
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <Grid2 container spacing={2} rowSpacing={1}>
                                <LoansControl formik={formik} loans={loansState} onLoadLoans={loadLoans} />
                                <QueuesControl
                                    formik={formik}
                                    queues={queuesState}
                                    onLoadQueues={loadQueues}
                                />
                                <Grid2 size={6}>
                                    <FormControlLabel
                                        value='EMPLOYEE'
                                        control={<Radio size='small' />}
                                        label='Employee'
                                    />
                                </Grid2>
                            </Grid2>
                        </RadioGroup>
                    </FormControl>
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        type='file'
                        name='files'
                        variant='outlined'
                        onBlur={formik.handleBlur}
                        onFocus={() => {
                            setOpenFileEver(!!formik.touched.files);
                        }}
                        onChange={handleFileChange}
                        slotProps={{htmlInput: {multiple: true}}}
                        error={openFileEver && !!formik.errors.files}
                        helperText={openFileEver && fileError}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
