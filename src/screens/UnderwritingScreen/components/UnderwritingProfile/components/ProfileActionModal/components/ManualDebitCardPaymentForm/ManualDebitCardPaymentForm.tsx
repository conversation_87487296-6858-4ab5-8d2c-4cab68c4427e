import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {ManualDebitCardPaymentValues, validationSchema} from './schema';
import {Grid2, TextField} from '@mui/material';
import dayjs from 'dayjs';
import {KasAutocompleteField, KasDatePickerFormField, KasLoadingBackDrop, KasModalFooter} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {LookupDTO} from '@/models';
import {useSnackbar} from '@/hooks/useSnackbar';
import {SelectModel} from '@/interfaces';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const ManualDebitCardPaymentForm = ({entityId}: ProfileActionModalFormProps) => {
    const {showMessage} = useSnackbar();
    const {submittingAction, onSubmitAction, setOpenActionModal} = useUnderwritingProfile();
    const [loading, setLoading] = useState(false);
    const [loans, setLoans] = useState<LookupDTO[]>([]);

    const onSubmit = async (values: ManualDebitCardPaymentValues) => {
        const url = '/api/secured/power/actions/debitcard-payment';
        const body = JSON.stringify({
            entity_class: 'Loan',
            entity_id: values.loanId,
            amount: values.amount,
            confirmation_number: values.confirmationNumber,
            process_date: dayjs(values.processDate).format('YYYYMMDD'),
            comment: values.comment,
        });

        await onSubmitAction(url, body, 'post');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            amount: '',
            loanId: '',
            confirmationNumber: '',
            processDate: '',
        },
        onSubmit,
        validationSchema,
    });

    const loanOptions: SelectModel<string>[] = useMemo(() => {
        return loans.map(({id, text}) => ({
            id,
            label: text,
            value: id,
        }));
    }, [loans]);

    const loadLoans = () => {
        (async () => {
            setLoading(true);
            const url = `/api/secured/ui/lookup/employee-loan/${entityId}?direction=balance`;
            const response = await apiRequest(url);

            setLoans(response.value || []);

            if (response.error) {
                setOpenActionModal(null);
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    useEffect(loadLoans, []);

    return (
        <form
            className='kas-underwriting-actions-manual-debit-card-payment-form'
            onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='processDate'
                        label='Payment Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasAutocompleteField
                        name='loanId'
                        label='Loan ID'
                        options={loanOptions}
                        formik={formik}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        name='confirmationNumber'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Confirmation Number'
                        variant='outlined'
                        error={!!formik.errors.confirmationNumber && formik.touched.confirmationNumber}
                        helperText={formik.touched.confirmationNumber && formik.errors.confirmationNumber}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
