import {useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {LookupDTO} from '@/models';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DataStateInterface} from '@/interfaces';

interface UploadDocumentFormContextModel {
    loansState: DataStateInterface<LookupDTO[]>;
    loadLoans: () => Promise<void>;
    queuesState: DataStateInterface<LookupDTO[]>;
    loadQueues: () => Promise<void>;
}

export const useUploadDocumentForm = (entityId: number): UploadDocumentFormContextModel => {
    const {showMessage} = useSnackbar();
    const [loansState, setLoansState] = useState(getDefaultState<LookupDTO[]>());
    const [queuesState, setQueuesState] = useState(getDefaultState<LookupDTO[]>());

    const loadLoans = async () => {
        const url = `/api/secured/ui/lookup/employee-loan/${entityId}`;

        setLoansState(getLoadingState(loansState));

        const response = await apiRequest(url);

        setLoansState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    const loadQueues = async () => {
        const url = `/api/secured/ui/lookup/employee/${entityId}/queues`;

        setQueuesState(getLoadingState(queuesState));

        const response = await apiRequest(url);

        setQueuesState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    return {loansState, loadLoans, queuesState, loadQueues};
};
