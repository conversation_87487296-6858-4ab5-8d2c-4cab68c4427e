import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {EditEmployeeProfileValues, validationSchema} from './schema';
import {Grid2, TextField} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {KasDatePickerFormField, KasMaskInput, KasModalFooter} from '@/components';
import dayjs from 'dayjs';
import {EmployerFields, StatusField} from './components';

export const EditEmployeeProfileForm = ({entityId}: ProfileActionModalFormProps) => {
    const {
        submittingAction,
        setOpenActionModal,
        onSubmitAction,
        employeeProfileState: {data},
    } = useUnderwritingProfile();

    const onSubmit = async (values: EditEmployeeProfileValues) => {
        const url = '/api/secured/underwriting/edit/employee';
        const body = JSON.stringify({
            dob: values.dob === data?.dob ? null : dayjs(values.dob).format('YYYYMMDD'),
            employer_id: values.employerId === data?.employer_id.toString() ? null : values.employerId,
            payrollGroupId:
                values.payrollGroupId === data?.payroll_group_id.toString() ? null : values.payrollGroupId,
            first_name: values.firstName === data?.first_name ? '' : values.firstName,
            gid: entityId,
            last_hire_date:
                values.lastHireDate === data?.last_hire_date
                    ? null
                    : dayjs(values.lastHireDate).format('YYYYMMDD'),
            last_name: values.lastName === data?.last_name ? '' : values.lastName,
            ssn: values.ssn === data?.ssn ? '' : values.ssn,
            status: values.status === data?.status ? '' : values.status,
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            employerId: (data?.employer_locked && data?.employer_id.toString()) || '',
            payrollGroupId: (data?.employer_locked && data?.payroll_group_id.toString()) || '',
            dob: data?.dob || '',
            firstName: data?.first_name || '',
            lastName: data?.last_name || '',
            ssn: '',
            status: '',
            lastHireDate: data?.last_hire_date || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-edit-employee-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <EmployerFields formik={formik} />
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        name='firstName'
                        size='small'
                        disabled={submittingAction}
                        label='First Name'
                        variant='outlined'
                        defaultValue={formik.values.firstName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        name='lastName'
                        size='small'
                        disabled={submittingAction}
                        label='Last Name'
                        variant='outlined'
                        defaultValue={formik.values.lastName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <StatusField formik={formik} />
                </Grid2>
                <Grid2 size={6}>
                    <KasMaskInput formik={formik} name='ssn' disabled={submittingAction} mask='***********'>
                        <TextField
                            fullWidth
                            name='ssn'
                            size='small'
                            variant='outlined'
                            placeholder={data?.ssn || ''}
                            label='SSN'
                            disabled={submittingAction}
                            slotProps={{
                                inputLabel: {shrink: true},
                                input: {notched: true},
                            }}
                            value={formik.values.ssn}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={!!formik.errors.ssn && formik.touched.ssn}
                            helperText={formik.touched.ssn && formik.errors.ssn}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='dob'
                        label='Date of Birth'
                        disabled={submittingAction || data?.dob_locked}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='lastHireDate'
                        label='Hired Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.dirty || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
