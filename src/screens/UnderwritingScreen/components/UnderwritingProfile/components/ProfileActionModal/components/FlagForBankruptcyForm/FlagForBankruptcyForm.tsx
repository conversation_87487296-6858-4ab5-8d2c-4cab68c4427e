import './styles.scss';

import React from 'react';
import {useFormik} from 'formik';
import {FlagForBankruptcyValues, validationSchema} from './schema';
import {FormControl, FormHelperText, Grid2, InputLabel, Select, TextField} from '@mui/material';
import dayjs from 'dayjs';
import MenuItem from '@mui/material/MenuItem';
import {KasDatePickerFormField, KasModalFooter} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {ProfileActionModalFormProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';

export const FlagForBankruptcyForm = ({entityClass, entityId}: ProfileActionModalFormProps) => {
    const {
        submittingAction,
        setOpenActionModal,
        onSubmitAction,
        employeeProfileState: {data},
    } = useUnderwritingProfile();

    const onSubmit = async (values: FlagForBankruptcyValues) => {
        const url = '/api/secured/power/actions/flag-bankruptcy';
        const body = JSON.stringify({
            entity_class: entityClass,
            entity_id: entityId,
            bankruptcy_chapter: values.bankruptcyChapter,
            bankruptcy_date: dayjs(values.bankruptcyDate).format('YYYYMMDD'),
            comment: values.comment,
        });

        await onSubmitAction(url, body, 'put');
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            bankruptcyChapter: data?.bankruptcy_chapter || '',
            bankruptcyDate: data?.bankruptcy_date || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-underwriting-actions-flag-for-bankruptcy-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='bankruptcyDate'
                        label='Bankruptcy Petition Date'
                        disabled={submittingAction}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <FormControl
                        fullWidth
                        size='small'
                        variant='outlined'
                        error={formik.touched.bankruptcyChapter && !!formik.errors.bankruptcyChapter}>
                        <InputLabel id='select-label'>Chapter</InputLabel>
                        <Select
                            labelId='select-label'
                            id='select-field'
                            name='bankruptcyChapter'
                            label='Chapter'
                            value={formik.values.bankruptcyChapter}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <MenuItem value='7'>Chapter 7</MenuItem>
                            <MenuItem value='13'>Chapter 13</MenuItem>
                        </Select>
                        {formik.touched.bankruptcyChapter && !!formik.errors.bankruptcyChapter && (
                            <FormHelperText>{formik.errors.bankruptcyChapter}</FormHelperText>
                        )}
                    </FormControl>
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Flag'
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
