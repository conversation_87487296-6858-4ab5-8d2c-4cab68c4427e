import React, {useEffect, useState} from 'react';
import {FormikValues} from 'formik';
import {FormControl, InputLabel, Select} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {LookupDTO} from '@/models';

interface StatusFieldProps {
    formik: FormikValues;
}

export const StatusField = ({formik}: StatusFieldProps) => {
    const {
        employeeProfileState: {data},
    } = useUnderwritingProfile();
    const [statuses, setStatuses] = useState<LookupDTO[]>([]);

    const getStatuses = async () => {
        const url = `/api/secured/ui/lookup/employee/status`;
        const response = await apiRequest(url);

        setStatuses(response.value ? response.value : []);
    };

    useEffect(() => {
        getStatuses().then();
    }, []);

    return (
        <FormControl fullWidth size='small' variant='outlined'>
            <InputLabel id='select-status-label' shrink>
                Status
            </InputLabel>
            <Select
                displayEmpty
                notched
                labelId='select-status-label'
                id='select-status-field'
                name='status'
                label='Status'
                renderValue={(selected) => {
                    if (!selected) {
                        return data?.status || '';
                    }
                    return statuses.find((item) => item.id === selected)?.text || '';
                }}
                value={formik.values.status}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}>
                {statuses.map((status) => (
                    <MenuItem key={status.id} value={status.id}>
                        {status.text}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    );
};
