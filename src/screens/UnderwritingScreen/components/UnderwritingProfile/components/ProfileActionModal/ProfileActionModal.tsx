import './styles.scss';

import React, {useMemo} from 'react';
import {KasL<PERSON>dingBackDrop, KasModal} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {
    AddLoanHardshipForm,
    AddReferralForm,
    BenefitfocusForm,
    ChangeIdentityStatusForm,
    ChangeOffCurrentLoanForm,
    CloseBankruptcyForm,
    DeclineApplicationForm,
    DefaultActionForm,
    EditEmployeeProfileForm,
    EmailPreviewForm,
    FlagEmployeeForm,
    FlagForBankruptcyForm,
    FlagSCRAForm,
    LetterPreviewForm,
    ManualDebitCardPaymentForm,
    NoLoanToVoidForm,
    ProceedEditEmployeeForm,
    PushUserIntoQueueForm,
    RequestLetterForm,
    ResendEmailReferralForm,
    RestrictContactForm,
    SendCTAEmailForm,
    SettlementForm,
    UpdateDirectDepositForm,
    UploadACHForm,
    UploadDocumentForm,
    VerificationStatusForm,
    VoidLoanForm,
} from './components';
import {
    ProfileActionModalFormEntityClass,
    ProfileActionModalFormProps,
    UnderwritingProfileAction,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {capitalizeWords} from '@/utils/TextUtils';

export const ProfileActionModal = () => {
    const {typ, profile, openActionModal, setOpenActionModal, submittingAction} = useUnderwritingProfile();

    const title = useMemo(() => {
        switch (openActionModal?.type) {
            case UnderwritingProfileAction.ADD_COMMENT:
                return 'Add New Comment';
            case UnderwritingProfileAction.DIRECT_DEPOSIT_REPORT:
                return 'Report Direct Deposit';
            case UnderwritingProfileAction.REQUEST_LETTER:
                return 'Request Letter(Dv or Gw)';
            case UnderwritingProfileAction.SET_TERMINATED:
                return 'Mark Employee Terminated';
            case UnderwritingProfileAction.SET_DECEASED:
                return 'Mark Employee Deceased';
            case UnderwritingProfileAction.UNFLAG_DECEASED:
                return 'Unmark Employee Deceased';
            case UnderwritingProfileAction.PAID_LEAVE:
                return 'Mark Employee On Leave(Paid)';
            case UnderwritingProfileAction.UNPAID_LEAVE:
                return 'Mark Employee On Leave(Unpaid)';
            case UnderwritingProfileAction.UPLOAD_ACH:
                return 'Upload Signed ACH Agreement';
            case UnderwritingProfileAction.PAYROLL_DEDUCTION_REVOKE:
                return 'Revoke Payroll Deduction';
            case UnderwritingProfileAction.PAYROLL_DEDUCTION_AUTHORIZE:
                return 'Authorize Payroll Deduction';
            case UnderwritingProfileAction.ADD_LOAN_HARDSHIP:
                return 'Add Loan Hardship';
            case UnderwritingProfileAction.REMOVE_LOAN_HARDSHIP:
                return 'Remove Existing Loan Hardship';
            case UnderwritingProfileAction.UNFLAG_INCOME:
                return 'Unflag from Income';
            case UnderwritingProfileAction.CLOSE_BANKRUPTCY:
                return 'Close Bankruptcy';
            case UnderwritingProfileAction.ADD_REFERRAL:
                return 'Add Referral';
            case UnderwritingProfileAction.FLAGGING_MODAL:
                return '[POWER] Flag for Review/Fraud';
            case UnderwritingProfileAction.FLAGGING_SCRA_MODAL:
                return '[POWER] Flag for SCRA';
            case UnderwritingProfileAction.UNFLAG_REVIEW:
                return '[POWER] Unflag from Review';
            case UnderwritingProfileAction.VOID_LOAN:
                return '[POWER] Void Current Loan';
            case UnderwritingProfileAction.DECLINE_APPLICATION:
                return '[POWER] Decline Application';
            case UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN:
                return '[POWER] Loan Disbursement (Redisbursement)';
            case UnderwritingProfileAction.CHARGEOFF_LOAN:
                return '[POWER] Charge-off Current Loan';
            case UnderwritingProfileAction.SETTLE_LOAN:
                return '[POWER] Settle (Partial Charge-off) Current Loan';
            case UnderwritingProfileAction.CANCEL_ACH_PAYMENT:
                return '[POWER] Cancel ACH Payment';
            case UnderwritingProfileAction.CLEAR_EMPLOYMENT:
                return '[POWER] Clear User Employment';
            case UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT:
                return '[POWER] Add Manual Debit Card Payment';
            case UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING:
                return '[POWER] Unenroll from Credit Monitoring';
            case UnderwritingProfileAction.RESET_MFA:
                return '[POWER] Reset MFA';
            case UnderwritingProfileAction.RUN_FULFILLMENT:
                return '[ADMIN] Re-attempt Fulfillment';
            case UnderwritingProfileAction.RUN_FULFILLMENT_BF:
                return '[ADMIN] Re-attempt Fulfillment (Benefitfocus)';
            case UnderwritingProfileAction.LETTER_PREVIEW:
                return 'Letter Preview';
            case UnderwritingProfileAction.FLAG_REVIEW:
                return '[POWER] Flag for Review';
            case UnderwritingProfileAction.FLAG_FOR_BANKRUPTCY:
                return 'Flag for Bankruptcy';
            case UnderwritingProfileAction.CLEAR_BANKRUPTCY:
                return 'Clear Bankruptcy';
            case UnderwritingProfileAction.IDENTITY_VERIFIED:
                return 'Change status to';
            case UnderwritingProfileAction.RESTRICT_CONTACT:
                return 'Restrict Contact';
            case UnderwritingProfileAction.PRIVACY_OPT_OUT_EMAIL:
                return 'Email Preview';
            case UnderwritingProfileAction.PROCEED_EDIT_EMPLOYEE_PROFILE:
            case UnderwritingProfileAction.EDIT_EMPLOYEE_PROFILE:
                return 'Edit Employee Info';
            case UnderwritingProfileAction.SEND_CTA_EMAIL:
                return 'Send CTA Email';
            case UnderwritingProfileAction.PUSH_USER_INTO_QUEUE:
                return 'Push User into Queue';
            case UnderwritingProfileAction.UPLOAD_DOCUMENT:
                return 'Upload Document';
            case UnderwritingProfileAction.VERIFICATION_STATUS:
                return `${capitalizeWords(openActionModal.props.type)} Verification`;
            case UnderwritingProfileAction.RESEND_REFERRAL_EMAIL:
                return 'Resend Email';
            case UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT:
                return 'Mark Direct Deposit as Primary';
            default:
                return 'Action';
        }
    }, [openActionModal]);

    const privacyOptOutEmailURL = `/api/secured/underwriting/employee-profile/${profile.employee_id}/privacy-opt-out`;
    const renderActionForm = useMemo(() => {
        if (typ === 'EMPL') {
            const props: ProfileActionModalFormProps = {
                entityClass: ProfileActionModalFormEntityClass.Employee,
                entityId: profile.employee_id,
            };

            switch (openActionModal?.type) {
                case UnderwritingProfileAction.ADD_COMMENT:
                    return (
                        <DefaultActionForm path='underwriting/actions/add-comment' method='post' {...props} />
                    );
                case UnderwritingProfileAction.DIRECT_DEPOSIT_REPORT:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/report-direct-deposit'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.REQUEST_LETTER:
                    return <RequestLetterForm {...props} />;
                case UnderwritingProfileAction.SET_TERMINATED:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/set-terminated'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.SET_DECEASED:
                    return (
                        <DefaultActionForm path='underwriting/actions/set-deceased' method='put' {...props} />
                    );
                case UnderwritingProfileAction.UNFLAG_DECEASED:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/unflag-deceased'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.PAID_LEAVE:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/paid-leave?paid=1'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.UNPAID_LEAVE:
                    return (
                        <DefaultActionForm path='underwriting/actions/paid-leave' method='put' {...props} />
                    );
                case UnderwritingProfileAction.UPLOAD_ACH:
                    return <UploadACHForm {...props} />;
                case UnderwritingProfileAction.PAYROLL_DEDUCTION_REVOKE:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/deduction?type=revoke'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.PAYROLL_DEDUCTION_AUTHORIZE:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/deduction?type=authorize'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.ADD_LOAN_HARDSHIP:
                    return <AddLoanHardshipForm {...props} />;
                case UnderwritingProfileAction.REMOVE_LOAN_HARDSHIP:
                    return <DefaultActionForm path='underwriting/actions/hardship' method='put' {...props} />;
                case UnderwritingProfileAction.UNFLAG_INCOME:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/unflag-income'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.CLOSE_BANKRUPTCY:
                    return <CloseBankruptcyForm {...props} />;
                case UnderwritingProfileAction.ADD_REFERRAL:
                    return <AddReferralForm {...props} />;
                case UnderwritingProfileAction.FLAGGING_MODAL:
                    return <FlagEmployeeForm {...props} />;
                case UnderwritingProfileAction.FLAGGING_SCRA_MODAL:
                    return <FlagSCRAForm {...props} />;
                case UnderwritingProfileAction.UNFLAG_REVIEW:
                    return (
                        <DefaultActionForm
                            path='power/actions/flag'
                            method='put'
                            actionType='UNFLAG_REVIEW'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.VOID_LOAN:
                    if (profile.current_loan_id) {
                        return <VoidLoanForm {...props} loanId={profile.current_loan_id} />;
                    } else {
                        return <NoLoanToVoidForm />;
                    }
                case UnderwritingProfileAction.DECLINE_APPLICATION:
                    return <DeclineApplicationForm {...props} />;
                case UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN:
                    // TODO: not working in current dashboard
                    return null;
                case UnderwritingProfileAction.CHARGEOFF_LOAN:
                    return <ChangeOffCurrentLoanForm {...props} />;
                case UnderwritingProfileAction.SETTLE_LOAN:
                    return <SettlementForm {...props} />;
                case UnderwritingProfileAction.CANCEL_ACH_PAYMENT:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/cancel-ach-payment'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.CLEAR_EMPLOYMENT:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/clear-employment'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT:
                    return <ManualDebitCardPaymentForm {...props} />;
                case UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/unenroll-monitoring'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.RUN_FULFILLMENT:
                    return <DefaultActionForm path='power/actions/run-fulfillment' method='put' {...props} />;
                case UnderwritingProfileAction.RUN_FULFILLMENT_BF:
                    return <BenefitfocusForm {...props} />;
                case UnderwritingProfileAction.LETTER_PREVIEW:
                    return <LetterPreviewForm />;
                case UnderwritingProfileAction.FLAG_FOR_BANKRUPTCY:
                    return <FlagForBankruptcyForm {...props} />;
                case UnderwritingProfileAction.CLEAR_BANKRUPTCY:
                    return (
                        <DefaultActionForm
                            path='power/actions/flag-bankruptcy'
                            method='put'
                            apiRest={{code: 'CLEAR'}}
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.IDENTITY_VERIFIED:
                    return <ChangeIdentityStatusForm {...props} />;
                case UnderwritingProfileAction.RESTRICT_CONTACT:
                    return <RestrictContactForm {...props} />;
                case UnderwritingProfileAction.PROCEED_EDIT_EMPLOYEE_PROFILE:
                    return <ProceedEditEmployeeForm />;
                case UnderwritingProfileAction.EDIT_EMPLOYEE_PROFILE:
                    return <EditEmployeeProfileForm {...props} />;
                case UnderwritingProfileAction.SEND_CTA_EMAIL:
                    return <SendCTAEmailForm {...props} />;
                case UnderwritingProfileAction.PUSH_USER_INTO_QUEUE:
                    return <PushUserIntoQueueForm {...props} />;
                case UnderwritingProfileAction.UPLOAD_DOCUMENT:
                    return <UploadDocumentForm {...props} />;
                case UnderwritingProfileAction.VERIFICATION_STATUS:
                    return <VerificationStatusForm {...openActionModal.props} />;
                case UnderwritingProfileAction.RESET_MFA:
                    return <DefaultActionForm path='power/actions/reset-mfa' method='put' {...props} />;
                case UnderwritingProfileAction.PRIVACY_OPT_OUT_EMAIL:
                    return (
                        <EmailPreviewForm
                            id={props.entityId}
                            onClose={() => {
                                setOpenActionModal({type: UnderwritingProfileAction.RESTRICT_CONTACT});
                            }}
                            actionAfterSendEmail={() => {
                                setOpenActionModal({type: UnderwritingProfileAction.RESTRICT_CONTACT});
                            }}
                            emailPreviewUrl='/api/secured/underwriting/employee-profile/email/privacy-opt-out?saveComment=true'
                            emailTemplatesList={privacyOptOutEmailURL}
                            loadEmailImmediately={true}
                        />
                    );
                case UnderwritingProfileAction.RESEND_REFERRAL_EMAIL:
                    return <ResendEmailReferralForm {...openActionModal.props} />;
                case UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT:
                    return <UpdateDirectDepositForm {...openActionModal.props} />;
                default:
                    return null;
            }
        } else {
            const props: ProfileActionModalFormProps = {
                entityClass: ProfileActionModalFormEntityClass.User,
                entityId: profile.user_id,
            };

            switch (openActionModal?.type) {
                case UnderwritingProfileAction.ADD_COMMENT:
                    return (
                        <DefaultActionForm path='underwriting/actions/add-comment' method='post' {...props} />
                    );
                case UnderwritingProfileAction.FLAG_REVIEW:
                    return (
                        <DefaultActionForm
                            path='power/actions/flag'
                            method='put'
                            actionType='REVIEW'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.UNFLAG_REVIEW:
                    return (
                        <DefaultActionForm
                            path='power/actions/flag'
                            method='put'
                            actionType='UNFLAG_REVIEW'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.CLEAR_EMPLOYMENT:
                    return (
                        <DefaultActionForm
                            path='underwriting/actions/clear-employment'
                            method='put'
                            {...props}
                        />
                    );
                case UnderwritingProfileAction.VERIFICATION_STATUS:
                    return <VerificationStatusForm {...openActionModal.props} />;
                default:
                    return null;
            }
        }
    }, [openActionModal]);

    return (
        <KasModal title={title} open={!!openActionModal} onClose={() => setOpenActionModal(null)}>
            <div className='kas-underwriting-profile-action-modal'>
                {submittingAction && <KasLoadingBackDrop />}
                {renderActionForm}
            </div>
        </KasModal>
    );
};
