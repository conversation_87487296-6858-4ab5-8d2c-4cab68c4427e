import React, {useEffect} from 'react';
import {
    ProfileItem,
    useUnderwritingLoans,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {LoansDetails, LoansPreview} from './components';
import {UnderwritingProfileItemModel} from '@/models';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';

export const Loans = ({item}: {item: UnderwritingProfileItemModel}) => {
    const {loadEmployeeProfileData, employeeProfileState} = useUnderwritingProfile();
    const {
        loansState: {loading, error, data},
        loadLoansData,
    } = useUnderwritingLoans();

    const onRefreshHandler = () => {
        loadLoansData().then();
        if (employeeProfileState.error) {
            loadEmployeeProfileData().then();
        }
    };

    useEffect(() => {
        loadLoansData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={loading}
            loadingError={error || employeeProfileState.error}
            loaded={!!data}
            DetailsComponent={<LoansDetails />}
            PreviewComponent={<LoansPreview />}
        />
    );
};
