import React, {createContext, useContext, useState} from 'react';
import {UnderwritingLoanModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {UnderwritingLoanModalProps} from './interfaces';

interface UnderwritingLoansContextModel {
    openLoanModal: UnderwritingLoanModalProps | null;
    setOpenLoanModal: (value: UnderwritingLoanModalProps | null) => void;
    loansState: DataStateInterface<UnderwritingLoanModel[]>;
    loadLoansData: () => Promise<void>;
}

const UnderwritingLoansContext = createContext<UnderwritingLoansContextModel | undefined>(undefined);

interface UnderwritingLoansProviderProps {
    children: React.ReactNode;
}

export const UnderwritingLoansProvider: React.FC<UnderwritingLoansProviderProps> = ({children}) => {
    const {gid} = useUnderwritingProfile();
    const [openLoanModal, setOpenLoanModal] = useState<UnderwritingLoanModalProps | null>(null);
    const [loansState, setLoansState] = useState(getDefaultState<UnderwritingLoanModel[]>());

    const loadLoansData = async () => {
        const url = `/api/secured/underwriting/employee-profile/${gid}/loans`;

        setLoansState(getLoadingState(loansState));

        const response: Completable<UnderwritingLoanModel[]> = await apiRequest(url);

        setLoansState(getLoadedState(response));
    };

    const value: UnderwritingLoansContextModel = {
        openLoanModal,
        setOpenLoanModal,
        loansState,
        loadLoansData,
    };

    return <UnderwritingLoansContext.Provider value={value}>{children}</UnderwritingLoansContext.Provider>;
};

export function useUnderwritingLoans() {
    const context = useContext(UnderwritingLoansContext);
    if (!context) {
        throw new Error('useUnderwritingLoans must be used within UnderwritingLoansProvider');
    }
    return context;
}
