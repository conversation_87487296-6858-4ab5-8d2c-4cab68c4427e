import {LoanDetailsModel} from '@/interfaces';

interface BaseLoanActionProps {
    onClose: () => void;
}

export interface PayoffLoanActionProps extends BaseLoanActionProps {
    loan: LoanDetailsModel;
}

export interface SendCommunicationLoanActionProps extends BaseLoanActionProps {
    loan: LoanDetailsModel;
}

export type UnderwritingLoanModalProps =
    | {type: UnderwritingLoanModal.Payoff_Loan; props: PayoffLoanActionProps}
    | {type: UnderwritingLoanModal.Send; props: SendCommunicationLoanActionProps}
    | {type: UnderwritingLoanModal.Backup_Ach_Pause_Clear; props: PayoffLoanActionProps}
    | {type: UnderwritingLoanModal.Backup_Ach_Pause_Flag; props: PayoffLoanActionProps}
    ;

export enum UnderwritingLoanModal {
    Payoff_Loan = 'Payoff_Loan',
    Send = 'Send',
    Backup_Ach_Pause_Clear = 'Backup_Ach_Pause_Clear',
    Backup_Ach_Pause_Flag = 'Backup_Ach_Pause_Flag',
}
