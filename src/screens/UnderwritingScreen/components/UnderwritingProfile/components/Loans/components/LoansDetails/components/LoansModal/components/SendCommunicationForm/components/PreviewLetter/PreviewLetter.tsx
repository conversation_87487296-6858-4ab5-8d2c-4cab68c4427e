import React, {useEffect, useMemo, useState} from 'react';
import {<PERSON><PERSON><PERSON><PERSON>ding, KasSearchAutocompleteSelect, KasSelect, KasSwitch, KasS<PERSON><PERSON><PERSON>} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingLoanLetterPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {SelectModel} from '@/interfaces';
import {EmailPreview, ErrorView} from '@/views';
import {Typography, Grid2} from '@mui/material';
import {LetterRecipients, PaymentOffset} from './components';
import {useSendCommunication} from './../../useSendCommunication';
import MenuItem from '@mui/material/MenuItem';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {Completable} from '@/interfaces';

interface PreviewLetterProps {
    onChange: (value: UnderwritingLoanLetterPreviewModel | null) => void;
}

export const PreviewLetter = ({onChange}: PreviewLetterProps) => {
    const {loan, selectedTypeOption, handleLetterPrintContent} = useSendCommunication();
    const [originalPreviewOption, setOriginalPreviewOption] =
        useState<SelectModel<UnderwritingLoanLetterPreviewModel> | null>(null);
    const [previewOption, setPreviewOption] = useState<UnderwritingLoanLetterPreviewModel | null>(null);
    const [letterState, setLetterState] = useState(getDefaultState<UnderwritingLoanLetterPreviewModel[]>());

    const options = useMemo(() => {
        if (letterState.data) {
            return letterState.data.map((item) => ({
                id: item.letter_type,
                value: item,
                label: `${item.letter_type}${item.sent_date ? ` (Sent: ${item.sent_date})` : ''}`,
            }));
        }
        return [];
    }, [letterState.data]);

    const recoveryRatios = useMemo(() => {
        if (originalPreviewOption?.value.recovery_ratios) {
            return originalPreviewOption.value.recovery_ratios.map((item) => ({
                id: item,
                value: item,
                label: item * 100 + '%',
            }));
        }
        return null;
    }, [originalPreviewOption?.value.recovery_ratios]);

    const showPaymentOffset = useMemo(() => {
        if (originalPreviewOption) {
            const {letter_type} = originalPreviewOption.value;

            return letter_type === 'loan_early_repayment' || letter_type === 'loan_early_repayment_retained';
        }

        return false;
    }, [originalPreviewOption?.value]);

    const url = useMemo(() => {
        return `/api/secured/underwriting/loans/${loan.gid}/letter-preview/${selectedTypeOption?.value}`;
    }, [selectedTypeOption, loan.gid]);

    const letterPreviewUrl = useMemo(() => {
        return `/api/secured/underwriting/loans/letter-preview/${selectedTypeOption?.value}?preview=true`;
    }, [selectedTypeOption]);

    const changeOriginalPreviewOption = (
        newOption: SelectModel<UnderwritingLoanLetterPreviewModel> | null,
    ) => {
        setOriginalPreviewOption(newOption);
        setPreviewOption(newOption?.value || null);
    };

    const loadData = async () => {
        setLetterState(getLoadingState(letterState));
        const response: Completable<UnderwritingLoanLetterPreviewModel[]> = await apiRequest(url);
        setLetterState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, [url]);

    useEffect(() => {
        onChange(previewOption);
    }, [previewOption]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={letterState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!letterState.error}>
                <ErrorView error={letterState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!options.length}>
                <Typography variant='body1'>No Available Options</Typography>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!options.length}>
                <Grid2 container spacing={2} rowSpacing={2}>
                    <Grid2 size={6}>
                        <KasSearchAutocompleteSelect<UnderwritingLoanLetterPreviewModel>
                            value={originalPreviewOption}
                            label='Select Preview Type'
                            disabled={letterState.loading}
                            placeholder='Select Preview Type'
                            options={options}
                            onSelect={(value) => changeOriginalPreviewOption(value)}
                        />
                    </Grid2>
                    {previewOption && originalPreviewOption && (
                        <>
                            <Grid2 size={12}>
                                <LetterRecipients
                                    recipients={originalPreviewOption.value.recipients}
                                    onChange={(recipients) => {
                                        setPreviewOption({...previewOption, recipients});
                                    }}
                                />
                            </Grid2>
                            {showPaymentOffset && (
                                <Grid2 size={6}>
                                    <PaymentOffset
                                        onChange={(offset_days) => {
                                            setPreviewOption({...previewOption, offset_days});
                                        }}
                                    />
                                </Grid2>
                            )}
                            {recoveryRatios && (
                                <Grid2 size={6}>
                                    <KasSelect
                                        value={previewOption.recovery_ratio}
                                        label='Recovery Percent'
                                        onChange={(event) => {
                                            const newValue = event.target.value as number;

                                            setPreviewOption({
                                                ...previewOption,
                                                recovery_ratio: newValue ? newValue : undefined,
                                            });
                                        }}>
                                        {recoveryRatios.map(({id, value, label}) => (
                                            <MenuItem key={id} value={value}>
                                                {label}
                                            </MenuItem>
                                        ))}
                                    </KasSelect>
                                </Grid2>
                            )}
                            <Grid2 size={12}>
                                <Typography variant='subtitle1'>Preview</Typography>
                                <EmailPreview
                                    url={letterPreviewUrl}
                                    payload={JSON.stringify(previewOption)}
                                    isLetter={true}
                                    onContent={handleLetterPrintContent}
                                />
                            </Grid2>
                        </>
                    )}
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
