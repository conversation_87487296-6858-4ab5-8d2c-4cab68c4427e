import {KasModal} from '@/components';
import {KasCommentForm} from '@/components/KasGlobalModals/components/KasCommentForm/CommentForm';
import {KasCommentFormValues} from '@/components/KasGlobalModals/components/KasCommentForm/schema';
import {ActionCell} from '@/components/table/cells';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Loans/useUnderwritingLoans';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/useUnderwritingProfile';
import {Delete} from '@mui/icons-material';
import {useState} from 'react';

interface CancelAchButtonProps {
    loanId: number;
    paymentId: number;
}

export const CancelAchButton = ({loanId, paymentId}: CancelAchButtonProps) => {
    const [open, setOpen] = useState(false);
    const onClose = () => setOpen(false);

    const {onSubmitAction, loadEmployeeProfileData} = useUnderwritingProfile();
    const {loadLoansData} = useUnderwritingLoans();

    const onSubmit = async (values: KasCommentFormValues) => {
        const url = `/api/secured/underwriting/actions/cancel-ach-payment`;
        const body = JSON.stringify({
            entity_class: 'Loan',
            entity_id: loanId,
            comment: values.comment,
            context: `${paymentId}`,
        });

        const response = await onSubmitAction(url, body, 'put');

        onClose();

        if (response.value) {
            loadEmployeeProfileData().then();
            loadLoansData().then();
        }
    };

    return (
        <>
            <ActionCell
                testid='uw-loan-details-cancel-ach'
                Icon={<Delete color='error' titleAccess='Cancel ACH' />}
                onClick={() => setOpen(true)}
            />
            <KasModal
                title='Cancel ACH'
                open={open}
                onClose={onClose}
                data-testid='uw-loan-details-cancel-ach'>
                <KasCommentForm onSubmit={onSubmit} onCancel={onClose} />
            </KasModal>
        </>
    );
};
