import React, {useMemo} from 'react';
import {Alert, AlertTitle, Button} from '@mui/material';
import Box from '@mui/material/Box';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Kas<PERSON><PERSON>, KasSwitchWhen} from '@/components';

export const LoanDetailsAlerts = () => {
    const {loanAlerts, confirmLoanAlert, employeeProfileState} = useUnderwritingProfile();

    const scraMessage = useMemo(() => {
        let text = `This account was covered under SCRA until ${employeeProfileState.data?.scra_close_date}.`;

        if (!!employeeProfileState.data?.scra_open_date && !!employeeProfileState.data?.scra_close_date) {
            text = `This account was covered under SCRA ${employeeProfileState.data?.scra_open_date} to ${employeeProfileState.data?.scra_close_date}.`;
        } else if (!!employeeProfileState.data?.scra_open_date) {
            text = `This account is covered under SCRA from ${employeeProfileState.data?.scra_open_date}.`;
        }

        return text;
    }, [employeeProfileState.data]);

    return (
        <Box pb={2} data-testid='uw-loan-bankruptcy-alert'>
            <KasSwitch>
                <KasSwitchWhen condition={!!loanAlerts.bankruptcyAlert}>
                    <Alert
                        severity='error'
                        action={
                            <Button
                                variant='contained'
                                size='small'
                                onClick={() => confirmLoanAlert('bankruptcyAlert')}>
                                Acknowledge
                            </Button>
                        }>
                        <AlertTitle>This customer may be in bankruptcy</AlertTitle>
                        <p>
                            &quot;If you currently have an open bankruptcy case or have received a discharge
                            of this debt under bankruptcy law, this is for informational purposes only and is
                            not an attempt to collect a debt or a demand for payment&quot;
                        </p>
                        Do not provide any information on delinquency days, delinquent amounts or recommend
                        any action for future eligibility.
                    </Alert>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!loanAlerts.scraAlert}>
                    <Alert
                        severity='warning'
                        action={
                            <Button
                                variant='contained'
                                size='small'
                                onClick={() => confirmLoanAlert('scraAlert')}>
                                Acknowledge
                            </Button>
                        }>
                        {scraMessage}
                    </Alert>
                </KasSwitchWhen>
            </KasSwitch>
        </Box>
    );
};
