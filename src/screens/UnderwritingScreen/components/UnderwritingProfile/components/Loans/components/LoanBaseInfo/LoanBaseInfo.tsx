import React, {useMemo} from 'react';
import {KasCopyText, KasInfo, KasStrike} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import {UnderwritingLoanModel} from '@/screens/UnderwritingScreen/interfaces';

export const LoanBaseInfo = ({loan}: {loan: UnderwritingLoanModel}) => {
    const isStrike = useMemo(() => loan.loan_status === 'Void', [loan.loan_status]);

    return (
        <>
            <KasInfo label='ID'>
                <KasCopyText textToCopy={loan.gid}>
                    <KasStrike isStrike={isStrike}>{loan.gid}</KasStrike>
                </KasCopyText>
            </KasInfo>
            <KasInfo label='Status'>
                <KasStrike isStrike={isStrike}>{loan.loan_status}</KasStrike>
            </KasInfo>
            <KasInfo label='Opened'>
                <KasStrike isStrike={isStrike}>{loan.start_date}</KasStrike>
            </KasInfo>
            <KasInfo label='Closed'>
                <KasStrike isStrike={isStrike}>{loan.close_date || null}</KasStrike>
            </KasInfo>
            <KasInfo label='Amount'>
                <KasStrike isStrike={isStrike}>{toCurrency(loan.amount)}</KasStrike>
            </KasInfo>
            <KasInfo label='Installment'>
                <KasStrike isStrike={isStrike}>{toCurrency(loan.installment_amount)}</KasStrike>
            </KasInfo>
            <KasInfo label='Balance'>
                <KasStrike isStrike={isStrike}>{toCurrency(loan.balance)}</KasStrike>
            </KasInfo>
        </>
    );
};
