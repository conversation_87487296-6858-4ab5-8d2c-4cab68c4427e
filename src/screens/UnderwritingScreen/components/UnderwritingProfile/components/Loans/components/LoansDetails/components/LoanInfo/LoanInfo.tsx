import './styles.scss';

import React, {useEffect, useMemo, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Grid2} from '@mui/material';
import {KasExpandIcon, KasLoading, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, LoanDetailsModel} from '@/interfaces';
import {LoanInfoDetails, LoanInfoPreview, LoanInfoTabDetails} from './components';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {LoanInfoTabs, LoanLitigationAlert, LoanRetainedAlert} from '@/views/loan';
import {UnderwritingLoanModel} from '@/screens/UnderwritingScreen/interfaces';

interface LoanInfoProps {
    loan: UnderwritingLoanModel;
    expandedLoan?: boolean;
}

export const LoanInfo = ({loan, expandedLoan = false}: LoanInfoProps) => {
    const {loansState} = useUnderwritingLoans();
    const [expanded, setExpanded] = useState(expandedLoan);
    const [loanState, setLoanState] = useState(getDefaultState<LoanDetailsModel>());

    const loadData = async () => {
        setLoanState(getLoadingState(loanState));
        const response: Completable<LoanDetailsModel> = await apiRequest(
            `/api/secured/underwriting/loans/${loan.gid}`,
        );
        setLoanState(getLoadedState(response));
    };

    const isUpdateData = useMemo(
        () =>
            !loan.litigation &&
            ((expanded && !loanState.data && !loanState.loading) || (loansState.loading && loanState.data)),
        [expanded, loansState.loading, loan],
    );

    const showRetainedAlert = useMemo(
        () => loanState.data?.retained && !!loanState.data.debt_validation_sent_date,
        [loanState.data],
    );

    useEffect(() => {
        if (isUpdateData) {
            loadData().then();
        }
    }, [isUpdateData]);

    if (loan.litigation) {
        return <LoanLitigationAlert />;
    }

    return (
        <div className='kas-underwriting-loan-info'>
            <Accordion disableGutters expanded={expanded} color={'error'} elevation={0}>
                <AccordionSummary
                    component='div'
                    aria-controls={loan.gid.toString()}
                    id={loan.gid.toString()}
                    expandIcon={
                        <KasExpandIcon
                            expanded={true}
                            onClick={() => setExpanded(!expanded)}
                            testid='uw-loan-info'
                        />
                    }>
                    <LoanInfoPreview loan={loan} data={loanState.data} />
                </AccordionSummary>
                <AccordionDetails data-testid='uw-loan-info-details'>
                    <div className='kas-underwriting-loan-info__details'>
                        <KasSwitch>
                            <KasSwitchWhen condition={loanState.loading && !loanState.data}>
                                <KasLoading />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!loanState.error}>
                                <KasLoadingError error={loanState.error} onTryAgain={loadData} />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!loanState.data}>
                                {loanState.data && (
                                    <Grid2 container rowSpacing={3} px={2}>
                                        {showRetainedAlert && (
                                            <Grid2 size={12}>
                                                <LoanRetainedAlert />
                                            </Grid2>
                                        )}
                                        <Grid2 size={12}>
                                            <LoanInfoDetails data={loanState.data} />
                                        </Grid2>
                                        <Grid2 size={12}>
                                            <LoanInfoTabs loan={loanState.data} employeeId={loan.employee_id}>
                                                <LoanInfoTabDetails />
                                            </LoanInfoTabs>
                                        </Grid2>
                                    </Grid2>
                                )}
                            </KasSwitchWhen>
                        </KasSwitch>
                    </div>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
