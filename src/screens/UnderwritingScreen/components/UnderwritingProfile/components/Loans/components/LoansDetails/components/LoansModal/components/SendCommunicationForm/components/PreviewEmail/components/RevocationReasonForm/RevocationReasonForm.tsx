import './styles.scss';

import React, {useState} from 'react';
import {KasSearchAutocompleteSelect} from '@/components';
import {SelectModel} from '@/interfaces';
import {Typography, Button, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {UnderwritingLoanEmailPreviewModel} from '@/screens/UnderwritingScreen/interfaces';

const REVOCATION_REASON_OPTIONS: SelectModel<string>[] = [
    {
        id: '',
        value: '',
        label: '-',
    },
    {
        id: 'OPEN_BANKRUPTY',
        value: 'OPEN_BANKRUPTY',
        label: 'Open bankruptcy',
    },
    {
        id: 'DEBT_COMPANY',
        value: 'DEBT_COMPANY',
        label: 'Working with debt relief company',
    },
    {
        id: 'TERMINATED',
        value: 'TERMINATED',
        label: 'No longer at employer',
    },
    {
        id: 'ALTERNATIVE_METHOD',
        value: 'ALTERNATIVE_METHOD',
        label: 'Pay through alternative method',
    },
];

interface RevocationReasonFormProps {
    disabled?: boolean;
    onApply: (data: Partial<UnderwritingLoanEmailPreviewModel>) => void;
}

export const RevocationReasonForm = ({disabled = false, onApply}: RevocationReasonFormProps) => {
    const [selectedOption, setSelectedOption] = useState<SelectModel<string> | null>(null);

    const onSubmit = async () => {
        const options: Partial<UnderwritingLoanEmailPreviewModel> = {
            revocation_reason: selectedOption?.value,
        };

        onApply(options);
    };

    const formik = useFormik({
        initialValues: {
            revocation_reason: '',
        },
        onSubmit,
    });

    return (
        <form className='kas-underwriting-revocation-reason-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <Typography variant='subtitle1'>Revocation Reason</Typography>
                </Grid2>
                <Grid2 size={3}>
                    <KasSearchAutocompleteSelect<string>
                        value={selectedOption}
                        label='Select Reason'
                        disabled={disabled}
                        placeholder='Select Reason'
                        options={REVOCATION_REASON_OPTIONS}
                        onSelect={setSelectedOption}
                    />
                </Grid2>
                {selectedOption && (
                    <>
                        <Grid2 size={1.5}>
                            <Button
                                variant='outlined'
                                fullWidth
                                size='small'
                                type='submit'
                                disabled={disabled || selectedOption?.value === ''}>
                                Apply
                            </Button>
                        </Grid2>
                    </>
                )}
            </Grid2>
        </form>
    );
};
