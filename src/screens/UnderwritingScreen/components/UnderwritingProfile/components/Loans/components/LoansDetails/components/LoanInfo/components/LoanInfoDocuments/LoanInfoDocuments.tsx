import React from 'react';
import {Stack} from '@mui/material';
import {LibraryBooks, Theaters} from '@mui/icons-material';
import {DownloadCell} from '@/components/table/cells';

export const LoanInfoDocuments = ({loanId}: {loanId: number}) => {
    const loanParams = {
        path: `/secured/uw/profile/loan/${loanId}/pdf`,
    };
    const disclosureParams = {
        path: `/secured/common/download/loan/${loanId}`,
        params: {
            t: 'CREDIT_DISCLOSURE',
        },
    };
    return (
        <Stack direction='row' spacing={1}>
            <DownloadCell
                testid='uw-loan-info-documents-tape'
                params={JSON.stringify(loanParams)}
                Icon={<Theaters color='primary' titleAccess='Loan Tape' />}
            />
            <DownloadCell
                testid='uw-loan-info-documents-disclosure'
                params={JSON.stringify(disclosureParams)}
                Icon={<LibraryBooks color='primary' titleAccess='Credit Disclosure' />}
            />
        </Stack>
    );
};
