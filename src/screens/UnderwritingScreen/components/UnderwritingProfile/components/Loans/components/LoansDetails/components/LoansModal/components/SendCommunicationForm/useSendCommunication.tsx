import React, {createContext, useContext, useMemo, useState} from 'react';
import {SelectModel} from '@/interfaces';
import {CommunicationType} from './interfaces';
import {LoanDetailsModel} from '@/interfaces';
import {usePrint} from '@/hooks/usePrint';

interface SendCommunicationContextModel extends ReturnType<typeof usePrint> {
    sendOptions: SelectModel<string>[];
    loan: LoanDetailsModel;
    communicationType: CommunicationType;
    changeCommunicationType: (value: CommunicationType) => void;
    selectedTypeOption: SelectModel<string> | null;
    changeSelectedTypeOption: (value: SelectModel<string> | null) => void;
}

const SendCommunicationContext = createContext<SendCommunicationContextModel | undefined>(undefined);

interface SendCommunicationProviderProps {
    children: React.ReactNode;
    loan: LoanDetailsModel;
}

export const SendCommunicationProvider: React.FC<SendCommunicationProviderProps> = ({children, loan}) => {
    const printMethods = usePrint();
    const [communicationType, setCommunicationType] = useState<CommunicationType>(CommunicationType.Email);
    const [selectedTypeOption, setSelectedTypeOption] = useState<SelectModel<string> | null>(null);

    const sendEmailOptions = useMemo(() => {
        const result: SelectModel<string>[] = [
            {
                id: 'incorrect-deduction',
                label: 'Incorrect Deduction',
                value: 'incorrect-deduction',
            },
            {
                id: 'status',
                label: 'Status',
                value: 'status',
            },
            {
                id: 'revocation',
                label: 'Revocation',
                value: 'revocation',
            },
        ];

        if (loan.delinquent) {
            result.unshift({
                id: 'delinquency',
                label: 'Delinquency',
                value: 'delinquency',
            });
        }

        if (!loan.paid) {
            result.unshift(
                {
                    id: 'early-repayment',
                    label: 'Early Repayment',
                    value: 'early-repayment',
                },
                {
                    id: 'early-installment',
                    label: 'Early Installment',
                    value: 'early-installment',
                },
            );
        }

        return result;
    }, [loan]);

    const sendLetterOptions = useMemo(() => {
        const result: SelectModel<string>[] = [
            {
                id: 'information',
                label: 'Information',
                value: 'information',
            },
        ];

        if (loan.open) {
            result.unshift(
                {
                    id: 'early-payment',
                    label: 'Early Payment',
                    value: 'early-payment',
                },
                {
                    id: 'settlement',
                    label: 'Settlement',
                    value: 'settlement',
                },
                {
                    id: 'delinquency',
                    label: 'Delinquency',
                    value: 'delinquency',
                },
            );
        }

        return result;
    }, [loan]);

    const sendOptions = useMemo(() => {
        switch (communicationType) {
            case CommunicationType.Email:
                return sendEmailOptions;
            case CommunicationType.Letter:
                return sendLetterOptions;
            default:
                return [];
        }
    }, [communicationType]);

    const changeCommunicationType = (value: CommunicationType) => {
        setCommunicationType(value);
        setSelectedTypeOption(null);
    };

    const changeSelectedTypeOption = (value: SelectModel<string> | null) => {
        setSelectedTypeOption(value);
    };

    const value: SendCommunicationContextModel = {
        sendOptions,
        loan,
        communicationType,
        changeCommunicationType,
        selectedTypeOption,
        changeSelectedTypeOption,
        ...printMethods,
    };

    return <SendCommunicationContext.Provider value={value}>{children}</SendCommunicationContext.Provider>;
};

export function useSendCommunication() {
    const context = useContext(SendCommunicationContext);
    if (!context) {
        throw new Error('useSendCommunication must be used within SendCommunicationProvider');
    }
    return context;
}
