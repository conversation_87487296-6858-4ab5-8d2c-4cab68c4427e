import React from 'react';
import {Alert} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasSwitch, KasSwitchWhen} from '@/components';

export const LoansPreviewAlerts = () => {
    const {loanAlerts} = useUnderwritingProfile();

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!!loanAlerts.bankruptcyAlert}>
                <Alert severity='error' sx={{marginY: -1}}>
                    This customer may be in bankruptcy!
                </Alert>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!loanAlerts.scraAlert}>
                <Alert severity='warning' sx={{marginY: -1}}>
                    This account was or is covered under SCRA!
                </Alert>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
