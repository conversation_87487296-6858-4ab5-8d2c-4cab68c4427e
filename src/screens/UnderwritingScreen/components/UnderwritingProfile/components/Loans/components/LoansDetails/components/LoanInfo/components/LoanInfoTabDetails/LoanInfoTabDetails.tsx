import React, {useEffect, useMemo} from 'react';
import {AlternativePaymentTableColumns, RepaymentScheduleTableColumns} from './tables';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import Box from '@mui/material/Box';
import {
    LoanAlternativePaymentTable,
    LoanAlternativeScheduleTable,
    LoanCreditReportingTable,
    LoanDelinquencyTable,
    LoanInfoTabType,
    LoanRepaymentScheduleTable,
    LoanTransactionsTable,
    useLoanInfoTabs,
} from '@/views/loan';
import {useSecured} from '@/hooks/useSecured';
import {KasInfo} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import {Stack} from '@mui/material';

export const LoanInfoTabDetails = () => {
    const {hasAnyRole} = useSecured();
    const {
        loan,
        activeTab,
        repaymentScheduleState,
        loadRepaymentScheduleData,
        alternativeScheduleState,
        loadAlternativeScheduleData,
        alternativePaymentState,
        loadAlternativePaymentData,
        creditReportingState,
        loadCreditReportingData,
        delinquencyState,
        loadDelinquencyData,
        transactionsState,
        loadTransactionsData,
    } = useLoanInfoTabs();
    const {loansState} = useUnderwritingLoans();

    const onRefresh = () => {
        if (repaymentScheduleState.data || repaymentScheduleState.error) {
            loadRepaymentScheduleData().then();
        }
        if (alternativeScheduleState.data || alternativeScheduleState.error) {
            loadAlternativeScheduleData().then();
        }
        if (alternativePaymentState.data || alternativePaymentState.error) {
            loadAlternativePaymentData().then();
        }
        if (creditReportingState.data || creditReportingState.error) {
            loadCreditReportingData().then();
        }
        if (delinquencyState.data || delinquencyState.error) {
            loadDelinquencyData().then();
        }
    };

    const repaymentScheduleColumns = useMemo(() => {
        const showFullTable = hasAnyRole(['KASH_COMPLIANCE']);

        return !showFullTable ? RepaymentScheduleTableColumns : undefined;
    }, [hasAnyRole]);

    useEffect(() => {
        if (!loansState.loading) {
            onRefresh();
        }
    }, [loansState.loading]);

    return (
        <Box pt={2}>
            {activeTab === LoanInfoTabType.Repayment_Schedule && (
                <LoanRepaymentScheduleTable
                    state={repaymentScheduleState}
                    loadData={loadRepaymentScheduleData}
                    hideBackDropLoading={loansState.loading}
                    columns={repaymentScheduleColumns}
                />
            )}
            {activeTab === LoanInfoTabType.Alternative_Schedule && (
                <LoanAlternativeScheduleTable
                    state={alternativeScheduleState}
                    loadData={loadAlternativeScheduleData}
                    hideBackDropLoading={loansState.loading}
                />
            )}
            {activeTab === LoanInfoTabType.Alternative_Payment && (
                <LoanAlternativePaymentTable
                    state={alternativePaymentState}
                    loadData={loadAlternativePaymentData}
                    hideBackDropLoading={loansState.loading}
                    columns={AlternativePaymentTableColumns}
                    tableActions={
                        <Stack justifyContent='center' pl={1}>
                            <KasInfo label='Installment Amount:' isInline>
                                {toCurrency(loan.installment_amount)}
                            </KasInfo>
                        </Stack>
                    }
                />
            )}
            {activeTab === LoanInfoTabType.Credit_Reporting && (
                <LoanCreditReportingTable
                    state={creditReportingState}
                    loadData={loadCreditReportingData}
                    hideBackDropLoading={loansState.loading}
                    loanId={loan.gid}
                />
            )}
            {activeTab === LoanInfoTabType.Delinquency && (
                <LoanDelinquencyTable
                    state={delinquencyState}
                    loadData={loadDelinquencyData}
                    hideBackDropLoading={loansState.loading}
                />
            )}
            {activeTab === LoanInfoTabType.Transactions && (
                <LoanTransactionsTable
                    state={transactionsState}
                    loadData={loadTransactionsData}
                    hideBackDropLoading={loansState.loading}
                />
            )}
        </Box>
    );
};
