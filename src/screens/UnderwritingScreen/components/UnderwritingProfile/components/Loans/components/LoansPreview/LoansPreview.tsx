import './styles.scss';

import React, {useMemo} from 'react';
import {Stack} from '@mui/material';
import {useUnderwritingLoans} from './../../../../components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {LoanBaseInfo} from './../../components';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {LoansPreviewAlerts, LoansPreviewLoading} from './components';

export const LoansPreview = () => {
    const {profile, loanAlerts} = useUnderwritingProfile();
    const {loansState} = useUnderwritingLoans();

    const currentLoan = useMemo(
        () => loansState.data?.find(({gid}) => gid === profile.current_loan_id),
        [loansState.data],
    );

    if (loansState.error) {
        return null;
    }

    return (
        <div className='kas-underwriting-loans-preview' data-testid='uw-loans-preview'>
            <KasSwitch>
                <KasSwitchWhen condition={!!loanAlerts.bankruptcyAlert || !!loanAlerts.scraAlert}>
                    <LoansPreviewAlerts />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!loansState.data}>
                    <LoansPreviewLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!currentLoan}>
                    {currentLoan && (
                        <Stack direction='row' spacing={4}>
                            <LoanBaseInfo loan={currentLoan} />
                        </Stack>
                    )}
                </KasSwitchWhen>
            </KasSwitch>
        </div>
    );
};
