import React, {ChangeEvent, useEffect, useState} from 'react';
import {Checkbox, FormControlLabel, Typography} from '@mui/material';
import {RecipientDTO} from '@/models';

interface LetterRecipientsProps {
    recipients: RecipientDTO[];
    onChange: (value: RecipientDTO[]) => void;
}

export const LetterRecipients = ({recipients, onChange}: LetterRecipientsProps) => {
    const [selectedRecipients, setSelectedRecipients] = useState<RecipientDTO[]>(recipients);

    const getRecipientAddress = (value: RecipientDTO): string => {
        return `${value.address_1 || ''} ${value.address_2 || ''} ${value.city || ''} ${value.state || ''}, ${value.zip || ''}`;
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = Number(event.target.value);
        const recipient = recipients.find((item) => item.address_id === value);

        if (!recipient) return;

        if (selectedRecipients.some((item) => item.address_id === value)) {
            setSelectedRecipients(selectedRecipients.filter((item) => item.address_id !== value));
        } else {
            setSelectedRecipients([...selectedRecipients, recipient]);
        }
    };

    useEffect(() => {
        onChange(selectedRecipients);
    }, [selectedRecipients]);

    return (
        <>
            <Typography variant='subtitle1'>Recipients</Typography>
            {recipients.map((item, index) => (
                <div key={index}>
                    <FormControlLabel
                        label={getRecipientAddress(item)}
                        control={
                            <Checkbox
                                size='small'
                                value={item.address_id}
                                checked={selectedRecipients.some(
                                    (selected) => selected.address_id === item.address_id,
                                )}
                                onChange={handleChangeRecipients}
                                inputProps={{'aria-label': 'controlled'}}
                            />
                        }
                    />
                </div>
            ))}
        </>
    );
};
