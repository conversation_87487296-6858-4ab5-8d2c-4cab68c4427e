import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {Stack} from '@mui/material';
import {CancelAchButton} from '../components';
import {toCurrency} from '@/utils/FormatUtils';
import {KasFlaggedIcon} from '@/components';
import React from 'react';
import {LoanAlternativePaymentModel} from '@/interfaces';

const columnHelper = createColumnHelper<LoanAlternativePaymentModel>();

const _defaultInfoColumn = defaultInfoColumn<LoanAlternativePaymentModel>;
const _defaultAmountColumn = defaultAmountColumn<LoanAlternativePaymentModel>;

export const AlternativePaymentTableColumns = [
    _defaultAmountColumn('payment_amount', 'Amount', false),
    columnHelper.accessor('active', {
        id: 'active',
        header: 'Status',
        cell: (props) => {
            const {cleared_date, active} = props.row.original;

            if (active) {
                return 'ACTIVE';
            } else if (cleared_date) {
                return 'COMPLETED';
            } else {
                return 'CANCELLED';
            }
        },
    }),
    columnHelper.accessor('payment_type', {
        id: 'payment_type',
        header: 'Type',
        cell: (props) => {
            const {payment_type, payment_code} = props.row.original;

            return `${payment_type} ${payment_code ? ` (${payment_code})` : ''}`;
        },
    }),
    _defaultInfoColumn('process_date', 'Processed'),
    columnHelper.accessor('schedule', {
        id: 'schedule',
        header: 'Schedule',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
        enableSorting: false,
    }),
    _defaultInfoColumn('create_time', 'Created'),
    _defaultInfoColumn('payment_mode', 'Payment'),
    columnHelper.accessor('payoff_amount', {
        id: 'payoff_amount',
        header: 'Payoff Amount',
        cell: (props) => {
            const {payoff_until, payoff_amount} = props.row.original;

            return <abbr title={`until ${payoff_until}`}>{toCurrency(payoff_amount)}</abbr>;
        },
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<LoanAlternativePaymentModel, string>) => {
            const {gid, loan_id, process_date, active} = props.row.original;

            if (process_date || !active) {
                return null;
            }

            return (
                <Stack direction='row' justifyContent='center' spacing={1}>
                    <CancelAchButton loanId={loan_id} paymentId={gid} />
                </Stack>
            );
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
] as ColumnDef<LoanAlternativePaymentModel, unknown>[];
