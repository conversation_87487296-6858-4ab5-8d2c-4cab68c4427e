import React, {PropsWithChildren} from 'react';
import {Divider, useTheme, Grid2} from '@mui/material';
import {KasInfo} from '@/components';
import {LoanDetailedCollection} from '@/interfaces';

const LoanDetailedCollectionsValue = ({children}: PropsWithChildren) => {
    const {palette} = useTheme();

    return <span style={{color: palette.warning.main}}>{children}</span>;
};

export const LoanDetailedCollections = ({data}: {data: LoanDetailedCollection[]}) => {
    if (!data.length) {
        return null;
    }

    return (
        <>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            {data.map((item, i) => (
                <Grid2 key={i} size={12} container spacing={2}>
                    <Grid2 size={4}>
                        <KasInfo label='Coll. Agency:' isInline>
                            <LoanDetailedCollectionsValue>
                                {item.collection_agency_name}
                            </LoanDetailedCollectionsValue>
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={4}>
                        <KasInfo label='Coll. Agency Date:' isInline>
                            <LoanDetailedCollectionsValue>
                                {item.collection_agency_date}
                            </LoanDetailedCollectionsValue>
                        </KasInfo>
                    </Grid2>
                    <Grid2 size={4}>
                        <KasInfo label='Coll. Agency Closed Date:' isInline>
                            <LoanDetailedCollectionsValue>
                                {item.collection_agency_close_date || 'OPEN'}
                            </LoanDetailedCollectionsValue>
                        </KasInfo>
                    </Grid2>
                </Grid2>
            ))}
        </>
    );
};
