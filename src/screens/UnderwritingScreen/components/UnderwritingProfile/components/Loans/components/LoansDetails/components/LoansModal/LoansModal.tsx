import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingLoanModal} from './../../../../interfaces';
import {PayoffLoanForm, SendCommunicationForm} from './components';
import {SendCommunicationProvider} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Loans/components/LoansDetails/components/LoansModal/components/SendCommunicationForm/useSendCommunication';
import { BackupAchPauseClearForm } from './components/BackupAchPauseClearForm /BackupAchPauseClearForm';
import { BackupAchPauseFlagForm } from './components/BackupAchPauseFlagForm/BackupAchPauseFlagForm';
 
export const LoansModal = () => {
    const {openLoanModal, setOpenLoanModal} = useUnderwritingLoans();

    const title = useMemo(() => {
        switch (openLoanModal?.type) {
            case UnderwritingLoanModal.Payoff_Loan:
                return `Payoff for Loan #: ${openLoanModal.props.loan.gid}`;
            case UnderwritingLoanModal.Send:
                return 'Send Communication';
            case UnderwritingLoanModal.Backup_Ach_Pause_Flag:
                return 'Flag backup ach pause date';
            case UnderwritingLoanModal.Backup_Ach_Pause_Clear:
                    return 'Clear backup ach pause date';
            default:
                return '';
        }
    }, [openLoanModal]);

    const renderModalContent = useMemo(() => {
        switch (openLoanModal?.type) {
            case UnderwritingLoanModal.Payoff_Loan:
                return <PayoffLoanForm {...openLoanModal.props} />;
            case UnderwritingLoanModal.Send:
                return (
                    <SendCommunicationProvider loan={openLoanModal.props.loan}>
                        <SendCommunicationForm {...openLoanModal.props} />
                    </SendCommunicationProvider>
                );
            case UnderwritingLoanModal.Backup_Ach_Pause_Clear:
                return <BackupAchPauseClearForm {...openLoanModal.props} />;
            case UnderwritingLoanModal.Backup_Ach_Pause_Flag:                
                return <BackupAchPauseFlagForm {...openLoanModal.props} />;
            default:
                return null;
        }
    }, [openLoanModal?.type]);

    return (
        <KasModal
            data-testid='uw-loans'
            size={openLoanModal?.type === UnderwritingLoanModal.Send ? 'medium' : 'small'}
            title={title}
            open={!!openLoanModal}
            onClose={() => setOpenLoanModal(null)}>
            {renderModalContent}
        </KasModal>
    );
};
