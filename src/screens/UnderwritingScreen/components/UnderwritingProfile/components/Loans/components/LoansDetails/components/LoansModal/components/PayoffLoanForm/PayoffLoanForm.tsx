import React, {useState} from 'react';
import {KasDatePickerFormField, KasInfo, KasModalFooter} from '@/components';
import {Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {PayoffLoanFormValues, validationSchema} from './schema';
import {PayoffLoanActionProps} from './../../../../../../interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingLoanPayoffModel} from '@/screens/UnderwritingScreen/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {toCurrency} from '@/utils/FormatUtils';
import dayjs from 'dayjs';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const PayoffLoanForm = ({loan, onClose}: PayoffLoanActionProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [data, setData] = useState<UnderwritingLoanPayoffModel>({
        loan_id: loan.gid,
        amount: '',
        until_date: '',
    });

    const onSubmit = async (values: PayoffLoanFormValues) => {
        const date = dayjs(values.date).format('YYYYMMDD');
        const url = `/api/secured/underwriting/loans/${loan.gid}/payoff?date=${date}`;

        setSubmitting(true);

        const response = await apiRequest(url);

        if (response.value) {
            setData(response.value);
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: loan.disbursement_date,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasInfo label='Amount:' isInline>
                        {data.amount ? toCurrency(data.amount) : null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={6}>
                    <KasInfo label='Until Date:' isInline>
                        {data.until_date || null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={12}>
                    <KasDatePickerFormField
                        minDate={dayjs(loan.disbursement_date)}
                        formik={formik}
                        name='date'
                        label='As of Date'
                        format='YYYY-MM-DD'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Submit'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
