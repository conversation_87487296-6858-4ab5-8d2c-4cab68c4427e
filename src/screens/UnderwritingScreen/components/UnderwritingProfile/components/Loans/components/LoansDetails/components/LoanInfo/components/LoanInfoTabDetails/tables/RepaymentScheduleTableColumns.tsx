import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {ColumnDef, createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasFlaggedIcon} from '@/components';
import {LoanRepaymentScheduleModel} from '@/interfaces';

const columnHelper = createColumnHelper<LoanRepaymentScheduleModel>();

const _defaultInfoColumn = defaultInfoColumn<LoanRepaymentScheduleModel>;
const _defaultAmountColumn = defaultAmountColumn<LoanRepaymentScheduleModel>;

export const RepaymentScheduleTableColumns = [
    _defaultInfoColumn('date', 'Date'),
    _defaultAmountColumn('total', 'Amount', false),
    columnHelper.accessor('deduction', {
        id: 'deduction',
        header: 'Deduction',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
        enableSorting: false,
        meta: {
            notExport: true,
        },
    }),
] as ColumnDef<LoanRepaymentScheduleModel, unknown>[];
