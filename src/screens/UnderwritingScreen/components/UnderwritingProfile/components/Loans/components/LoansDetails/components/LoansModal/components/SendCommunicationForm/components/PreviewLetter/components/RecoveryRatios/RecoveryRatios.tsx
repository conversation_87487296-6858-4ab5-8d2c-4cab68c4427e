import React, {useEffect, useState} from 'react';
import {KasSearchAutocompleteSelect} from '@/components';
import {SelectModel} from '@/interfaces';

interface RecoveryRatiosProps {
    options: SelectModel<number>[];
    onChange: (value: number | null) => void;
}

export const RecoveryRatios = ({options, onChange}: RecoveryRatiosProps) => {
    const [selectedOption, setSelectedOption] = useState<SelectModel<number> | null>(null);

    useEffect(() => {
        onChange(selectedOption?.value || null);
    }, [selectedOption]);

    return (
        <KasSearchAutocompleteSelect<number>
            value={selectedOption}
            label='Select Recovery Percent'
            placeholder='Select Recovery Percent'
            options={options}
            onSelect={(value) => setSelectedOption(value)}
        />
    );
};
