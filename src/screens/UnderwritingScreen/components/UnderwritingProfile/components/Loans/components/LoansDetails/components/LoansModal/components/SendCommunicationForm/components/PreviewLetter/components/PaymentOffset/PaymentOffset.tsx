import React, {useEffect, useState} from 'react';
import {KasSearchAutocompleteSelect} from '@/components';
import {SelectModel} from '@/interfaces';

const PAYMENT_OFFSET_OPTIONS: SelectModel<number>[] = [
    {
        id: '15',
        value: 15,
        label: '15 days',
    },
    {
        id: '30',
        value: 30,
        label: '30 days',
    },
];

interface PaymentOffsetProps {
    onChange: (value: number | null) => void;
}

export const PaymentOffset = ({onChange}: PaymentOffsetProps) => {
    const [selectedOption, setSelectedOption] = useState<SelectModel<number> | null>(null);

    useEffect(() => {
        onChange(selectedOption?.value || null);
    }, [selectedOption]);

    return (
        <KasSearchAutocompleteSelect<number>
            value={selectedOption}
            label='Select Payment Offset'
            placeholder='Select Payment Offset'
            options={PAYMENT_OFFSET_OPTIONS}
            onSelect={(value) => setSelectedOption(value)}
        />
    );
};
