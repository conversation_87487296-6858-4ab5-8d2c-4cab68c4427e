import './styles.scss';

import React from 'react';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {LoanDetailsAlerts, LoanInfo, LoansModal} from './components';
import {KasLoading, KasLoadingBackDrop, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {NoResultsView} from '@/views';

export const LoansDetails = () => {
    const {profile, employeeProfileState, loanAlerts} = useUnderwritingProfile();
    const {
        loansState: {loading, data, error},
        loadLoansData,
    } = useUnderwritingLoans();

    return (
        <div className='kas-underwriting-loans-details' data-testid='uw-loans-details'>
            <div className='kas-underwriting-loans-details__list'>
                <KasSwitch>
                    <KasSwitchWhen condition={(loading && !data) || employeeProfileState.loading}>
                        <KasLoading style={{marginBottom: '16px'}} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!error}>
                        <KasLoadingError view='contained' error={error} onTryAgain={loadLoansData} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!data && !!employeeProfileState.data}>
                        {loading && <KasLoadingBackDrop />}
                        <KasSwitch>
                            <KasSwitchWhen condition={!!loanAlerts.bankruptcyAlert || !!loanAlerts.scraAlert}>
                                <LoanDetailsAlerts />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!data?.length}>
                                <NoResultsView text='This user doesn’t have any loans.' />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={true}>
                                {data?.map((item) => (
                                    <LoanInfo
                                        key={item.gid}
                                        loan={item}
                                        expandedLoan={item.gid === profile.current_loan_id}
                                    />
                                ))}
                            </KasSwitchWhen>
                        </KasSwitch>
                    </KasSwitchWhen>
                </KasSwitch>
            </div>
            <LoansModal />
        </div>
    );
};
