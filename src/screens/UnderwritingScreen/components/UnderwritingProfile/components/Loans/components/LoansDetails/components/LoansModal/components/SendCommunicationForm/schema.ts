import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    payload: Yup.string().required(DEFAULT_VALIDATION_MSG),
    recipients: Yup.array().required(DEFAULT_VALIDATION_MSG).min(1, 'The array must have at least one item'),
    sendable: Yup.boolean(),
});

export type SendCommunicationFormValues = Yup.Asserts<typeof validationSchema>;
