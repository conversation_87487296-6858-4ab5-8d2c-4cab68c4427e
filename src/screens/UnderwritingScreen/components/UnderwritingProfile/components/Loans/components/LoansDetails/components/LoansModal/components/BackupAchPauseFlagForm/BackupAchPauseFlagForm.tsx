import React, {useState} from 'react';
import {<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ield, KasModalFooter} from '@/components';
import {Grid2 ,TextField, Checkbox, FormControlLabel} from '@mui/material';
import {useFormik} from 'formik';
import {FlagBackupACHPauseFormValues, validationSchema} from './schema';
import {PayoffLoanActionProps} from '../../../../../../interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import dayjs from 'dayjs';
import { useUnderwritingProfile } from '@/screens/UnderwritingScreen/components/UnderwritingProfile/useUnderwritingProfile';
import { useUnderwritingLoans } from '../../../../../../useUnderwritingLoans';

export const BackupAchPauseFlagForm = ({loan, onClose}: PayoffLoanActionProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const {onSubmitAction} = useUnderwritingProfile();
    const {loadLoansData } = useUnderwritingLoans();


    const onSubmit = async (values: FlagBackupACHPauseFormValues) => {
        const url = `/api/secured/underwriting/actions/backup-ach-pause-flag`;

        const body = JSON.stringify({
            entity_class: 'Loan',
            entity_id: loan.gid,
            comment: values.comment,
            pause_date: dayjs(values.pause_date).format('YYYYMMDD'),
            disabled: values.disabled
        });        

        setSubmitting(true);
        const response = await onSubmitAction(url, body, 'put');
        setSubmitting(false);

        if (response.value) {
            showMessage("Flagged backup ach loan pause date.", 'success');
            onClose();
            loadLoansData().then();
        } else {
            showMessage( response.error || "Failed to flag backup ach loan pause date");
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            pause_date: loan.backup_ach_pause_date || '',
            comment: '',
            disabled: false
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>                
                <Grid2 size={12}>
                    <KasDatePickerFormField
                        disablePast={true}
                        formik={formik}
                        name='pause_date'
                        label='Pause until Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={12}>
                     <TextField
                        fullWidth
                        size='small'
                        disabled={false}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <FormControlLabel
                        disabled={submitting}
                        label='Permanent Disable'
                        control={
                            <Checkbox
                                size='small'
                                name='disabled'
                                sx={{height: 28}}
                                onChange={formik.handleChange}
                                checked={formik.values.disabled}
                            />
                        }
                    />
                    <KasModalFooter
                        submitText='Submit'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
