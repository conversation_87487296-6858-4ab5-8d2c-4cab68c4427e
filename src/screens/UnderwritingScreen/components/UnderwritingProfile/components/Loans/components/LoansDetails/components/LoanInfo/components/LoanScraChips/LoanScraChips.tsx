import React, {useMemo} from 'react';
import {Chip, Stack, Typography} from '@mui/material';
import {ScraModificationModel} from '@/screens/UnderwritingScreen/interfaces';
import dayjs from 'dayjs';

interface LoanScraChipsProps {
    scras: ScraModificationModel[];
}

export const LoanScraChips = ({scras}: LoanScraChipsProps) => {
    const today = dayjs();

    const chips = useMemo(() => {
        return scras.map((dateRange, index) => {
            const startDate = dayjs(dateRange.start_date);
            const endDate = dateRange.end_date ? dayjs(dateRange.end_date) : null;
            
            const isActive = today.isAfter(startDate) && (!endDate || today.isBefore(endDate));
            const color: 'error' | 'warning' = isActive ? 'error' : 'warning';

            const label = endDate 
                ? `${startDate.format('MM/DD/YYYY')} - ${endDate.format('MM/DD/YYYY')}`
                : `${startDate.format('MM/DD/YYYY')} - Present`;

            return (
                <Chip
                    key={index}
                    label={label}
                    color={color}
                    size='small'
                    variant='outlined'
                />
            );
        });
    }, [scras, today]);

    if (!scras.length) {
        return null;
    }

    return (
        <Stack direction='row' spacing={1} alignItems='center'>
            {chips}
        </Stack>
    );
};
