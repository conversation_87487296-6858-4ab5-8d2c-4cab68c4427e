import React from 'react';
import {Stack} from '@mui/material';
import {UnderwritingLoanModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasInfo} from '@/components';
import {LoanInfoDocuments} from './../../components';
import {LoanBaseInfo} from './../../../../../../components';
import {LoanDetailsModel} from '@/interfaces';
import {DownloadLoanAgreements} from '@/views/loan';
import {LoanScraChips} from './../../components';

interface LoanInfoPreviewProps {
    loan: UnderwritingLoanModel;
    data: LoanDetailsModel | null;
}

export const LoanInfoPreview = ({loan, data}: LoanInfoPreviewProps) => {
    return (
        <Stack direction='row' spacing={3} px={1}>
            <LoanBaseInfo loan={loan} />
            <KasInfo label='Agreements'>
                <DownloadLoanAgreements loanId={loan.gid} backupAchActive={data?.backup_ach_active} />
            </KasInfo>
            <KasInfo label='Documents'>
                {loan.loan_status !== 'Void' ? <LoanInfoDocuments loanId={loan.gid} /> : null}
            </KasInfo>
            {loan.scra_modifications?.length > 0 && (
                <KasInfo label='SCRA'>
                    <LoanScraChips scras={loan.scra_modifications} />
                </KasInfo>
            )}
        </Stack>
    );
};
