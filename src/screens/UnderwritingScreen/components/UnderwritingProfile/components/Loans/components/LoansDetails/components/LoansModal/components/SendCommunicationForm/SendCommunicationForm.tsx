import './styles.scss';

import React, {useEffect, useMemo, useState, useRef} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Kas<PERSON>odalFooter, KasSearchAutocompleteSelect} from '@/components';
import {FormControl, FormControlLabel, Radio, RadioGroup, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {SendCommunicationFormValues, validationSchema} from './schema';
import {SendCommunicationLoanActionProps} from './../../../../../../interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useSendCommunication} from './useSendCommunication';
import {PreviewEmail, PreviewLetter} from './components';
import {CommunicationType} from './interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    UnderwritingLoanEmailPreviewModel,
    UnderwritingLoanLetterPreviewModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const SendCommunicationForm = ({onClose}: SendCommunicationLoanActionProps) => {
    const {
        sendOptions,
        communicationType,
        changeCommunicationType,
        selectedTypeOption,
        changeSelectedTypeOption,
        handlePrint,
        handleEmailPrintContent
    } = useSendCommunication();
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [submitUrl, setSubmitUrl] = useState('');
    const [contentData, setContentData] = useState<string>('');
    const templateType = useRef<string>('');

    const communicationTypeName = useMemo(() => {
        switch (communicationType) {
            case CommunicationType.Email:
                return 'Send Email';
            case CommunicationType.Letter:
                return 'Send Letter';
            default:
                return '';
        }
    }, [communicationType]);

    const onSubmit = async (values: SendCommunicationFormValues) => {
        const url = `${submitUrl}preview=false`;

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'post', body: values.payload});

        if (response.code === 200) {
            showMessage('Sent successfully', 'success');
            onClose();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            payload: '',
            recipients: [],
            sendable: true,
        },
        onSubmit,
        validationSchema,
    });

    const changeEmailDataHandler = (value: UnderwritingLoanEmailPreviewModel | null, content: string) => {
        formik.setFieldValue('payload', value ? JSON.stringify(value) : '');
        formik.setFieldValue('recipients', value ? value.recipients : []);
        setContentData(content);
        templateType.current = value ? value.email_type : '';
        updateSubmitUrl(value?.frequency);
    };

    const changeLetterDataHandler = (value: UnderwritingLoanLetterPreviewModel | null) => {
        formik.setFieldValue('payload', value ? JSON.stringify(value) : '');
        formik.setFieldValue('recipients', value ? value.recipients : []);
        formik.setFieldValue('sendable', value ? value.sendable : true);
        templateType.current = value ? value.letter_type : '';
        updateSubmitUrl();
    };

    const updateSubmitUrl = (frequency?: string) => {
        setSubmitUrl(
            `/api/secured/underwriting/loans/${communicationType}/${selectedTypeOption?.value}?${frequency ? `frequency=${frequency}&` : ''}`,
        );
    };

    const handleCommunicationTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setContentData('');
        changeCommunicationType((event.target as HTMLInputElement).value as CommunicationType);
    };

    useEffect(() => {
        handleEmailPrintContent(contentData, formik.values.recipients, templateType.current);
    }, [formik.values.recipients, contentData]);     

    return (
        <div className='kas-underwriting-loan-send-communication-form'>
            <div className='kas-underwriting-loan-send-communication-form__content'>
                {submitting && <KasLoadingBackDrop />}
                <Grid2 container spacing={2} rowSpacing={2}>
                    <Grid2 size={12}>
                        <FormControl disabled={submitting}>
                            <RadioGroup
                                row
                                defaultValue={CommunicationType.Email}
                                onChange={handleCommunicationTypeChange}>
                                <FormControlLabel
                                    value={CommunicationType.Email}
                                    control={<Radio size='small' />}
                                    label='Send Email'
                                />
                                <FormControlLabel
                                    value={CommunicationType.Letter}
                                    control={<Radio size='small' />}
                                    label='Send Letter'
                                />
                            </RadioGroup>
                        </FormControl>
                    </Grid2>
                    <Grid2 size={6}>
                        <KasSearchAutocompleteSelect
                            value={selectedTypeOption}
                            label={communicationTypeName}
                            disabled={submitting}
                            autoFocus={true}
                            placeholder={communicationTypeName}
                            options={sendOptions}
                            onSelect={(value) => changeSelectedTypeOption(value)}
                        />
                    </Grid2>
                    <Grid2 size={12}>
                        {selectedTypeOption && (
                            <>
                                {communicationType === CommunicationType.Email && (
                                    <PreviewEmail onChange={changeEmailDataHandler} />
                                )}
                                {communicationType === CommunicationType.Letter && (
                                    <PreviewLetter onChange={changeLetterDataHandler} />
                                )}
                            </>
                        )}
                    </Grid2>
                </Grid2>
            </div>
            <form onSubmit={formik.handleSubmit}>
                <KasModalFooter
                    submitText={communicationTypeName}
                    disabled={submitting || !formik.isValid || !formik.values.sendable}
                    customDisable={submitting || !formik.isValid}
                    onCancel={onClose}
                    onCustomClick={handlePrint}
                    customButtonText='Print'
                />
            </form>
        </div>
    );
};
