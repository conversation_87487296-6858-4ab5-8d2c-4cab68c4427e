import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    disabled: Yup.boolean().required(DEFAULT_VALIDATION_MSG),
    pause_date: Yup.string()
      .nullable()
      .when('disabled', {
        is: false,
        then: (schema) => schema.required(DEFAULT_VALIDATION_MSG),
        otherwise: (schema) => schema.notRequired(),
      }),
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type FlagBackupACHPauseFormValues = Yup.Asserts<typeof validationSchema>;
