import React from 'react';
import {ClearBackupACHPauseFormValues} from './schema';
import {PayoffLoanActionProps} from '../../../../../../interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {KasCommentForm} from '@/components/KasGlobalModals/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/useUnderwritingProfile';
import {useUnderwritingLoans} from '../../../../../../useUnderwritingLoans';

export const BackupAchPauseClearForm = ({loan, onClose}: PayoffLoanActionProps) => {
    const {onSubmitAction} = useUnderwritingProfile();
    const {showMessage} = useSnackbar();
    const {loadLoansData} = useUnderwritingLoans();
    const onSubmit = async (values: ClearBackupACHPauseFormValues) => {
        const url = `/api/secured/underwriting/actions/backup-ach-pause-clear`;

        const body = JSON.stringify({
            entity_class: 'Loan',
            entity_id: loan.gid,
            comment: values.comment,
        });

        const response = await onSubmitAction(url, body, 'put');

        if (response.value) {
            showMessage('Cleared backup ach loan pause date.', 'success');
            onClose();
            loadLoansData().then();
        } else {
            showMessage(response.error || 'Failed to clear backup ach loan pause date');
        }
    };

    return <KasCommentForm onSubmit={onSubmit} onCancel={onClose} />;
};
