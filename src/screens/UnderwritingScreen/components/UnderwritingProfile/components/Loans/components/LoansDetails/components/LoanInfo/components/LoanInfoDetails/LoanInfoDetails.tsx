import React from 'react';
import {Divider, Tooltip, useTheme, Grid2} from '@mui/material';
import {KasInfo, KasLink} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import dayjs from 'dayjs';
import IconButton from '@mui/material/IconButton';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingLoanModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Loans/interfaces';
import {OpenInNew} from '@mui/icons-material';
import {LoanDetailedCollections, LoanInfoDetailsHead} from './components';
import {LoanDetailsModel} from '@/interfaces';
import EditIcon from '@mui/icons-material/Edit';
import {RetainedLoanNotice} from '@/views/loan';

export const LoanInfoDetails = ({data}: {data: LoanDetailsModel}) => {
    const {setOpenLoanModal} = useUnderwritingLoans();
    const {palette} = useTheme();

    const getColor = (condition: boolean) => (condition ? palette.success.main : palette.warning.main);

    return (
        <Grid2 container rowSpacing={2}>
            <Grid2 size={12}>
                <LoanInfoDetailsHead data={data} />
            </Grid2>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            <Grid2 size={12} container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Status:' isInline>
                        <span style={{color: getColor(data.loan_status === 'Open')}}>{data.loan_status}</span>
                        {data.loan_status === 'Open' && (
                            <span style={{color: getColor(data.payroll_deduction_status === 'Authorized')}}>
                                (Deductions {data.payroll_deduction_status})
                            </span>
                        )}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Purpose:' isInline>
                        {data.loan_purpose}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='State:' isInline>
                        {data.address_state}
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            {data.origination_source && (
                <>
                    <Grid2 size={12} container spacing={2}>
                        <Grid2 size={4}>
                            <KasInfo label='Origination Source:' isInline>
                                {data.origination_source} ({data.origination_id || ''})
                            </KasInfo>
                            <RetainedLoanNotice retained={data.retained} />
                            {data.retained && data.rolling_dlq_days_cur > 14 && (
                                <KasInfo label='Validation Letter Sent:'>
                                    {data.debt_validation_sent_date ? (
                                        <>
                                            , valid until:{' '}
                                            {dayjs(data.debt_validation_sent_date)
                                                .add(35, 'days')
                                                .format('YYYY-MM-DD')}
                                        </>
                                    ) : (
                                        'N/A'
                                    )}
                                </KasInfo>
                            )}
                        </Grid2>
                        <Grid2 size={4}>
                            <KasInfo label='Third Party Status:' isInline>
                                <abbr
                                    title={
                                        data.origination_external_status_time ||
                                        data.origination_last_update_time ||
                                        ''
                                    }>
                                    {data.origination_status}
                                </abbr>
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={4}>
                            <KasInfo label='Purchase Date:' isInline>
                                {data.purchase_date}
                            </KasInfo>
                        </Grid2>
                    </Grid2>
                    <Grid2 size={12}>
                        <Divider />
                    </Grid2>
                </>
            )}
            <Grid2 size={12} container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Amount:' isInline>
                        {toCurrency(data.amount)}
                    </KasInfo>
                    <KasInfo label='Term:' isInline>
                        {toCurrency(data.installment_amount)} x {data.installment_count} pymts ({data.term}{' '}
                        mo)
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Interest:' isInline>
                        {toCurrency(data.interest)}
                    </KasInfo>
                    <KasInfo label='Interest Rate:' isInline>
                        {data.interest_rate}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Origination Fee:' isInline>
                        {toCurrency(data.origination_fee)}
                    </KasInfo>
                    <KasInfo label='APR:' isInline>
                        {data.apr}
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            <Grid2 size={12} container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Loan Balance:' isInline>
                        <abbr title='Includes precomputed interest'>{toCurrency(data.balance)}</abbr>
                    </KasInfo>
                    <KasInfo label='Revenue Balance:' isInline>
                        <abbr title='Unpaid principal + accrued interest/fees'>
                            {toCurrency(data.revenue_balance)}
                        </abbr>
                    </KasInfo>
                    <KasInfo label='Running Balance:' isInline>
                        <abbr title='Balance of payables vs receivables'>
                            {toCurrency(data.running_balance)} (v2: {toCurrency(data.running_balance2)})
                        </abbr>
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Unpaid Principal:' isInline>
                        {toCurrency(data.principal_balance)}
                    </KasInfo>
                    <KasInfo label='Interest Accrued:' isInline>
                        {toCurrency(data.accrued_interest)}
                    </KasInfo>
                    <KasInfo label='Fees Accrued:' isInline>
                        {toCurrency(data.accrued_fee)}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Payoff Amount:' isInline>
                        <abbr title={`util ${data.payoff_until}`}>{toCurrency(data.payoff_amount)}</abbr>
                        <Tooltip title='Prior Payoff Amount'>
                            <IconButton
                                data-testid='uw-loan-info-details-payoff-amount'
                                size='small'
                                sx={{minHeight: 20}}
                                onClick={() =>
                                    setOpenLoanModal({
                                        type: UnderwritingLoanModal.Payoff_Loan,
                                        props: {
                                            loan: data,
                                            onClose: () => setOpenLoanModal(null),
                                        },
                                    })
                                }>
                                <OpenInNew fontSize='small' />
                            </IconButton>
                        </Tooltip>
                    </KasInfo>
                    <KasInfo label='Paid-to-date:' isInline>
                        {toCurrency(data.paid_amount)}
                    </KasInfo>
                    {data.refund_amount && data.refund_amount > 0 && (
                        <KasInfo label='Refund-to-date:' isInline>
                            {toCurrency(data.refund_amount)}
                        </KasInfo>
                    )}
                </Grid2>
            </Grid2>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            <Grid2 size={12} container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Application Date:' isInline>
                        {data.application_date}
                    </KasInfo>
                    <KasInfo label='Signature Date:' isInline>
                        {data.signature_date}
                    </KasInfo>
                    <KasInfo label='Start Date:' isInline>
                        {data.start_date}
                    </KasInfo>
                    <KasInfo label='Backup ACH Pause Until Date:' isInline>
                        {data.backup_ach_pause_date ? (
                            <>
                                <KasLink
                                    testid='uw-loan-profile-backupach'
                                    onClick={() =>
                                        setOpenLoanModal({
                                            type: UnderwritingLoanModal.Backup_Ach_Pause_Clear,
                                            props: {
                                                loan: data,
                                                onClose: () => setOpenLoanModal(null),
                                            },
                                        })
                                    }>
                                    {data.backup_ach_pause_date}
                                </KasLink>{' '}
                                <IconButton
                                    data-testid='uw-loan-profile-backupach-edit'
                                    onClick={() =>
                                        setOpenLoanModal({
                                            type: UnderwritingLoanModal.Backup_Ach_Pause_Flag,
                                            props: {
                                                loan: data,
                                                onClose: () => setOpenLoanModal(null),
                                            },
                                        })
                                    }>
                                    <EditIcon sx={{height: 16}} />
                                </IconButton>
                            </>
                        ) : data.backup_ach_active === false ? (
                            <KasLink
                                data-testid='uw-loan-profile-backupach-edit-clear'
                                onClick={() =>
                                    setOpenLoanModal({
                                        type: UnderwritingLoanModal.Backup_Ach_Pause_Clear,
                                        props: {
                                            loan: data,
                                            onClose: () => setOpenLoanModal(null),
                                        },
                                    })
                                }>
                                Backup ACH Disabled
                            </KasLink>
                        ) : (
                            <KasLink
                                data-testid='uw-loan-profile-backupach-edit'
                                onClick={() =>
                                    setOpenLoanModal({
                                        type: UnderwritingLoanModal.Backup_Ach_Pause_Flag,
                                        props: {
                                            loan: data,
                                            onClose: () => setOpenLoanModal(null),
                                        },
                                    })
                                }>
                                N/A
                            </KasLink>
                        )}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='Funding Date:' isInline>
                        {data.funding_date}
                    </KasInfo>
                    <KasInfo label='Disbursement Date:' isInline>
                        {data.disbursement_date}
                    </KasInfo>
                    <KasInfo label='Maturity Date:' isInline>
                        {data.maturity_date}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='First Pymt Date:' isInline>
                        {data.first_installment_date}
                    </KasInfo>
                    <KasInfo label='Last Pymt Date:' isInline>
                        {data.last_payment_date}
                    </KasInfo>
                    <KasInfo label='Close Date:' isInline>
                        {data.close_date ? (
                            <span style={{color: getColor(data.loan_status === 'Paid')}}>
                                {data.close_date}
                                {data.loan_prepaid ? '*' : null}
                            </span>
                        ) : null}
                    </KasInfo>
                    <KasInfo label='Backup ACH Pymt Status:' isInline>
                        {data.backup_ach_payment_status}
                    </KasInfo>
                </Grid2>
            </Grid2>
            <Grid2 size={12}>
                <Divider />
            </Grid2>
            <Grid2 size={12} container spacing={2}>
                <Grid2 size={4}>
                    <KasInfo label='Last Current Date:' isInline>
                        {data.rolling_dlq_date}
                    </KasInfo>
                    <KasInfo label='First Dlq Date (FCRA):' isInline>
                        {data.fcra_first_dlq_date}
                    </KasInfo>
                    <KasInfo label='SOL End Date:' isInline>
                        {data.statute_limit_end_date ? data.statute_limit_end_date + ' (' + data.current_address_state + ')' : null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='R-Dlq Days (current):' isInline>
                        {data.rolling_dlq2_days_cur}
                    </KasInfo>
                    <KasInfo label='R-Dlq Days (max):' isInline>
                        {data.rolling_dlq2_days_max}
                    </KasInfo>
                </Grid2>
                <Grid2 size={4}>
                    <KasInfo label='R-Dlq Amount (current):' isInline>
                        {toCurrency(data.rolling_dlq2_amount_cur)}
                    </KasInfo>
                    <KasInfo label='R-Dlq Amount (max):' isInline>
                        {toCurrency(data.rolling_dlq2_amount_max)}
                    </KasInfo>
                </Grid2>
            </Grid2>
            {data.loan_detailed_collections && (
                <LoanDetailedCollections data={data.loan_detailed_collections} />
            )}
        </Grid2>
    );
};
