import React from 'react';
import {<PERSON><PERSON>, Stack, Typography} from '@mui/material';
import {useUnderwritingLoans} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingLoanModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/Loans/interfaces';
import {CreditCard, Send} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {COLLECTIONS_HASH} from '@/constants';
import {LoanDetailsModel} from '@/interfaces';
import {GlobalModal, useGlobalModal} from '@/components';

export const LoanInfoDetailsHead = ({data}: {data: LoanDetailsModel}) => {
    const {showGlobalModal} = useGlobalModal();
    const {setOpenLoanModal} = useUnderwritingLoans();

    return (
        <Stack direction='row' alignItems='center' justifyContent='space-between' spacing={1}>
            <Stack direction='row' alignItems='center' spacing={1}>
                <Typography variant='subtitle1' pr={1}>
                    Loan #{data.gid} {data.context === 'REFINANCE' ? '(Refinance)' : ''}
                </Typography>
                <ActionCell
                    testid='uw-loan-info-details-head-collections-app'
                    Icon={
                        <CreditCard color='primary' fontSize='small' titleAccess='Open in Collections App' />
                    }
                    href={`/secured/collections#${COLLECTIONS_HASH}:${data.gid}`}
                />
                <ActionCell
                    testid='uw-loan-info-details-head-send-communication'
                    Icon={<Send color='primary' fontSize='small' titleAccess='Send Communication' />}
                    onClick={() =>
                        setOpenLoanModal({
                            type: UnderwritingLoanModal.Send,
                            props: {
                                loan: data,
                                onClose: () => setOpenLoanModal(null),
                            },
                        })
                    }
                />
            </Stack>
            {data.open && data.gid && (
                <Stack direction='row' alignItems='center' spacing={1}>
                    <Button
                        data-testid='uw-loan-info-details-head-debit-card-payment'
                        variant='contained'
                        color='success'
                        size='small'
                        onClick={() =>
                            showGlobalModal({type: GlobalModal.Debit_Payment, props: {loanId: data.gid, payoffAmount: Number(data.payoff_amount)}})
                        }>
                        Debit Card Payment
                    </Button>
                    <Button
                        data-testid='uw-loan-info-details-head-verbal-ach'
                        variant='contained'
                        size='small'
                        onClick={() =>
                            showGlobalModal({
                                type: GlobalModal.Verbal_ACH,
                                props: {
                                    loan: data,
                                },
                            })
                        }>
                        Verbal ACH
                    </Button>
                </Stack>
            )}
        </Stack>
    );
};
