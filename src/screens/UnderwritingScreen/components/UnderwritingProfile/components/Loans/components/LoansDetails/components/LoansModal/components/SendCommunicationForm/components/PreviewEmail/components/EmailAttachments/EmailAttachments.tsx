import React, {ChangeEvent, useEffect, useState} from 'react';
import {Checkbox, FormControlLabel, Typography} from '@mui/material';
import { AttachmentDTO } from '@/models/attachmentDTO';

interface AttachmentsProps {
    attachments: AttachmentDTO[];
    onChange: (value: AttachmentDTO[]) => void;
}

export const EmailAttachments = ({attachments,  onChange}: AttachmentsProps) => {
    const [selectedAttachments, setSelectedAttachments] = useState<AttachmentDTO[]>([]);
    const [selectedAttachment, setSelectedAttachment] = useState<AttachmentDTO | null>(null);

    const handleChangeAttachments= (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        const attachment = attachments.find((item) => item.attachment_type === value);

        if (!attachment) return;

        if (selectedAttachments.some((item) => item.attachment_type === value)) {
            setSelectedAttachments([]);
        } else {
            setSelectedAttachments([attachment]);
            setSelectedAttachment(attachment);
        }
    };

    useEffect(() => {
            onChange(selectedAttachments);
    }, [selectedAttachments]);
    

    return (
        <>
            <Typography variant='subtitle1'>Attachments</Typography>
            {attachments.map((item, index) => (
                <div key={index}>
                    <FormControlLabel
                        label={item.attachment_type}
                        control={
                            <Checkbox
                                size='small'
                                value={item.attachment_type}
                                checked={selectedAttachment === item}
                                onChange={handleChangeAttachments}
                                inputProps={{'aria-label': 'controlled'}}
                            />
                        }
                    />
                </div>
            ))}
        </>
    );
};
