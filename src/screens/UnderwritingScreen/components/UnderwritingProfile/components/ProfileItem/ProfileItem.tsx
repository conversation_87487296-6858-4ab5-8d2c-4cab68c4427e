import './styles.scss';

import React, {ReactNode, useEffect, useMemo, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Grid2, Stack} from '@mui/material';
import {Refresh} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import {
    KasDotsMenu,
    KasDotsMenuItemProps,
    KasDragTitle,
    KasErrorBoundary,
    KasExpandIcon,
    KasLoadingStatusIcon,
} from '@/components';
import {useSortableList} from '@/views';
import {UnderwritingProfileItemModel, UnderwritingProfileItemType} from '@/models';
import {useUnderwriting} from '@/screens/UnderwritingScreen/useUnderwriting';
import Box from '@mui/material/Box';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {generateProfileItemId} from '@/utils/UnderwritingUtils';

interface ProfileItemProps {
    item: UnderwritingProfileItemModel;
    loading: boolean;
    loadingError: string;
    loaded: boolean;
    TitleComponent?: ReactNode;
    PreviewComponent?: ReactNode;
    DetailsComponent: ReactNode;
    menuItems?: KasDotsMenuItemProps[];
    onRefresh?: () => void;
}

export const ProfileItem = ({
    item,
    loading,
    loadingError,
    loaded,
    TitleComponent,
    PreviewComponent,
    DetailsComponent,
    menuItems = [],
    onRefresh,
}: ProfileItemProps) => {
    const {profile} = useUnderwritingProfile();
    const {expandedItems, updateExpandedItem, createDeleteMenuItem} = useUnderwriting();
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const {attributes, listeners, setActivatorNodeRef} = useSortableList();
    const profileItemId = generateProfileItemId(item.id, profile.uid);

    const expandedItem = useMemo(() => {
        return expandedItems.includes(item.id);
    }, [expandedItems, item]);

    const handleAccordionChange = () => {
        const value = !isAccordionOpen;

        setIsAccordionOpen(value);
        updateExpandedItem(item.id, value);
    };

    useEffect(() => {
        setIsAccordionOpen(expandedItem);
    }, [expandedItem]);

    return (
        <div key={profileItemId} id={profileItemId} className='kas-underwriting-profile-item'>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    expandIcon={<KasExpandIcon expanded={loaded} onClick={handleAccordionChange} 
                        testid={`uw-profile-item-${UnderwritingProfileItemType[item.type]}`}/>}
                    aria-controls={item.id}
                    id={item.id}
                    onDoubleClick={handleAccordionChange}>
                    <Box sx={{flexGrow: 1}}>
                        <KasErrorBoundary>
                            <Grid2 container alignItems='center'>
                                <Grid2 size={3}>
                                    <KasDragTitle
                                        attributes={attributes}
                                        listeners={listeners}
                                        setActivatorNodeRef={setActivatorNodeRef}>
                                        {TitleComponent ? TitleComponent : item.title}
                                    </KasDragTitle>
                                </Grid2>
                                <Grid2 size={8}>
                                    <div
                                        className={`kas-underwriting-profile-item__preview ${isAccordionOpen ? 'hidden' : ''}`}>
                                        {PreviewComponent}
                                    </div>
                                </Grid2>
                                <Grid2 size={1}>
                                    <Stack direction='row' justifyContent='flex-end'>
                                        <KasLoadingStatusIcon loading={loading} loadingError={loadingError} />
                                        {!loading && onRefresh && (
                                            <IconButton title={'Refresh'} onClick={onRefresh} data-testid={`uw-profile-item-${UnderwritingProfileItemType[item.type]}-refresh`}>
                                                <Refresh />
                                            </IconButton>
                                        )}
                                        <KasDotsMenu
                                            testid={`uw-profile-item-${UnderwritingProfileItemType[item.type]}`}
                                            disabled={!!loadingError || loading}
                                            menuItems={[...menuItems, createDeleteMenuItem(item.type)]}
                                        />
                                    </Stack>
                                </Grid2>
                            </Grid2>
                        </KasErrorBoundary>
                    </Box>
                </AccordionSummary>
                <AccordionDetails>
                    <KasErrorBoundary>{DetailsComponent}</KasErrorBoundary>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
