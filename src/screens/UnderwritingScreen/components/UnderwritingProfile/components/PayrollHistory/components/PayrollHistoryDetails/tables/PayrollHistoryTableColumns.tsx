import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingPayrollsHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {CellContext} from '@tanstack/react-table';

const _defaultInfoColumn = defaultInfoColumn<UnderwritingPayrollsHistoryModel>;
const _defaultAmountColumn = defaultAmountColumn<UnderwritingPayrollsHistoryModel>;

const effectiveDateCell = () => {
    return (cell: CellContext<UnderwritingPayrollsHistoryModel, string>) => {
        const {effective_date, payroll_frequency} = cell.row.original;
        return `${effective_date} (${payroll_frequency})`;
    };
};

export const PayrollHistoryTableColumns = [
    _defaultInfoColumn('effective_date', 'Effective Date', 'Total:', effectiveDateCell()),
    _defaultAmountColumn('gross_pay', 'Gross Pay', true, false),
    _defaultAmountColumn('base_pay', 'Base Pay', true, false),
    _defaultAmountColumn('net_pay', 'Net Pay', true, false),
    _defaultAmountColumn('deduction_kashable', 'Kashable Deduction', true, false),
];
