import './styles.scss';

import React, {useMemo} from 'react';
import {UnderwritingPayrollsHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {Row} from '@tanstack/react-table';
import {
    CartesianGrid,
    Legend,
    Line,
    LineChart,
    ResponsiveContainer,
    XAxis,
    YAxis,
    Tooltip as ChartTooltip,
    Label,
} from 'recharts';
import {useTheme} from '@mui/material';
import {PayrollHistoryChartTooltip} from '../index';

interface ChartDataModel {
    effective_date: string;
    gross_pay: number;
    net_pay: number;
    net_pay_plus_deduction: number;
}

interface PayrollHistoryChartProps {
    rows: Row<UnderwritingPayrollsHistoryModel>[];
}

export const PayrollHistoryChart = ({rows}: PayrollHistoryChartProps) => {
    const theme = useTheme();

    const chartData: ChartDataModel[] = useMemo(() => {
        if (!rows.length) {
            return [];
        }
        return rows.reverse().map((row) => {
            return {
                effective_date: row.original.effective_date || '',
                gross_pay: Number(row.original.gross_pay),
                net_pay: Number(row.original.net_pay),
                net_pay_plus_deduction:
                    Number(row.original.net_pay) + Number(row.original.deduction_kashable),
            };
        });
    }, [rows]);

    return (
        <div className='kas-underwriting-payroll-history-chart'>
            <ResponsiveContainer width='100%' height={300}>
                <LineChart width={500} height={300} data={chartData} margin={{top: 10, right: 10, left: 10}}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='effective_date' style={{fontSize: 'var(--small-text-size)'}} />
                    <YAxis
                        axisLine={false}
                        tickLine={false}
                        tickSize={12}
                        style={{fontSize: 'var(--small-text-size)'}}>
                        <Label
                            value='Pay Amount ($)'
                            offset={0}
                            position='insideLeft'
                            angle={-90}
                            style={{fontSize: 'var(--small-text-size)', textAnchor: 'middle'}}
                        />
                    </YAxis>
                    <ChartTooltip content={<PayrollHistoryChartTooltip />} />
                    <Legend wrapperStyle={{fontSize: 12}} />
                    <Line
                        type='monotone'
                        dataKey='gross_pay'
                        name='Gross Pay'
                        stroke={theme.palette.primary.main}
                    />
                    <Line
                        type='monotone'
                        dataKey='net_pay'
                        name='Net Pay'
                        stroke={theme.palette.text.primary}
                    />
                    <Line
                        type='monotone'
                        dataKey='net_pay_plus_deduction'
                        name='Net Pay*'
                        stroke={theme.palette.success.main}
                    />
                </LineChart>
            </ResponsiveContainer>
        </div>
    );
};
