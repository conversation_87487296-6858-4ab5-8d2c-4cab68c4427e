import './styles.scss';

import React from 'react';
import {TooltipProps} from 'recharts';
import {toCurrency} from '@/utils/FormatUtils';
import {useTheme} from '@mui/material';

export const PayrollHistoryChartTooltip: React.FC<TooltipProps<number, string>> = ({active, payload}) => {
    const theme = useTheme();

    if (active && payload && payload.length) {
        return (
            <div className='kas-underwriting-payroll-history-chart-tooltip'>
                <strong>{payload[0].payload.effective_date}</strong>
                <div className='kas-underwriting-payroll-history-chart-tooltip__item'>
                    <span
                        className='kas-underwriting-payroll-history-chart-tooltip__label'
                        style={{color: theme.palette.primary.main}}>
                        {payload[0].name}
                    </span>
                    : {toCurrency(payload[0].value || '')}
                </div>
                <div className='kas-underwriting-payroll-history-chart-tooltip__item'>
                    <span
                        className='kas-underwriting-payroll-history-chart-tooltip__label'
                        style={{color: theme.palette.text.primary}}>
                        {payload[1].name}
                    </span>
                    : {toCurrency(payload[1].value || '')}
                </div>
                <div className='kas-underwriting-payroll-history-chart-tooltip__item'>
                    <span
                        className='kas-underwriting-payroll-history-chart-tooltip__label'
                        style={{color: theme.palette.success.main}}>
                        {payload[2].name}
                    </span>
                    : {toCurrency(payload[2].value || '')}
                </div>
            </div>
        );
    }

    return null;
};
