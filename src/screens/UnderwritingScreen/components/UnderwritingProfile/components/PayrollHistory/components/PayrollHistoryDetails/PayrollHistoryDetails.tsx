import React, {useState} from 'react';
import {useUnderwritingPayrollHistory} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingPayrollsHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {ColumnDef, Row} from '@tanstack/react-table';
import {PayrollHistoryTableColumns} from './tables/PayrollHistoryTableColumns';
import {PayrollHistoryChart} from './components';
import {TableView} from '@/views';
import Box from '@mui/material/Box';

export const PayrollHistoryDetails = () => {
    const {payrollsHistoryState} = useUnderwritingPayrollHistory();
    const [rows, setRows] = useState<Row<UnderwritingPayrollsHistoryModel>[]>([]);

    return (
        <Box px={5} pb={1.5} data-testid='uw-payroll-history-details'>
            {!!rows.length && (
                <Box mb={2.5}>
                    <PayrollHistoryChart rows={rows} />
                </Box>
            )}
            <TableView<UnderwritingPayrollsHistoryModel>
                loading={payrollsHistoryState.loading}
                error={payrollsHistoryState.error}
                data={payrollsHistoryState.data}
                columns={PayrollHistoryTableColumns as ColumnDef<UnderwritingPayrollsHistoryModel, unknown>[]}
                onTableStateChange={(value) => {
                    setRows(value);
                }}
            />
        </Box>
    );
};
