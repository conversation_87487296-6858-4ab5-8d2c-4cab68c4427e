import React, {useEffect} from 'react';
import {
    ProfileItem,
    useUnderwritingPayrollHistory,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {PayrollHistoryDetails} from './components';
import {UnderwritingProfileItemModel} from '@/models';

interface ApplicationsProps {
    item: UnderwritingProfileItemModel;
}

export const PayrollHistory = ({item}: ApplicationsProps) => {
    const {
        loadPayrollsHistoryData,
        payrollsHistoryState: {loading, error, data},
    } = useUnderwritingPayrollHistory();

    useEffect(() => {
        loadPayrollsHistoryData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadPayrollsHistoryData}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            DetailsComponent={<PayrollHistoryDetails />}
        />
    );
};
