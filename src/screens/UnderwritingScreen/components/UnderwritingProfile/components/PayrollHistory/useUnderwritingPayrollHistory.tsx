import React, {createContext, useContext, useState} from 'react';
import {UnderwritingPayrollsHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface UnderwritingPayrollHistoryContextModel {
    payrollsHistoryState: DataStateInterface<UnderwritingPayrollsHistoryModel[]>;
    loadPayrollsHistoryData: () => Promise<void>;
}

const UnderwritingPayrollHistoryContext = createContext<UnderwritingPayrollHistoryContextModel | undefined>(
    undefined,
);

interface UnderwritingPayrollHistoryProviderProps {
    children: React.ReactNode;
}

export const UnderwritingPayrollHistoryProvider: React.FC<UnderwritingPayrollHistoryProviderProps> = ({
    children,
}) => {
    const {gid} = useUnderwritingProfile();
    const [payrollsHistoryState, setPayrollsHistoryState] =
        useState(getDefaultState<UnderwritingPayrollsHistoryModel[]>());

    const loadPayrollsHistoryData = async () => {
        const url = `/api/secured/underwriting/payroll-history/${gid}`;

        setPayrollsHistoryState(getLoadingState(payrollsHistoryState));

        const response: Completable<UnderwritingPayrollsHistoryModel[]> = await apiRequest(url);

        setPayrollsHistoryState(getLoadedState(response));
    };

    const value: UnderwritingPayrollHistoryContextModel = {
        payrollsHistoryState,
        loadPayrollsHistoryData,
    };

    return (
        <UnderwritingPayrollHistoryContext.Provider value={value}>
            {children}
        </UnderwritingPayrollHistoryContext.Provider>
    );
};

export function useUnderwritingPayrollHistory() {
    const context = useContext(UnderwritingPayrollHistoryContext);
    if (!context) {
        throw new Error(
            'useUnderwritingPayrollHistory must be used within UnderwritingPayrollHistoryProvider',
        );
    }
    return context;
}
