import React from 'react';
import {
    ProfileItem,
    useUnderwritingApplications,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ApplicationsDetails} from './components';
import {Typography} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {KasDotsMenuItemProps} from '@/components';
import {UnderwritingProfileItemModel} from '@/models';

interface ApplicationsProps {
    item: UnderwritingProfileItemModel;
}

export const Applications = ({item}: ApplicationsProps) => {
    const {setOpenActionModal} = useUnderwritingProfile();
    const {
        applicationHistoryState,
        loadApplicationHistoryData,
        documentHistoryState,
        loadDocumentHistoryData,
        verificationHistoryState,
        loadVerificationHistoryData,
        persistentHistoryState,
        loadPersistentHistoryData,
    } = useUnderwritingApplications();

    const menuItems: KasDotsMenuItemProps[] = [
        {
            ContentComponent: <Typography variant='body1'>Push To Queue</Typography>,
            onClick: () => setOpenActionModal({type: UnderwritingProfileAction.PUSH_USER_INTO_QUEUE}),
        },
        {
            ContentComponent: <Typography variant='body1'>Upload Document</Typography>,
            onClick: () => setOpenActionModal({type: UnderwritingProfileAction.UPLOAD_DOCUMENT}),
        },
    ];

    const onRefreshHandler = () => {
        if (applicationHistoryState.data || applicationHistoryState.error) {
            loadApplicationHistoryData().then();
        }
        if (documentHistoryState.data || documentHistoryState.error) {
            loadDocumentHistoryData().then();
        }
        if (verificationHistoryState.data || verificationHistoryState.error) {
            loadVerificationHistoryData().then();
        }
        if (persistentHistoryState.data || persistentHistoryState.error) {
            loadPersistentHistoryData().then();
        }
    };

    return (
        <ProfileItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={false}
            loadingError={''}
            loaded={true}
            menuItems={menuItems}
            DetailsComponent={<ApplicationsDetails />}
        />
    );
};
