import React, {createContext, useContext, useMemo, useState} from 'react';
import {
    UnderwritingApplicationHistoryModel,
    UnderwritingDocumentHistoryModel,
    UnderwritingVerificationHistoryModel,
    UnderwritingVerificationPersistentHistoryModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface UnderwritingApplicationsContextModel {
    applicationHistoryState: DataStateInterface<UnderwritingApplicationHistoryModel[]>;
    loadApplicationHistoryData: () => Promise<void>;
    documentHistoryState: DataStateInterface<UnderwritingDocumentHistoryModel[]>;
    loadDocumentHistoryData: () => Promise<void>;
    verificationHistoryState: DataStateInterface<UnderwritingVerificationHistoryModel[]>;
    loadVerificationHistoryData: () => Promise<void>;
    persistentHistoryState: DataStateInterface<UnderwritingVerificationPersistentHistoryModel[]>;
    loadPersistentHistoryData: () => Promise<void>;
}

const UnderwritingApplicationsContext = createContext<UnderwritingApplicationsContextModel | undefined>(
    undefined,
);

interface UnderwritingApplicationsProviderProps {
    children: React.ReactNode;
}

export const UnderwritingApplicationsProvider: React.FC<UnderwritingApplicationsProviderProps> = ({
    children,
}) => {
    const {gid} = useUnderwritingProfile();
    const baseUrl = useMemo(() => `/api/secured/underwriting/applications/${gid}`, [gid]);
    const [applicationHistoryState, setApplicationHistoryState] =
        useState(getDefaultState<UnderwritingApplicationHistoryModel[]>());
    const [persistentHistoryState, setPersistentHistoryState] =
        useState(getDefaultState<UnderwritingVerificationPersistentHistoryModel[]>());
    const [documentHistoryState, setDocumentHistoryState] =
        useState(getDefaultState<UnderwritingDocumentHistoryModel[]>());
    const [verificationHistoryState, setVerificationHistoryState] =
        useState(getDefaultState<UnderwritingVerificationHistoryModel[]>());

    const loadApplicationHistoryData = async () => {
        const url = `${baseUrl}/application`;

        setApplicationHistoryState(getLoadingState(applicationHistoryState));

        const response: Completable<UnderwritingApplicationHistoryModel[]> = await apiRequest(url);

        setApplicationHistoryState(getLoadedState(response));
    };

    const loadDocumentHistoryData = async () => {
        const url = `${baseUrl}/document`;

        setDocumentHistoryState(getLoadingState(documentHistoryState));

        const response: Completable<UnderwritingDocumentHistoryModel[]> = await apiRequest(url);

        setDocumentHistoryState(getLoadedState(response));
    };

    const loadVerificationHistoryData = async () => {
        const url = `${baseUrl}/verification`;

        setVerificationHistoryState(getLoadingState(verificationHistoryState));

        const response: Completable<UnderwritingVerificationHistoryModel[]> = await apiRequest(url);

        setVerificationHistoryState(getLoadedState(response));
    };

    const loadPersistentHistoryData = async () => {
        const url = `${baseUrl}/persistent`;

        setPersistentHistoryState(getLoadingState(persistentHistoryState));

        const response: Completable<UnderwritingVerificationPersistentHistoryModel[]> = await apiRequest(url);

        setPersistentHistoryState(getLoadedState(response));
    };

    const value: UnderwritingApplicationsContextModel = {
        applicationHistoryState,
        loadApplicationHistoryData,
        documentHistoryState,
        loadDocumentHistoryData,
        verificationHistoryState,
        loadVerificationHistoryData,
        persistentHistoryState,
        loadPersistentHistoryData,
    };

    return (
        <UnderwritingApplicationsContext.Provider value={value}>
            {children}
        </UnderwritingApplicationsContext.Provider>
    );
};

export function useUnderwritingApplications() {
    const context = useContext(UnderwritingApplicationsContext);
    if (!context) {
        throw new Error('useUnderwritingApplications must be used within UnderwritingApplicationsProvider');
    }
    return context;
}
