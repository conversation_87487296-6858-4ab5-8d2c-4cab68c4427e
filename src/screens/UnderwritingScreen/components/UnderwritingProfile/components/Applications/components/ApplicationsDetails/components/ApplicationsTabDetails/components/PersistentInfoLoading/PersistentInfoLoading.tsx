import './styles.scss';

import React from 'react';
import {Paper, Skeleton, Stack, Grid2} from '@mui/material';

export const PersistentInfoLoading = () => {
    return (
        <Grid2 className='kas-underwriting-applications-persistent-info-loading' container spacing={2} mb={2}>
            <Grid2 size={6}>
                <Paper elevation={0}>
                    <Grid2 container spacing={2} p={1.5}>
                        <Grid2 size={12}>
                            <Skeleton variant='rounded' animation='wave' width={200} height={28} />
                        </Grid2>
                        <Grid2 size={12}>
                            <Stack direction='row' spacing={4}>
                                <Skeleton variant='rounded' animation='wave' width={120} height={18} />
                                <Skeleton variant='rounded' animation='wave' width={120} height={18} />
                            </Stack>
                        </Grid2>
                    </Grid2>
                </Paper>
            </Grid2>
            <Grid2 size={6}>
                <Paper elevation={0}>
                    <Grid2 container spacing={2} p={1.5}>
                        <Grid2 size={12}>
                            <Skeleton variant='rounded' animation='wave' width={200} height={28} />
                        </Grid2>
                        <Grid2 size={12}>
                            <Stack direction='row' spacing={4}>
                                <Skeleton variant='rounded' animation='wave' width={120} height={18} />
                                <Skeleton variant='rounded' animation='wave' width={120} height={18} />
                            </Stack>
                        </Grid2>
                    </Grid2>
                </Paper>
            </Grid2>
        </Grid2>
    );
};
