import './styles.scss';

import React, {useState} from 'react';
import {ApplicationsTabDetails} from './components';
import {Button, ButtonGroup} from '@mui/material';
import {ApplicationsTabType} from './interfaces';

export const ApplicationsDetails = () => {
    const [activeTab, setActiveTab] = useState<ApplicationsTabType>(ApplicationsTabType.Application);

    return (
        <div className='kas-underwriting-applications-details'>
            <div className='kas-underwriting-applications-details__tabs'>
                <ButtonGroup>
                    {Object.entries(ApplicationsTabType).map(([key, value]) => (
                        <Button
                            key={key}
                            data-testid={`uw-applications-tab-${key}`}
                            variant={activeTab === value ? 'contained' : 'text'}
                            onClick={() => setActiveTab(value)}>
                            {value}
                        </Button>
                    ))}
                </ButtonGroup>
            </div>
            <ApplicationsTabDetails activeTab={activeTab} />
        </div>
    );
};
