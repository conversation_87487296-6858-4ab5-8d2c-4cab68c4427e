import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingApplicationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {AmountCell} from '@/components/table/cells';
import React from 'react';
import {KasStrike} from '@/components';
import {BrowserCell, DecisionCell} from './cells';

const columnHelper = createColumnHelper<UnderwritingApplicationHistoryModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingApplicationHistoryModel>;

const _strikeContent = (props: CellContext<UnderwritingApplicationHistoryModel, string>) => (
    <KasStrike key={`${props.column.id}-${props.row.original.gid}`} isStrike={!props.row.original.session_id}>
        {props.getValue()}
    </KasStrike>
);

const _strikeAmountContent = (props: CellContext<UnderwritingApplicationHistoryModel, string>) => (
    <KasStrike key={`${props.column.id}-${props.row.original.gid}`} isStrike={!props.row.original.session_id}>
        <AmountCell data={Number(props.getValue() || 0)} />
    </KasStrike>
);

export const ApplicationHistoryTableColumns = [
    columnHelper.accessor('application_time', {
        id: 'application_time',
        header: 'Date [browser]',
        cell: (props: CellContext<UnderwritingApplicationHistoryModel, string>) => (
            <BrowserCell data={props.row.original}/>
        ),
    }),
    columnHelper.group({
        id: 'application',
        header: 'Application',
        columns: [
            _defaultInfoColumn('gid', 'ID', undefined, _strikeContent),
            columnHelper.accessor('context', {
                id: 'context',
                header: 'Type',
                cell: (props) => (
                    <KasStrike isStrike={!props.row.original.session_id}>
                        {props.getValue() || 'DEFAULT'}
                    </KasStrike>
                ),
            }),
            columnHelper.accessor('decision_result', {
                id: 'decision_result',
                header: 'Decision',
                enableSorting: false,
                cell: (props) => <DecisionCell data={props.row.original} />,
            }),
            _defaultInfoColumn('loan_purpose', 'Purpose', undefined, _strikeContent),
            _defaultInfoColumn('amount', 'Amount', undefined, _strikeAmountContent),
        ],
    }),
    columnHelper.group({
        id: 'characteristics',
        header: 'Characteristics',
        columns: [
            _defaultInfoColumn('max_kash', 'Max Kash', undefined, _strikeAmountContent),
            _defaultInfoColumn('calc_net_pay', 'Net Pay', undefined, _strikeAmountContent),
            _defaultInfoColumn('apr', 'APR', undefined, _strikeContent),
            _defaultInfoColumn('interest_rate', 'Rate', undefined, _strikeContent),
            _defaultInfoColumn('origination_fee', 'Fee', undefined, _strikeAmountContent),
            _defaultInfoColumn('address_state', 'State', undefined, _strikeContent),
        ],
    }),
    columnHelper.group({
        id: 'loan',
        header: 'Loan',
        columns: [
            _defaultInfoColumn('loan_id', 'ID', undefined, _strikeContent),
            _defaultInfoColumn('bank_id', 'Bank', undefined, _strikeContent),
        ],
    }),
];
