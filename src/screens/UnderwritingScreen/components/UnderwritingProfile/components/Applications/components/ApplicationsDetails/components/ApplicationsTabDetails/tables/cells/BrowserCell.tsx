import React, {ReactNode} from 'react';
import {UnderwritingApplicationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {Chip, Stack, Tooltip} from '@mui/material';
import {KasStrike} from '@/components';

interface DecisionCellProps {
    data: UnderwritingApplicationHistoryModel;
}

export const BrowserCell = ({data}: DecisionCellProps) => {
    const content: (string | ReactNode)[] = [
        <KasStrike key={`application_time-${data.gid}`} isStrike={!data.session_id}>
            {data.application_time || data.create_time}
        </KasStrike>,
    ];

    if (data.user_agent) {
        const agent = data.user_agent.split(' ');
        let browser = '?';

        const browsers = ['Chrome', 'Android', 'Firefox', 'IE', 'Safari', 'Mozilla'];
        for (let i = 0; i < browsers.length; i++) {
            if (new RegExp(browsers[i], 'i').test(agent[0] + agent[1])) {
                browser = browsers[i];
                break;
            }
        }

        content.push(
            <Tooltip
                key={`browser-${data.gid}`}
                title={data.user_agent}
                data-testid={`uw-application-history-browser`}>
                <Chip label={browser} size='small' variant='outlined' />
            </Tooltip>,
        );

        if (data.device) {
            content.push(
                <Tooltip
                    key={`device-${data.gid}`}
                    title='Device'
                    data-testid={`uw-application-history-device`}>
                    <Chip label={data.device} size='small' color='warning' variant='outlined' />
                </Tooltip>,
            );
        }

        const phones = ['Android', 'iOS', 'iPhone', 'Mobile'];
        for (let i = 0; i < phones.length; i++) {
            if (new RegExp(phones[i], 'i').test(data.user_agent)) {
                content.push(
                    <Tooltip
                        key={`mobile-${data.gid}`}
                        title='Mobile'
                        data-testid={`uw-application-history-mobile`}>
                        <Chip label='M' size='small' color='warning' variant='outlined' />
                    </Tooltip>,
                );
                break;
            }
        }
    }

    return (
        <Stack key={data.gid} direction='row' flexWrap='wrap' useFlexGap alignItems='center' spacing={1}>
            {content}
        </Stack>
    );
};
