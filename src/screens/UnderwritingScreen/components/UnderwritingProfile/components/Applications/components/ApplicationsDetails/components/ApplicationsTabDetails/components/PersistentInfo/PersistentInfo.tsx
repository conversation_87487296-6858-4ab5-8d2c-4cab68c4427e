import './styles.scss';

import React from 'react';
import {Grid2} from '@mui/material';
import {UnderwritingVerificationPersistentHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {PersistentSection} from './components';

interface PersistentInfoProps {
    persistent: UnderwritingVerificationPersistentHistoryModel[];
}

export const PersistentInfo = ({persistent}: PersistentInfoProps) => {
    return (
        <Grid2 className='kas-underwriting-applications-persistent-info' container spacing={2} mb={2}>
            <Grid2 size={6}>
                <PersistentSection
                    title='Persistent Approval'
                    data={persistent.filter(({approval_expiry_time}) => approval_expiry_time)}
                    dateKey='approval_expiry_time'
                />
            </Grid2>
            <Grid2 size={6}>
                <PersistentSection
                    title='Persistent Queue'
                    data={persistent.filter(({persistance_expiry_time}) => persistance_expiry_time)}
                    dateKey='persistance_expiry_time'
                />
            </Grid2>
        </Grid2>
    );
};
