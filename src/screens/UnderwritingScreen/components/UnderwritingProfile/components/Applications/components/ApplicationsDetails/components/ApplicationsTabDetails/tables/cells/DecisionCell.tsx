import React, {useState} from 'react';
import {UnderwritingApplicationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {Delete, Error, VisibilityOutlined} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {ApplicationRetireModal, ApplicationTapeCell} from '@/views/application';
import {Stack} from '@mui/material';
import {KasFlaggedIcon} from '@/components';
import {useUnderwritingApplications} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

export const DecisionCell = ({data}: {data: UnderwritingApplicationHistoryModel}) => {
    const {loadApplicationHistoryData} = useUnderwritingApplications();
    const [openModal, setOpenModal] = useState(false);
    const buttons = [];
    const showVisibility = !!((data.decision_result === null || data.decision_result) && data.queues);
    const showError = data.decision_result === null;
    const showDelete = data.session_id != null && data.loan_id == null;
    if (showVisibility) {
        buttons.push(
            <VisibilityOutlined
                data-testid={`uw-application-history-decision-visibility`}
                key={`visibility-${data.gid}`}
                color='primary'
                fontSize='small'
                titleAccess={data.queues}
            />,
        );
    } else if (showError) {
        buttons.push(
            <Error
                data-testid={`uw-application-history-decision-error`}
                key={`error-${data.gid}`}
                fontSize='small'
                color='error'
                titleAccess={data?.decision_rules ? data.decision_rules.join(', ') : ''}
            />,
        );
    } else {
        if (data.decision_result) {
            buttons.push(
                <KasFlaggedIcon
                    key={`done-${data.gid}`}
                    flagged={true}
                    testid={`uw-application-history-decision-result-true`}
                />,
            );
        } else {
            buttons.push(
                <KasFlaggedIcon
                    testid={`uw-application-history-decision-result-false`}
                    key={`done-${data.gid}`}
                    flagged={false}
                    titleAccess={data.decision_rules?.join(', ')}
                />,
            );
        }
    }

    buttons.push(<ApplicationTapeCell key={`theaters-${data.gid}`} gid={data.gid} />);

    if (showDelete) {
        buttons.push(
            <ActionCell
                testid={`uw-application-history-decision-retire`}
                key={`delete-${data.gid}`}
                Icon={<Delete color='error' titleAccess='Retire Application' />}
                onClick={() => setOpenModal(true)}
            />,
        );
    }

    return (
        <>
            <Stack direction='row' alignItems='center' spacing={1}>
                {buttons}
            </Stack>
            {openModal && (
                <ApplicationRetireModal
                    gid={data.gid}
                    onClose={() => setOpenModal(false)}
                    onSuccess={loadApplicationHistoryData}
                />
            )}
        </>
    );
};
