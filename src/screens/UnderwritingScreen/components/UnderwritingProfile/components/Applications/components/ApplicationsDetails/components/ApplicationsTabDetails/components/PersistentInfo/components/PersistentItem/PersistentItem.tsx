import React from 'react';
import dayjs from 'dayjs';
import {getHumanizedDuration} from '@/utils/DateUtils';

interface PersistentItemProps {
    date: string;
    code?: string;
}

export const PersistentItem = ({date, code}: PersistentItemProps) => {
    if (!date) return null;

    const diffDays = dayjs().diff(dayjs(date), 'days');
    const expirationLabel = diffDays > 0 ? 'Expired' : 'Expires';
    const textDecoration = diffDays > 0 ? 'line-through' : 'none';

    return code ? (
        <p style={{textDecoration}}>{code}</p>
    ) : (
        <div>
            <abbr title={`${expirationLabel} ${date}`}>{getHumanizedDuration(date)}</abbr>
        </div>
    );
};
