import React, {useEffect, useMemo} from 'react';
import {ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {KasLoading<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {
    ApplicationHistoryTableColumns,
    VerificationHistoryTableColumns,
    DocumentHistoryTableColumns,
} from './tables';
import {ApplicationsTabType} from '../../interfaces';
import {PersistentInfo, PersistentInfoLoading} from './components';
import {useUnderwritingApplications} from './../../../../useUnderwritingApplications';
import {
    UnderwritingApplicationHistoryModel,
    UnderwritingDocumentHistoryModel,
    UnderwritingVerificationHistoryModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {TableView} from '@/views';
import {useSecured} from '@/hooks/useSecured';
import {EmailCell} from './tables/cells';

interface ApplicationsTabDetailsProps {
    activeTab: ApplicationsTabType;
}

export const ApplicationsTabDetails = ({activeTab}: ApplicationsTabDetailsProps) => {
    const columnHelper = createColumnHelper<UnderwritingApplicationHistoryModel>();
    const {
        loadApplicationHistoryData,
        applicationHistoryState,
        loadDocumentHistoryData,
        documentHistoryState,
        loadVerificationHistoryData,
        verificationHistoryState,
        loadPersistentHistoryData,
        persistentHistoryState,
    } = useUnderwritingApplications();
    const {hasAnyRole} = useSecured();

    const columns = useMemo(() => {
        switch (activeTab) {
            case ApplicationsTabType.Application: {
                if (hasAnyRole(['KASH_COMPLIANCE', 'KASH_OPERATOR'])) {
                    return [
                        ...ApplicationHistoryTableColumns,
                        columnHelper.accessor('send_email', {
                            id: 'send_email',
                            header: 'Action',
                            enableSorting: false,
                            cell: (props) => <EmailCell data={props.row.original} />,
                        }),
                    ];
                }
                return ApplicationHistoryTableColumns;
            }
            case ApplicationsTabType.Document:
                return DocumentHistoryTableColumns;
            case ApplicationsTabType.Verification:
                return VerificationHistoryTableColumns;
            default:
                return [];
        }
    }, [activeTab]);

    const loadData = () => {
        switch (activeTab) {
            case ApplicationsTabType.Application:
                if (!applicationHistoryState.loading && !applicationHistoryState.data) {
                    loadApplicationHistoryData().then();
                }
                break;
            case ApplicationsTabType.Document:
                if (!documentHistoryState.loading && !documentHistoryState.data) {
                    loadDocumentHistoryData().then();
                }
                break;
            case ApplicationsTabType.Verification:
                if (!verificationHistoryState.loading && !verificationHistoryState.data) {
                    loadVerificationHistoryData().then();
                }
                if (!persistentHistoryState.loading && !persistentHistoryState.data) {
                    loadPersistentHistoryData().then();
                }
                break;
        }
    };

    useEffect(loadData, [activeTab]);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={activeTab === ApplicationsTabType.Application}>
                <TableView<UnderwritingApplicationHistoryModel>
                    loading={applicationHistoryState.loading}
                    error={applicationHistoryState.error}
                    data={applicationHistoryState.data}
                    columns={columns as ColumnDef<UnderwritingApplicationHistoryModel, unknown>[]}
                    onRetry={loadApplicationHistoryData}
                    tableName={activeTab}
                />
            </KasSwitchWhen>
            <KasSwitchWhen condition={activeTab === ApplicationsTabType.Document}>
                <TableView<UnderwritingDocumentHistoryModel>
                    loading={documentHistoryState.loading}
                    error={documentHistoryState.error}
                    data={documentHistoryState.data}
                    withTableActions
                    columns={columns as ColumnDef<UnderwritingDocumentHistoryModel, unknown>[]}
                    onRetry={loadDocumentHistoryData}
                    tableName={activeTab}
                    sortingColumns={[{id: 'gid', desc: true}]}
                />
            </KasSwitchWhen>
            <KasSwitchWhen condition={activeTab === ApplicationsTabType.Verification}>
                <KasSwitch>
                    <KasSwitchWhen condition={persistentHistoryState.loading}>
                        <PersistentInfoLoading />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!persistentHistoryState.error}>
                        <KasLoadingError
                            error={persistentHistoryState.error}
                            onTryAgain={loadPersistentHistoryData}
                        />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!persistentHistoryState.data}>
                        <PersistentInfo persistent={persistentHistoryState.data || []} />
                    </KasSwitchWhen>
                </KasSwitch>
                <TableView<UnderwritingVerificationHistoryModel>
                    loading={verificationHistoryState.loading}
                    error={verificationHistoryState.error}
                    data={verificationHistoryState.data}
                    columns={columns as ColumnDef<UnderwritingVerificationHistoryModel, unknown>[]}
                    onRetry={loadVerificationHistoryData}
                    tableName={activeTab}
                />
            </KasSwitchWhen>
        </KasSwitch>
    );
};
