import React, {useState} from 'react';
import {UnderwritingApplicationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {EmailOutlined} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {KasModal} from '@/components';
import {EmailPreviewForm} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/ProfileActionModal/components/EmailPreviewForm/EmailPreviewForm';
import { useUnderwritingApplications } from '../../../../../../useUnderwritingApplications';
interface EmailCellProps {
    data: UnderwritingApplicationHistoryModel;
}
export const EmailCell = ({data}: EmailCellProps) => {
    const [openModal, setOpenModal] = useState(false);
    const emailPreviewUrl='/api/secured/underwriting/applications/email-preview';
    const emailTemplatesList = `/api/secured/underwriting/applications/${data.gid}/email-preview`;

    const { loadApplicationHistoryData } = useUnderwritingApplications();

    const onClose = () => {
        setOpenModal(false);
    }

    const onSendEmail = () => {
        onClose();
        loadApplicationHistoryData().then();
    }

    return (
        <>
            <ActionCell Icon={<EmailOutlined color={data.pending_comm_count > 0 ? 'warning' : 'inherit'} />} onClick={() => setOpenModal(true)} />
            <KasModal title='Send Email' open={openModal} onClose={onClose}>
                <EmailPreviewForm id={data.gid} onClose={onClose} actionAfterSendEmail={onSendEmail} emailPreviewUrl={emailPreviewUrl} emailTemplatesList={emailTemplatesList}/>
            </KasModal>
        </>
    );
};