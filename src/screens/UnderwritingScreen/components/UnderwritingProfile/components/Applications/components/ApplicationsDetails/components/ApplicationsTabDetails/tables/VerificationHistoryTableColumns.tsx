import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingVerificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {KasFlaggedIcon, KasStrike} from '@/components';
import {VerificationDetailsLink} from '../components';

const columnHelper = createColumnHelper<UnderwritingVerificationHistoryModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingVerificationHistoryModel>;

const _strikeContent = (props: CellContext<UnderwritingVerificationHistoryModel, string>) => (
    <KasStrike isStrike={props.row.original.archived || !!props.row.original.verify_time}>
        {props.getValue()}
    </KasStrike>
);

export const VerificationHistoryTableColumns = [
    _defaultInfoColumn('application_id', 'Application', undefined, _strikeContent),
    columnHelper.accessor('gid', {
        id: 'gid',
        header: 'Queue ID',
        cell: (props) => (
            <KasStrike isStrike={props.row.original.archived || !!props.row.original.verify_time} testid='uw-verification-details-link'>
                <VerificationDetailsLink code={props.row.original.queue_code} id={props.getValue()} />
            </KasStrike>
        ),
    }),
    _defaultInfoColumn('queue_code', 'Type', undefined, _strikeContent),
    _defaultInfoColumn('loan_id', 'Loan', undefined, _strikeContent),
    _defaultInfoColumn('queue_time', 'Queued', undefined, _strikeContent),
    _defaultInfoColumn('verify_time', 'Verified', undefined, _strikeContent),
    _defaultInfoColumn('verify_user', 'Approved', undefined, _strikeContent),
    columnHelper.accessor('uploaded', {
        id: 'uploaded',
        header: 'Upload',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()}/>,
    }),
];
