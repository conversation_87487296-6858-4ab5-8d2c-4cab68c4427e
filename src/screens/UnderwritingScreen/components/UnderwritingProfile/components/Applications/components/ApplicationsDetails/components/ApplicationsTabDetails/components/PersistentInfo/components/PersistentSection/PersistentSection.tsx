import React from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {UnderwritingVerificationPersistentHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasInfo} from '@/components';
import Box from '@mui/material/Box';
import {PersistentItem} from './../index';

interface PersistentSectionProps {
    title: string;
    data: UnderwritingVerificationPersistentHistoryModel[];
    dateKey: keyof UnderwritingVerificationPersistentHistoryModel;
}

export const PersistentSection = ({title, data, dateKey}: PersistentSectionProps) => (
    <Paper elevation={0} style={{minHeight: '100%'}}>
        <Box p={1.5}>
            <Typography variant='subtitle1'>{title}</Typography>
            {!!data.length && (
                <Stack direction='row' spacing={4} pt={2}>
                    <KasInfo label='Type'>
                        {data.map((item, index) => (
                            <PersistentItem key={index} date={item[dateKey]} code={item.code} />
                        ))}
                    </KasInfo>
                    <KasInfo label='Expires'>
                        {data.map((item, index) => (
                            <PersistentItem key={index} date={item[dateKey]} />
                        ))}
                    </KasInfo>
                </Stack>
            )}
        </Box>
    </Paper>
);
