import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingDocumentHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {KasDownloadLink, KasStrike} from '@/components';
import {DocumentMetadataCell} from '../components';

const _columnHelper = createColumnHelper<UnderwritingDocumentHistoryModel>();

const _defaultInfoColumn = defaultInfoColumn<UnderwritingDocumentHistoryModel>;

const _strikeContent = (props: CellContext<UnderwritingDocumentHistoryModel, string>) => (
    <KasStrike isStrike={!props.row.original.active || !!props.row.original.verify_time}>
        {props.getValue()}
    </KasStrike>
);

export const DocumentHistoryTableColumns = [
    _defaultInfoColumn('gid', 'ID', undefined, _strikeContent),
    _defaultInfoColumn('type', 'Type', undefined, _strikeContent),
    _columnHelper.accessor('name', {
        id: 'name',
        header: 'Name',
        cell: (props: CellContext<UnderwritingDocumentHistoryModel, string>) => {
            const {name, gid} = props.row.original;
            const params = {
                path: `/secured/common/download/document/${gid}`,
            };

            return (
                <KasDownloadLink params={JSON.stringify(params)} testid='uw-document-history-link'>
                    {name}
                </KasDownloadLink>
            );
        },
        meta: {
            exportHTML: (cell) => cell.getValue(),
        },
    }),
    _defaultInfoColumn('upload_ts', 'Uploaded', undefined, _strikeContent),
    _defaultInfoColumn('last_update_user', 'Update User', undefined, _strikeContent),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingDocumentHistoryModel, string>) => {
            const {gid, metadata} = props.row.original;

            return <DocumentMetadataCell key={gid} gid={gid} metadata={metadata} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
