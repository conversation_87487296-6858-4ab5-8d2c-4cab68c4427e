import './styles.scss';

import React from 'react';
import {KasSharedLink} from '@/components';
import {VERIFICATION_HASH} from '@/constants';

interface VerificationDetailsLinkProps {
    code: string;
    id: number;
}

export const VerificationDetailsLink = ({code, id}: VerificationDetailsLinkProps) => {
    return (
        <KasSharedLink href={`/secured/verification#${VERIFICATION_HASH}:${code}|${id}`}>{id}</KasSharedLink>
    );
};
