export enum EmployeeProfileTabType {
    Addresses = 'Addresses',
    Phones = 'Phones',
    Emails = 'Emails',
}

interface BaseEmployeeProfileActionProps {
    onClose: () => void;
}

export interface RemoveEmployeeProfileAddressProps extends BaseEmployeeProfileActionProps {
    addressId: number;
}

export interface PrimaryEmployeeProfileAddressProps extends BaseEmployeeProfileActionProps {
    addressId: number;
}

export interface ToggleDeliverableEmployeeProfileAddressProps extends BaseEmployeeProfileActionProps {
    addressId: number;
}

export interface RemoveEmployeeProfilePhoneProps extends BaseEmployeeProfileActionProps {
    phoneId: number;
}

export interface PrimaryEmployeeProfilePhoneProps extends BaseEmployeeProfileActionProps {
    phoneId: number;
}

export interface RemoveEmployeeProfileEmailProps extends BaseEmployeeProfileActionProps {
    emailId: number;
}

export interface DeleteUserHistoryEmployeeProfileProps extends BaseEmployeeProfileActionProps {
    emailId: number;
}

export interface PrimaryEmployeeProfileEmailProps extends BaseEmployeeProfileActionProps {
    emailId: number;
}

export interface RefundEmployeeProfileEmailProps extends BaseEmployeeProfileActionProps {
    bankId: number;
    onSuccess?: () => void;
}

export type UnderwritingEmployeeProfileModalProps =
    | {type: UnderwritingEmployeeProfileModal.Remove_Address; props: RemoveEmployeeProfileAddressProps}
    | {type: UnderwritingEmployeeProfileModal.Primary_Address; props: PrimaryEmployeeProfileAddressProps}
    | {
          type: UnderwritingEmployeeProfileModal.Toggle_Deliverable_Address;
          props: ToggleDeliverableEmployeeProfileAddressProps;
      }
    | {type: UnderwritingEmployeeProfileModal.Remove_Phone; props: RemoveEmployeeProfilePhoneProps}
    | {type: UnderwritingEmployeeProfileModal.Primary_Phone; props: PrimaryEmployeeProfilePhoneProps}
    | {type: UnderwritingEmployeeProfileModal.Remove_Email; props: RemoveEmployeeProfileEmailProps}
    | {
          type: UnderwritingEmployeeProfileModal.Delete_User_History;
          props: DeleteUserHistoryEmployeeProfileProps;
      }
    | {type: UnderwritingEmployeeProfileModal.Primary_Email; props: PrimaryEmployeeProfileEmailProps}
    | {type: UnderwritingEmployeeProfileModal.Refund_Email; props: RefundEmployeeProfileEmailProps};

export enum UnderwritingEmployeeProfileModal {
    Remove_Address = 'Remove_Address',
    Primary_Address = 'Primary_Address',
    Toggle_Deliverable_Address = 'Toggle_Deliverable_Address',
    Remove_Phone = 'Remove_Phone',
    Primary_Phone = 'Primary_Phone',
    Remove_Email = 'Remove_Email',
    Delete_User_History = 'Delete_User_History',
    Primary_Email = 'Primary_Email',
    Refund_Email = 'Refund_Email',
}
