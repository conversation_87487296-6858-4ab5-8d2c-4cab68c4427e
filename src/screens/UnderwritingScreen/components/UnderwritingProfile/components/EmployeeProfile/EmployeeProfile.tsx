import React, {useEffect} from 'react';
import {
    ProfileItem,
    useUnderwritingEmployeeProfile,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {EmployeeProfileDetails, EmployeeProfilePreview, EmployeeProfileTitle} from './components';
import {Typography} from '@mui/material';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {useEventSubscription} from '@/utils/EventUtils';
import {EventName} from '@/lib/slices/eventSlice';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {KasDotsMenuItemProps} from '@/components';
import {UnderwritingProfileItemModel} from '@/models';

interface EmployeeProfileProps {
    item: UnderwritingProfileItemModel;
}

export const EmployeeProfile = ({item}: EmployeeProfileProps) => {
    const {activeProfile} = useAppSelector(selectUnderwritingState);
    const {profile} = useUnderwritingProfile();
    const isProfileActive = activeProfile?.uid === profile.uid;
    const {
        employeeProfileState: {data, loading, error},
        loadEmployeeProfileData,
        setOpenActionModal,
    } = useUnderwritingProfile();
    const {addressesState, loadAddressesData, phonesState, loadPhonesData, emailsState, loadEmailsData} =
        useUnderwritingEmployeeProfile();
    const menuItems: KasDotsMenuItemProps[] = [
        {
            ContentComponent: <Typography variant='body1'>Change Contact Restrictions</Typography>,
            onClick: () => setOpenActionModal({type: UnderwritingProfileAction.RESTRICT_CONTACT}),
        },
        {
            ContentComponent: <Typography variant='body1'>Send CTA Email</Typography>,
            onClick: () => setOpenActionModal({type: UnderwritingProfileAction.SEND_CTA_EMAIL}),
        },
        {
            ContentComponent: <Typography variant='body1'>Edit</Typography>,
            onClick: () =>
                setOpenActionModal({type: UnderwritingProfileAction.PROCEED_EDIT_EMPLOYEE_PROFILE}),
        },
    ];

    const updateEmployeeAddresses = () => {
        if (addressesState.data || addressesState.error) {
            loadAddressesData().then();
        }
    };

    const updateEmployeePhones = () => {
        if (phonesState.data || phonesState.error) {
            loadPhonesData().then();
        }
    };

    const updateEmployeeEmails = () => {
        if (emailsState.data || emailsState.error) {
            loadEmailsData().then();
        }
    };

    const onRefreshHandler = () => {
        loadEmployeeProfileData().then();
        updateEmployeeAddresses();
        updateEmployeePhones();
        updateEmployeeEmails();
    };

    useEffect(() => {
        loadEmployeeProfileData().then();
    }, []);

    useEventSubscription(isProfileActive, EventName.UpdateEmployeeEmails, updateEmployeeEmails, [
        emailsState,
    ]);
    useEventSubscription(isProfileActive, EventName.UpdateEmployeePhones, updateEmployeePhones, [
        phonesState,
    ]);
    useEventSubscription(isProfileActive, EventName.UpdateEmployeeAddresses, updateEmployeeAddresses, [
        addressesState,
    ]);

    return (
        <ProfileItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={loading}
            loadingError={error}
            loaded={!!data}
            menuItems={menuItems}
            TitleComponent={<EmployeeProfileTitle data={data} />}
            DetailsComponent={<EmployeeProfileDetails />}
            PreviewComponent={<EmployeeProfilePreview />}
        />
    );
};
