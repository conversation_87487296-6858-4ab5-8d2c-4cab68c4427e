import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Alert, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {ToggleDeliverableEmployeeProfileAddressProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/EmployeeProfile/interfaces';

export const ToggleDeliverableAddressForm = ({
    addressId,
    onClose,
}: ToggleDeliverableEmployeeProfileAddressProps) => {
    const {onSubmitAddressAction} = useUnderwritingEmployeeProfile();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        setSubmitting(true);
        await onSubmitAddressAction(`addresses?id=${addressId}`, '', 'put');
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>Change the deliverability of this address?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter submitText='OK' loading={submitting} onCancel={onClose} />
                </Grid2>
            </Grid2>
        </form>
    );
};
