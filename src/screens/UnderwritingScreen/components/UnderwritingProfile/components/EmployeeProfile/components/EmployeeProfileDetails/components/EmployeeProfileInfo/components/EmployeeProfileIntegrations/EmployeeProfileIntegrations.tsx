import './styles.scss';

import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Paper, Stack, Typography} from '@mui/material';
import {UnderwritingEmployeeProfileIntegrationModel} from '@/screens/UnderwritingScreen/interfaces';
import {KasExpandIcon, KasInfo, KasLoading, KasSocialImage} from '@/components';
import Box from '@mui/material/Box';
import CachedIcon from '@mui/icons-material/Cached';
import IconButton from '@mui/material/IconButton';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';

interface EmployeeProfileIntegrationsProps {
    employeeId: number;
    integrations: UnderwritingEmployeeProfileIntegrationModel[];
}

export const EmployeeProfileIntegrations = ({employeeId, integrations}: EmployeeProfileIntegrationsProps) => {
    const {showMessage} = useSnackbar();
    const [updating, setUpdating] = useState(false);

    const onUpdateIntegration = async (value: string) => {
        setUpdating(true);
        const url = `/api/secured/underwriting/employee-profile/${employeeId}/integration`;
        const body = JSON.stringify({value});
        const response = await apiRequest(url, {method: 'put', body});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
        }

        setUpdating(false);
    };

    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };
    return (
        <div className='kas-underwriting-employee-profile-integration' data-testid='uw-employee-profile-integrations'>
            <Box mb={1}>
                <Typography variant='subtitle1'>Integrations</Typography>
            </Box>
            <Stack
                flexDirection='row'
                alignItems='center'
                columnGap={2}
                rowGap={1}
                useFlexGap
                flexWrap='wrap'>
                {integrations.map((item) => (
                <Paper key={item.identifier}>
                    <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                        <AccordionSummary
                            component='div'
                            sx={{paddingLeft: '8px'}}
                            expandIcon={item.loan_references ? <KasExpandIcon expanded={true} onClick={handleAccordionChange} /> : null}
                            onDoubleClick={handleAccordionChange}>
                        <Stack flexDirection='row' alignItems='center' py={1} px={2}>
                            <KasSocialImage type={item.type} />
                            <Box pl={1}>
                                <KasInfo label='ID:' isInline>
                                    {item.identifier}
                                </KasInfo>
                                <KasInfo label='Created:' isInline>
                                    <Box display='flex'>
                                        {item.create_time}
                                        <Box ml={0.5}>
                                            <IconButton
                                                data-testid={`uw-employee-profile-integration-${item.type}-update`}
                                                disabled={updating}
                                                size='small'
                                                onClick={() => onUpdateIntegration(item.type)}
                                                title={`Update ${item.type} Integration`}>
                                                {updating ? (
                                                    <KasLoading size={18} />
                                                ) : (
                                                    <CachedIcon fontSize='inherit' />
                                                )}
                                            </IconButton>
                                        </Box>
                                    </Box>
                                </KasInfo>
                            </Box>
                        </Stack>
                    </AccordionSummary>
                    <AccordionDetails>
                        {item.loan_references && (item.loan_references.map((loan_ref) => (
                            <Box pl={1} pb={2} key={loan_ref.loan_reference_id}>
                                <KasInfo label='Loan Reference ID:' isInline>
                                    {loan_ref.loan_reference_id}
                                </KasInfo>
                                <KasInfo label='Received:' isInline>
                                    {loan_ref.received}
                                </KasInfo>
                            </Box>
                        )))}
                    </AccordionDetails>
                </Accordion>
                </Paper>
                ))}
            </Stack>
        </div>
    );
};
