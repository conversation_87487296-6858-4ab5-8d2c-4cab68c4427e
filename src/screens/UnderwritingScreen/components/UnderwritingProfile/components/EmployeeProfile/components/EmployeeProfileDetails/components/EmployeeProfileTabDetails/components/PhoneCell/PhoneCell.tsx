import React from 'react';
import {Chip, Stack} from '@mui/material';
import {KasPhoneTooltip, KasStrike} from '@/components';
import {PhoneDTO} from '@/models';

interface PhoneCellProps {
    data: PhoneDTO;
    restrictedContact: string | null;
}

export const PhoneCell = ({data, restrictedContact}: PhoneCellProps) => {
    const phoneTooltip = (
        <KasPhoneTooltip
            phone={data.phone}
            addedDate={data.last_updated}
            withCopy
            restricted={restrictedContact}
        />
    );

    const content = !data.valid ? <KasStrike isStrike>{phoneTooltip}</KasStrike> : phoneTooltip;

    if (data.reassigned) {
        const tooltipText = data.reassigned_date ? `Reassigned - ${data.reassigned_date}` : 'Reassigned';

        return (
            <Stack flexDirection='row' alignItems='center' columnGap={1}>
                {content}
                <Chip
                    label='R'
                    size='small'
                    title={tooltipText}
                    color='error'
                    sx={{marginY: '-2px', padding: 0}}
                />
            </Stack>
        );
    }

    return content;
};
