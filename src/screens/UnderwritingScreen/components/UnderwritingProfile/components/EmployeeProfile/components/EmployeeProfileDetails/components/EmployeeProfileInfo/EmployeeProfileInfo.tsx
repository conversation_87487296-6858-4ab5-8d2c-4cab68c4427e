import './styles.scss';

import React, {useMemo} from 'react';
import {useTheme, Grid2} from '@mui/material';
import {
    KasCopyText,
    KasFlaggedIcon,
    KasFlaggedValue,
    KasInfo,
    KasLink,
    KasMaskedSSN,
    KasPhoneTooltip,
} from '@/components';
import {UnderwritingEmployeeProfileItemModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingProfileAction} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import EditIcon from '@mui/icons-material/Edit';
import IconButton from '@mui/material/IconButton';
import {toCurrency} from '@/utils/FormatUtils';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingEmployeeProfileModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/EmployeeProfile/interfaces';
import {getHumanizedDuration} from '@/utils/DateUtils';
import {EmployeeProfileIntegrations} from './components';

interface EmployeeProfileInfoProps {
    data: UnderwritingEmployeeProfileItemModel;
}

export const EmployeeProfileInfo = ({data}: EmployeeProfileInfoProps) => {
    const {setOpenActionModal} = useUnderwritingProfile();
    const {setOpenEmployeeProfileModal, loadUnmaskedSSN, unmaskedSSN} = useUnderwritingEmployeeProfile();
    const {palette} = useTheme();

    const onLoadSSN = async () => {
        return await loadUnmaskedSSN(data?.gid);
    };

    const statusColor: string = useMemo(() => {
        if (data.status === 'Active' || data.status === 'LeaveWithPay') {
            return palette.success.main;
        }

        return palette.error.main;
    }, [data.status]);

    const identity = useMemo(() => {
        switch (data.identity_verified) {
            case null:
                return 'Never Verified';
            case true:
                return 'Verified';
            default:
                return 'High Risk';
        }
    }, [data.identity_verified]);

    const identityColor = useMemo(() => {
        switch (data.identity_verified) {
            case null:
                return palette.primary.main;
            case true:
                return palette.success.main;
            default:
                return palette.error.main;
        }
    }, [data.identity_verified]);

    const refinanceEligibleTitle = useMemo(() => {
        if (data.refinance_eligible_date) {
            const duration = getHumanizedDuration(data.refinance_eligible_date);

            return `${data.refinance_eligible_date} (${duration})`;
        }

        return 'No eligible date';
    }, [data.identity_verified]);

    return (
        <div className='kas-underwriting-employee-profile-info' data-testid='uw-employee-profile-info'>
            <Grid2 container mb={2} spacing={2}>
                <Grid2 size={1.5}>
                    <KasInfo label='ID'>
                        <KasCopyText>{data.gid}</KasCopyText>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='First Name'>
                        <KasCopyText>{data.first_name}</KasCopyText>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Last Name'>
                        <KasCopyText>{data.last_name}</KasCopyText>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Date of Birth'>{data.dob}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    {
                        <KasInfo label='SSN'>
                            {unmaskedSSN
                                ? unmaskedSSN
                                : data.ssn && (
                                      <KasMaskedSSN
                                          ssn={data.ssn}
                                          onLoanSSN={onLoadSSN}
                                          testid='uw-employee-profile-details'
                                      />
                                  )}
                        </KasInfo>
                    }
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Home Phone'>
                        {data.phone2 ? <KasPhoneTooltip phone={data.phone2} withCopy /> : null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Identity'>
                        <KasLink
                            testid='uw-employee-profile-identity'
                            style={{color: identityColor}}
                            onClick={() =>
                                setOpenActionModal({type: UnderwritingProfileAction.IDENTITY_VERIFIED})
                            }>
                            {identity}
                        </KasLink>
                    </KasInfo>
                </Grid2>
            </Grid2>

            <Grid2 container mb={2} spacing={2}>
                <Grid2 size={1.5}>
                    <KasInfo label='ID @ Employer'>
                        {data.id_at_employer ? <KasCopyText>{data.id_at_employer}</KasCopyText> : null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Employment Status'>
                        <span style={{color: statusColor}}>{data.status}</span>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Pay Type'>
                        {data.pay_type ? (
                            <>
                                {data.pay_type} ({data.full_part_time})
                            </>
                        ) : null}
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Pay Rate'>{toCurrency(data.pay_rate)}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Position'>{data.position}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Contract'>{data.full_part_time}</KasInfo>
                </Grid2>
            </Grid2>

            <Grid2 container mb={2} spacing={2}>
                <Grid2 size={1.5}>
                    <KasInfo label='Benefits Eligible'>
                        <abbr title={data.benefit_ineligible_rule}>
                            <KasFlaggedIcon
                                flagged={data.benefit_eligible_employee}
                                testid='uw-employee-profile-benefits'
                            />
                        </abbr>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Application Precheck'>
                        <abbr title={data.application_precheck_rule}>
                            <KasFlaggedIcon
                                flagged={data.application_precheck}
                                testid='uw-employee-profile-precheck'
                            />
                        </abbr>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Refinance Eligible'>
                        <abbr title={refinanceEligibleTitle}>
                            <KasFlaggedIcon
                                flagged={data.refinance_eligible}
                                testid='uw-employee-profile-refinance'
                            />
                        </abbr>
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Deductions Authorized'>
                        <KasFlaggedIcon
                            flagged={data.payroll_deduction_status !== 'Revoked'}
                            testid='uw-employee-profile-deductions'
                        />
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Loan Guarantee'>
                        <KasFlaggedIcon flagged={data.guarantee} testid='uw-employee-profile-guarantee' />
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Income Flagged'>
                        <KasFlaggedValue flagged={data.income_flagged} testid='uw-employee-profile-income' />
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='PEP Flagged'>
                        <KasFlaggedValue
                            flagged={!!data.politically_exposed}
                            testid='uw-employee-profile-pep'
                        />
                    </KasInfo>
                </Grid2>
            </Grid2>

            <Grid2 container spacing={2}>
                <Grid2 size={1.5}>
                    <KasInfo label='Hire Date'>{data.last_hire_date}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Term Date'>{data.termination_date}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Account Balance'>{toCurrency(data.account_balance)}</KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Bankruptcy'>
                        {data.open_bankruptcy_petition ? (
                            <>
                                <KasLink
                                    testid='uw-employee-profile-bankruptcy'
                                    onClick={() =>
                                        setOpenActionModal({type: UnderwritingProfileAction.CLEAR_BANKRUPTCY})
                                    }>
                                    {data.bankruptcy_chapter ? 'Chapter ' + data.bankruptcy_chapter : 'Open'}
                                </KasLink>{' '}
                                <IconButton
                                    data-testid='uw-employee-profile-bankruptcy-edit'
                                    onClick={() =>
                                        setOpenActionModal({
                                            type: UnderwritingProfileAction.FLAG_FOR_BANKRUPTCY,
                                        })
                                    }>
                                    <EditIcon sx={{height: 16}} />
                                </IconButton>
                            </>
                        ) : (
                            <KasLink
                                data-testid='uw-employee-profile-bankruptcy-edit'
                                onClick={() =>
                                    setOpenActionModal({type: UnderwritingProfileAction.FLAG_FOR_BANKRUPTCY})
                                }>
                                N/A
                            </KasLink>
                        )}
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Deposit Confirmation Required'>
                        <KasFlaggedIcon
                            flagged={data.deposit_confirmation_required}
                            testid='uw-employee-profile-deposit-confirmation-required'
                        />
                    </KasInfo>
                </Grid2>
                <Grid2 size={1.5}>
                    <KasInfo label='Work Email'>{data.email_at_employer}</KasInfo>
                </Grid2>
                {!!data.refund && (
                    <Grid2 size={1.5}>
                        <KasInfo label='Refund'>
                            {data.primary_bank_id ? (
                                <KasLink
                                    testid='uw-employee-profile-refund'
                                    onClick={() =>
                                        setOpenEmployeeProfileModal({
                                            type: UnderwritingEmployeeProfileModal.Refund_Email,
                                            props: {
                                                bankId: data.primary_bank_id,
                                                onClose: () => setOpenEmployeeProfileModal(null),
                                            },
                                        })
                                    }>
                                    {toCurrency(data.refund)}
                                </KasLink>
                            ) : (
                                toCurrency(data.refund)
                            )}
                        </KasInfo>
                    </Grid2>
                )}
            </Grid2>

            {data.integrations.length > 0 && (
                <EmployeeProfileIntegrations employeeId={data.gid} integrations={data.integrations} />
            )}
        </div>
    );
};
