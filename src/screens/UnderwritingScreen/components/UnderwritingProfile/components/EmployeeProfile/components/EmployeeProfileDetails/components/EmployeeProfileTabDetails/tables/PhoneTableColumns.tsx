import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import React from 'react';
import {PhoneActions, PhoneCell} from './../components';
import {KasContactRestricted, KasFlaggedIcon} from '@/components';
import {ContactExtendModel} from './../EmployeeProfileTabDetails';
import {PhoneDTO} from '@/models';

type PhoneExpandModel = ContactExtendModel<PhoneDTO>;

const columnHelper = createColumnHelper<PhoneExpandModel>();

const _defaultInfoColumn = defaultInfoColumn<PhoneExpandModel>;

const _cellContent = (props: CellContext<PhoneExpandModel, string>) => (
    <KasContactRestricted restricted={props.row.original.restrictedContact}>
        {props.getValue()}
    </KasContactRestricted>
);

export const PhoneTableColumns = [
    columnHelper.accessor('phone', {
        id: 'phone',
        header: 'Phone',
        cell: (props) => (
            <PhoneCell data={props.row.original} restrictedContact={props.row.original.restrictedContact} />
        ),
    }),
    _defaultInfoColumn('source', 'Source', undefined, _cellContent),
    _defaultInfoColumn('last_updated', 'Last Updated', undefined, _cellContent),
    columnHelper.accessor('current', {
        id: 'current',
        header: 'Current',
        cell: (props) =>
            props.getValue() ? (
                <KasFlaggedIcon flagged={true} testid='uw-employee-profile-details-phone-current' />
            ) : null,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<PhoneExpandModel, string>) => <PhoneActions data={props.row.original} />,
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
