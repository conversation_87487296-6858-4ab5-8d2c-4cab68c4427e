import React from 'react';
import {Chip, Tooltip} from '@mui/material';
import {UnderwritingEmployeeProfileItemModel} from '@/screens/UnderwritingScreen/interfaces';

export const EmployeeProfileTitle = ({data}: {data: UnderwritingEmployeeProfileItemModel | null}) => {
    return (
        <>
            Employee Profile{' '}
            {data?.collection_agency && (
                <Tooltip title={data.collection_agency} data-testid='uw-employee-profile-collections'>
                    <Chip label='C' size='small' color='error' sx={{padding: 0}} />
                </Tooltip>
            )}
        </>
    );
};
