import './styles.scss';

import React, {useMemo, useState} from 'react';
import {EmployeeProfileInfo, EmployeeProfileModal, EmployeeProfileTabDetails} from './components';
import {Button, ButtonGroup} from '@mui/material';
import {GlobalModal, KasLoadingBackDrop, useGlobalModal} from '@/components';
import {EmployeeProfileTabType} from './../../interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {NoAccounts} from '@mui/icons-material';

export const EmployeeProfileDetails = () => {
    const {showGlobalModal} = useGlobalModal();
    const {
        gid,
        employeeProfileState: {data, loading},
        checkIsContactRestricted,
    } = useUnderwritingProfile();
    const [activeTab, setActiveTab] = useState<EmployeeProfileTabType>(EmployeeProfileTabType.Addresses);

    const actionButtonText = useMemo(() => {
        switch (activeTab) {
            case EmployeeProfileTabType.Addresses:
                return 'Add Address';
            case EmployeeProfileTabType.Phones:
                return 'Add Phone';
            case EmployeeProfileTabType.Emails:
                return 'Add Email';
            default:
                return 'Unknown Tab';
        }
    }, [activeTab]);

    const isTabRestricted = (item: EmployeeProfileTabType) => {
        switch (item) {
            case EmployeeProfileTabType.Addresses:
                return checkIsContactRestricted('LETTER');
            case EmployeeProfileTabType.Phones:
                return checkIsContactRestricted('PHONE');
            case EmployeeProfileTabType.Emails:
                return checkIsContactRestricted('EMAIL');
            default:
                return null;
        }
    };

    const onActionHandler = () => {
        switch (activeTab) {
            case EmployeeProfileTabType.Addresses:
                showGlobalModal({
                    type: GlobalModal.Employee_Address,
                    props: {employeeId: gid, method: 'post'},
                });
                break;
            case EmployeeProfileTabType.Phones:
                showGlobalModal({
                    type: GlobalModal.Employee_Phone,
                    props: {employeeId: gid, method: 'post'},
                });
                break;
            case EmployeeProfileTabType.Emails:
                showGlobalModal({
                    type: GlobalModal.Employee_Email,
                    props: {employeeId: gid, method: 'post'},
                });
                break;
        }
    };

    if (!data) {
        return null;
    }

    return (
        <div className='kas-underwriting-employee-profile-details' data-testid='uw-employee-profile-details'>
            {loading && <KasLoadingBackDrop />}
            <EmployeeProfileInfo data={data} />
            <div className='kas-underwriting-employee-profile-details__tabs'>
                <ButtonGroup>
                    {Object.entries(EmployeeProfileTabType).map(([key, value]) => {
                        const contactRestricted = isTabRestricted(value);

                        return (
                            <Button
                                data-testid={`uw-employee-profile-tab-${value}`}
                                key={key}
                                variant={activeTab === value ? 'contained' : 'text'}
                                title={
                                    !!contactRestricted
                                        ? `Contact Restricted ${contactRestricted}`
                                        : undefined
                                }
                                onClick={() => setActiveTab(value)}>
                                {!!contactRestricted && (
                                    <NoAccounts fontSize='small' sx={{marginRight: 0.5}} />
                                )}
                                {value}
                            </Button>
                        );
                    })}
                </ButtonGroup>
                <Button data-testid='uw-employee-profile-details-tab-action' onClick={onActionHandler}>
                    {actionButtonText}
                </Button>
            </div>
            <EmployeeProfileTabDetails activeTab={activeTab} restrictedContact={isTabRestricted(activeTab)} />
            <EmployeeProfileModal />
        </div>
    );
};
