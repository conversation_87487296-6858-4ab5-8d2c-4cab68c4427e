import React from 'react';
import {Stack} from '@mui/material';
import {ActionCell} from '@/components/table/cells';
import {AssuredWorkload, Delete, DeleteForever} from '@mui/icons-material';
import {UnderwritingEmployeeProfileEmailModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingEmployeeProfile} from './../../../../../../useUnderwritingEmployeeProfile';
import {UnderwritingEmployeeProfileModal} from './../../../../../../interfaces';
import EditIcon from '@mui/icons-material/Edit';
import {GlobalModal, useGlobalModal} from '@/components';

interface EmailActionsProps {
    data: UnderwritingEmployeeProfileEmailModel;
}

export const EmailActions = ({data}: EmailActionsProps) => {
    const {showGlobalModal} = useGlobalModal();
    const {setOpenEmployeeProfileModal} = useUnderwritingEmployeeProfile();

    const onClose = () => setOpenEmployeeProfileModal(null);
    return (
        <Stack direction='row' spacing={1}>
            {!data.history ? (
                <>
                    <ActionCell
                        testid='uw-employee-profile-details-email-edit'
                        Icon={<EditIcon fontSize='small' titleAccess='Edit Email' />}
                        onClick={() => {
                            showGlobalModal({
                                type: GlobalModal.Employee_Email,
                                props: {
                                    gid: data.gid,
                                    method: 'put',
                                    employeeId: data.employee_id,
                                    email: data.email_name,
                                    source: data.source,
                                },
                            });
                        }}
                    />
                    {!data.current && (
                        <>
                            <ActionCell
                                testid='uw-employee-profile-details-email-remove'
                                Icon={<Delete color='error' titleAccess='Remove Email' />}
                                onClick={() => {
                                    setOpenEmployeeProfileModal({
                                        type: UnderwritingEmployeeProfileModal.Remove_Email,
                                        props: {emailId: data.gid, onClose},
                                    });
                                }}
                            />
                            <ActionCell
                                testid='uw-employee-profile-details-email-primary'
                                Icon={<AssuredWorkload color='primary' titleAccess='Set Primary' />}
                                onClick={() => {
                                    setOpenEmployeeProfileModal({
                                        type: UnderwritingEmployeeProfileModal.Primary_Email,
                                        props: {emailId: data.gid, onClose},
                                    });
                                }}
                            />
                        </>
                    )}
                </>
            ) : (
                <ActionCell
                    testid='uw-employee-profile-details-email-delete-history'
                    Icon={<DeleteForever color='error' titleAccess='Delete Users History' />}
                    onClick={() => {
                        setOpenEmployeeProfileModal({
                            type: UnderwritingEmployeeProfileModal.Delete_User_History,
                            props: {emailId: data.gid, onClose},
                        });
                    }}
                />
            )}
        </Stack>
    );
};
