import './styles.scss';

import React from 'react';
import {Stack} from '@mui/material';
import {KasCopyText, KasInfo, KasInfoPreviewLoading, KasMaskedSSN} from '@/components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';

export const EmployeeProfilePreview = () => {
    const {
        employeeProfileState: {data, error},
    } = useUnderwritingProfile();
    const {loadUnmaskedSSN, unmaskedSSN} = useUnderwritingEmployeeProfile();

    const onLoadSSN = async () => {
        return await loadUnmaskedSSN(data?.gid);
    };

    if (error) {
        return null;
    }

    return (
        <div className='kas-underwriting-employee-profile-preview' data-testid='uw-employee-profile-preview'>
            {data ? (
                <Stack direction='row' spacing={4}>
                    <KasInfo label='ID @ Employer'>
                        {data.id_at_employer ? <KasCopyText>{data.id_at_employer}</KasCopyText> : null}
                    </KasInfo>
                    <KasInfo label='SSN'>
                        {unmaskedSSN
                            ? unmaskedSSN
                            : data.ssn && (
                                  <KasMaskedSSN
                                      ssn={data.ssn}
                                      onLoanSSN={onLoadSSN}
                                      testid='uw-employee-profile-preview'
                                  />
                              )}
                    </KasInfo>
                    <KasInfo label='Date of Birth'>{data.dob}</KasInfo>
                </Stack>
            ) : (
                <Stack direction='row' spacing={4}>
                    <KasInfoPreviewLoading />
                    <KasInfoPreviewLoading />
                    <KasInfoPreviewLoading />
                </Stack>
            )}
        </div>
    );
};
