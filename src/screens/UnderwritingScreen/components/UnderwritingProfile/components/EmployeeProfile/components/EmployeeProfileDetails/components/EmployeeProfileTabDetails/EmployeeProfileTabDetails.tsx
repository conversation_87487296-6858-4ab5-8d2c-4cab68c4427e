import './styles.scss';

import React, {useEffect, useMemo} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {EmployeeProfileTabType} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/EmployeeProfile/interfaces';
import {AddressTableColumns, EmailTableColumns, PhoneTableColumns} from './tables';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {TableView} from '@/views';
import {
    UnderwritingEmployeeProfileAddressModel,
    UnderwritingEmployeeProfileEmailModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {DataStateInterface} from '@/interfaces';
import {PhoneDTO} from '@/models';

export type ContactExtendModel<T extends object> = T & {restrictedContact: string | null};

interface EmployeeProfileTabDetailsProps {
    activeTab: EmployeeProfileTabType;
    restrictedContact: string | null;
}

export const EmployeeProfileTabDetails = <T,>({
    activeTab,
    restrictedContact,
}: EmployeeProfileTabDetailsProps) => {
    const {addressesState, loadAddressesData, phonesState, loadPhonesData, emailsState, loadEmailsData} =
        useUnderwritingEmployeeProfile();

    const columns = useMemo(() => {
        switch (activeTab) {
            case EmployeeProfileTabType.Addresses:
                return AddressTableColumns;
            case EmployeeProfileTabType.Phones:
                return PhoneTableColumns;
            case EmployeeProfileTabType.Emails:
                return EmailTableColumns;
            default:
                return [];
        }
    }, [activeTab, restrictedContact]);

    const loadData = () => {
        switch (activeTab) {
            case EmployeeProfileTabType.Addresses:
                if (!addressesState.loading && !addressesState.data) {
                    loadAddressesData().then();
                }
                break;
            case EmployeeProfileTabType.Phones:
                if (!phonesState.loading && !phonesState.data) {
                    loadPhonesData().then();
                }
                break;
            case EmployeeProfileTabType.Emails:
                if (!emailsState.loading && !emailsState.data) {
                    loadEmailsData().then();
                }
                break;
        }
    };

    const useStateData = <T,>(state: DataStateInterface<T[]>, restrictedContact: string | null) => {
        return useMemo(() => {
            if (state.data) {
                return state.data.map((item) => ({...item, restrictedContact}));
            }
            return null;
        }, [state.data, restrictedContact]);
    };

    const addressesStateData = useStateData(addressesState, restrictedContact);
    const phonesStateData = useStateData(phonesState, restrictedContact);
    const emailsStateData = useStateData(emailsState, restrictedContact);

    useEffect(loadData, [activeTab]);

    return (
        <div
            className='kas-underwriting-employee-profile-tab-details'
            data-testid='uw-employee-profile-details-tab-details'>
            {activeTab === EmployeeProfileTabType.Addresses && (
                <TableView<ContactExtendModel<UnderwritingEmployeeProfileAddressModel>>
                    loading={addressesState.loading}
                    error={addressesState.error}
                    data={addressesStateData}
                    hideBackDropLoading={true}
                    columns={
                        columns as ColumnDef<
                            ContactExtendModel<UnderwritingEmployeeProfileAddressModel>,
                            unknown
                        >[]
                    }
                    onRetry={loadAddressesData}
                />
            )}
            {activeTab === EmployeeProfileTabType.Phones && (
                <TableView<ContactExtendModel<PhoneDTO>>
                    loading={phonesState.loading}
                    error={phonesState.error}
                    data={phonesStateData}
                    hideBackDropLoading={true}
                    columns={columns as ColumnDef<ContactExtendModel<PhoneDTO>, unknown>[]}
                    onRetry={loadPhonesData}
                />
            )}
            {activeTab === EmployeeProfileTabType.Emails && (
                <TableView<ContactExtendModel<UnderwritingEmployeeProfileEmailModel>>
                    loading={emailsState.loading}
                    error={emailsState.error}
                    data={emailsStateData}
                    hideBackDropLoading={true}
                    columns={
                        columns as ColumnDef<
                            ContactExtendModel<UnderwritingEmployeeProfileEmailModel>,
                            unknown
                        >[]
                    }
                    onRetry={loadEmailsData}
                />
            )}
        </div>
    );
};
