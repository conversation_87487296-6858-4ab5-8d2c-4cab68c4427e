import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Alert, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {RemoveEmployeeProfileEmailProps} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/EmployeeProfile/interfaces';

export const RemoveEmailForm = ({emailId, onClose}: RemoveEmployeeProfileEmailProps) => {
    const {onSubmitEmailAction} = useUnderwritingEmployeeProfile();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        setSubmitting(true);
        await onSubmitEmailAction(`emails/${emailId}`, '', 'delete');
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>Are you sure?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter submitText='OK' loading={submitting} onCancel={onClose} />
                </Grid2>
            </Grid2>
        </form>
    );
};
