import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingEmployeeProfileAddressModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {AddressActions} from './../components';
import {KasContactRestricted, KasFlaggedIcon} from '@/components';
import {ContactExtendModel} from './../EmployeeProfileTabDetails';

type AddressExpandModel = ContactExtendModel<UnderwritingEmployeeProfileAddressModel>;

const columnHelper = createColumnHelper<AddressExpandModel>();

const _defaultInfoColumn = defaultInfoColumn<AddressExpandModel>;

const _cellContent = (props: CellContext<AddressExpandModel, string>) => (
    <KasContactRestricted restricted={props.row.original.restrictedContact}>
        {props.getValue()}
    </KasContactRestricted>
);

export const AddressTableColumns = [
    columnHelper.accessor('street1', {
        id: 'street1',
        header: 'Street Address',
        cell: (props) => {
            const {street1, street2, street3} = props.row.original;

            return (
                <KasContactRestricted restricted={props.row.original.restrictedContact}>
                    {[street1, street2, street3].filter(Boolean).join(', ')}
                </KasContactRestricted>
            );
        },
    }),
    _defaultInfoColumn('city', 'City', undefined, _cellContent),
    _defaultInfoColumn('state_cd', 'State', undefined, _cellContent),
    _defaultInfoColumn('zip', 'ZIP', undefined, _cellContent),
    _defaultInfoColumn('source', 'Source', undefined, _cellContent),
    _defaultInfoColumn('type', 'Type', undefined, _cellContent),
    _defaultInfoColumn('last_updated', 'Last Updated', undefined, _cellContent),
    columnHelper.accessor('current', {
        id: 'current',
        header: 'Current',
        cell: (props) =>
            props.getValue() ? (
                <KasFlaggedIcon flagged={true} testid='uw-employee-profile-details-address-current' />
            ) : null,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingEmployeeProfileAddressModel, string>) => (
            <AddressActions data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
