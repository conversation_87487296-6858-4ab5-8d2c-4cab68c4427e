import './styles.scss';

import React, {ChangeEvent, useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {Button, Checkbox, FormControlLabel, Stack, Typography, Grid2} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    KasLoading,
    KasLoadingBackDrop,
    KasNoResults,
    KasSearchAutocompleteSelect,
    KasSwitch,
    KasSwitchWhen,
} from '@/components';
import {SelectModel} from '@/interfaces';
import {EmailPreview, ErrorView} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {RefundEmployeeProfileEmailProps} from './../../../../../../interfaces';
import {UnderwritingEmployeeProfileEmailPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {Completable} from '@/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {usePrint} from '@/hooks/usePrint';

export const RefundEmailForm = ({bankId, onClose, onSuccess}: RefundEmployeeProfileEmailProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
    const [emailTypeState, setEmailTypeState] =
        useState(getDefaultState<UnderwritingEmployeeProfileEmailPreviewModel[]>());
    const [previewOption, setPreviewOption] =
        useState<SelectModel<UnderwritingEmployeeProfileEmailPreviewModel> | null>(null);
    const {handlePrint, handleEmailPrintContent} = usePrint();

    const options = useMemo(() => {
        if (emailTypeState.data) {
            return emailTypeState.data.map((item) => ({
                id: item.email_type,
                value: item,
                label: `${item.email_type}${item.sent_date ? ` (Sent: ${item.sent_date})` : ''}`,
            }));
        }
        return [];
    }, [emailTypeState.data]);

    const onSubmit = async () => {
        setSubmitting(true);
        const url = `/api/secured/underwriting/employee-profile/refunds/${bankId}?preview=false`;
        const body = JSON.stringify({
            ...previewOption?.value,
            recipients: previewOption?.value.recipients.filter(
                ({email}) => email && selectedEmails.includes(email),
            ),
        });
        const response = await apiRequest(url, {method: 'post', body});

        if (response.code === 200) {
            showMessage('Sent successfully', 'success');
            onSuccess?.();
            onClose();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {},
        onSubmit,
    });

    const changeOriginalPreviewOption = (
        value: SelectModel<UnderwritingEmployeeProfileEmailPreviewModel> | null,
    ) => {
        setPreviewOption(value);
        setSelectedEmails([]);
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedEmails.includes(value)) {
            setSelectedEmails(selectedEmails.filter((email) => email !== value));
        } else {
            setSelectedEmails([...selectedEmails, value]);
        }
    };

    const handlePrintContent = (value: string) => {
        handleEmailPrintContent(value, previewOption?.value.recipients, previewOption?.value.email_type);
    };

    const loadData = async () => {
        const url = `/api/secured/underwriting/employee-profile/refunds/${bankId}`;

        setEmailTypeState(getLoadingState(emailTypeState));

        const response: Completable<UnderwritingEmployeeProfileEmailPreviewModel[]> = await apiRequest(url);

        setEmailTypeState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={emailTypeState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!emailTypeState.error}>
                <ErrorView error={emailTypeState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!emailTypeState.data}>
                {!emailTypeState.data || emailTypeState.data?.length < 1 ? (
                    <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
                ) : (
                    <form className='kas-underwriting-refund-email-form' onSubmit={formik.handleSubmit}>
                        {emailTypeState.loading && <KasLoadingBackDrop />}
                        <Grid2 container spacing={2} rowSpacing={2}>
                            <Grid2 size={12}>
                                <KasSearchAutocompleteSelect<UnderwritingEmployeeProfileEmailPreviewModel>
                                    value={previewOption}
                                    label='Select Preview Type'
                                    disabled={emailTypeState.loading}
                                    autoFocus={true}
                                    placeholder='Select Preview Type'
                                    options={options}
                                    onSelect={(value) => changeOriginalPreviewOption(value)}
                                />
                            </Grid2>
                            {previewOption?.value && (
                                <>
                                    <Grid2 size={12}>
                                        <Typography variant='subtitle1'>Recipients</Typography>
                                        {previewOption.value.recipients.map((item, index) => (
                                            <div key={index}>
                                                <FormControlLabel
                                                    label={item.email}
                                                    control={
                                                        <Checkbox
                                                            size='small'
                                                            value={item.email}
                                                            checked={
                                                                !!item.email &&
                                                                selectedEmails.includes(item.email)
                                                            }
                                                            onChange={handleChangeRecipients}
                                                        />
                                                    }
                                                />
                                            </div>
                                        ))}
                                    </Grid2>
                                    <Grid2 size={12}>
                                        <Typography variant='subtitle1'>Preview</Typography>
                                        <EmailPreview
                                            url={`/api/secured/underwriting/employee-profile/refunds/${bankId}?preview=true`}
                                            payload={JSON.stringify(previewOption.value)}
                                            onContent={handlePrintContent}
                                        />
                                    </Grid2>
                                </>
                            )}
                            <Grid2 size={12}>
                                <Stack direction='row' justifyContent='flex-end' spacing={2}>
                                    <Button onClick={onClose}>CLOSE</Button>
                                    <Button
                                        variant='outlined'
                                        disabled={!selectedEmails.length}
                                        onClick={handlePrint}>
                                        PRINT
                                    </Button>
                                    <Button
                                        variant='contained'
                                        type='submit'
                                        loading={submitting}
                                        disabled={!formik.isValid || !selectedEmails.length || submitting}>
                                        SEND EMAIL
                                    </Button>
                                </Stack>
                            </Grid2>
                        </Grid2>
                    </form>
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
