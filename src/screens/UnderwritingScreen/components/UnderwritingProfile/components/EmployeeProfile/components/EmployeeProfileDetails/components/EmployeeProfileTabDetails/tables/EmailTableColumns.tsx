import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {UnderwritingEmployeeProfileEmailModel} from '@/screens/UnderwritingScreen/interfaces';
import React from 'react';
import {EmailActions} from './../components';
import {KasContactRestricted, KasFlaggedIcon} from '@/components';
import {ContactExtendModel} from './../EmployeeProfileTabDetails';

type EmailExpandModel = ContactExtendModel<UnderwritingEmployeeProfileEmailModel>;

const columnHelper = createColumnHelper<EmailExpandModel>();

const _defaultInfoColumn = defaultInfoColumn<EmailExpandModel>;

const _cellContent = (props: CellContext<EmailExpandModel, string>) => (
    <KasContactRestricted restricted={props.row.original.restrictedContact}>
        {props.getValue()}
    </KasContactRestricted>
);

export const EmailTableColumns = [
    _defaultInfoColumn('email_name', 'Email', undefined, _cellContent),
    _defaultInfoColumn('source', 'Source', undefined, _cellContent),
    _defaultInfoColumn('last_updated', 'Last Updated', undefined, _cellContent),
    columnHelper.accessor('current', {
        id: 'current',
        header: 'Current',
        cell: (props) =>
            props.getValue() ? (
                <KasFlaggedIcon flagged={true} testid='uw-employee-profile-details-email-current' />
            ) : null,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingEmployeeProfileEmailModel, string>) => (
            <EmailActions data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
