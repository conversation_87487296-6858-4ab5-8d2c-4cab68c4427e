import React from 'react';
import {Stack} from '@mui/material';
import {ActionCell} from '@/components/table/cells';
import {AssuredWorkload, Delete} from '@mui/icons-material';
import {useUnderwritingEmployeeProfile} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {UnderwritingEmployeeProfileModal} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components/EmployeeProfile/interfaces';
import EditIcon from '@mui/icons-material/Edit';
import {PhoneDTO} from '@/models';
import {GlobalModal, useGlobalModal} from '@/components';

export const PhoneActions = ({data}: {data: PhoneDTO}) => {
    const {showGlobalModal} = useGlobalModal();
    const {setOpenEmployeeProfileModal} = useUnderwritingEmployeeProfile();

    const onClose = () => setOpenEmployeeProfileModal(null);
    return (
        <Stack direction='row' spacing={1}>
            <ActionCell
                testid='uw-employee-profile-details-phone-edit'
                Icon={<EditIcon fontSize='small' titleAccess='Edit Phone' />}
                onClick={() => {
                    showGlobalModal({
                        type: GlobalModal.Employee_Phone,
                        props: {
                            gid: data.gid,
                            method: 'put',
                            employeeId: data.employee_id,
                            phone: data.phone,
                            source: data.source,
                        },
                    });
                }}
            />
            {!data.current && !data.reassigned && data.valid && (
                <>
                    <ActionCell
                        testid='uw-employee-profile-details-phone-remove'
                        Icon={<Delete color='error' titleAccess='Remove Phone' />}
                        onClick={() => {
                            setOpenEmployeeProfileModal({
                                type: UnderwritingEmployeeProfileModal.Remove_Phone,
                                props: {phoneId: data.gid, onClose},
                            });
                        }}
                    />
                    <ActionCell
                        testid='uw-employee-profile-details-phone-primary'
                        Icon={<AssuredWorkload color='primary' titleAccess='Set Primary' />}
                        onClick={() => {
                            setOpenEmployeeProfileModal({
                                type: UnderwritingEmployeeProfileModal.Primary_Phone,
                                props: {phoneId: data.gid, onClose},
                            });
                        }}
                    />
                </>
            )}
        </Stack>
    );
};
