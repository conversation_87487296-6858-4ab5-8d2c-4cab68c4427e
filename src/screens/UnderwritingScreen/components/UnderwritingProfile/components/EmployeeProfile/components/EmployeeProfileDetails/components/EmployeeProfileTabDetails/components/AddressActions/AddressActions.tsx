import React from 'react';
import {Stack} from '@mui/material';
import {ActionCell} from '@/components/table/cells';
import {AssuredWorkload, Delete} from '@mui/icons-material';
import {UnderwritingEmployeeProfileAddressModel} from '@/screens/UnderwritingScreen/interfaces';
import {useUnderwritingEmployeeProfile} from './../../../../../../useUnderwritingEmployeeProfile';
import {UnderwritingEmployeeProfileModal} from './../../../../../../interfaces';
import EditIcon from '@mui/icons-material/Edit';
import HomeIcon from '@mui/icons-material/Home';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {GlobalModal, useGlobalModal} from '@/components';

interface AddressActionsProps {
    data: UnderwritingEmployeeProfileAddressModel;
}

export const AddressActions = ({data}: AddressActionsProps) => {
    const {showGlobalModal} = useGlobalModal();
    const {gid} = useUnderwritingProfile();
    const {setOpenEmployeeProfileModal} = useUnderwritingEmployeeProfile();

    const onClose = () => setOpenEmployeeProfileModal(null);
    return (
        <Stack direction='row' spacing={1}>
            <ActionCell
                testid='uw-employee-profile-details-address-edit'
                Icon={<EditIcon fontSize='small' titleAccess='Edit Address' />}
                onClick={() => {
                    showGlobalModal({
                        type: GlobalModal.Employee_Address,
                        props: {
                            gid: data.gid,
                            method: 'put',
                            employeeId: gid,
                            address: data,
                            source: data.source,
                        },
                    });
                }}
            />
            {!data.current && (
                <>
                    <ActionCell
                        testid='uw-employee-profile-details-address-remove'
                        Icon={<Delete color='error' titleAccess='Remove Address' />}
                        onClick={() => {
                            setOpenEmployeeProfileModal({
                                type: UnderwritingEmployeeProfileModal.Remove_Address,
                                props: {addressId: data.gid, onClose},
                            });
                        }}
                    />
                    <ActionCell
                        testid='uw-employee-profile-details-address-primary'
                        Icon={<AssuredWorkload color='primary' titleAccess='Set Primary' />}
                        onClick={() => {
                            setOpenEmployeeProfileModal({
                                type: UnderwritingEmployeeProfileModal.Primary_Address,
                                props: {addressId: data.gid, onClose},
                            });
                        }}
                    />
                </>
            )}
            <ActionCell
                testid='uw-employee-profile-details-address-deliverable'
                Icon={
                    <HomeIcon
                        color={data.deliverable ? 'success' : 'error'}
                        fontSize='small'
                        titleAccess='Toggle Deliverable'
                    />
                }
                onClick={() => {
                    setOpenEmployeeProfileModal({
                        type: UnderwritingEmployeeProfileModal.Toggle_Deliverable_Address,
                        props: {addressId: data.gid, onClose},
                    });
                }}
            />
        </Stack>
    );
};
