import './styles.scss';

import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {useUnderwritingEmployeeProfile} from './../../../../useUnderwritingEmployeeProfile';
import {UnderwritingEmployeeProfileModal} from './../../../../interfaces';
import {
    DeleteUserHistoryForm,
    PrimaryAddressForm,
    PrimaryEmailForm,
    PrimaryPhoneForm,
    RefundEmailForm,
    RemoveAddressForm,
    RemoveEmailForm,
    RemovePhoneForm,
    ToggleDeliverableAddressForm,
} from './components';

export const EmployeeProfileModal = () => {
    const {openEmployeeProfileModal, setOpenEmployeeProfileModal} = useUnderwritingEmployeeProfile();

    const title = useMemo(() => {
        switch (openEmployeeProfileModal?.type) {
            case UnderwritingEmployeeProfileModal.Remove_Address:
                return 'Remove Address';
            case UnderwritingEmployeeProfileModal.Primary_Address:
                return 'Set Primary Address';
            case UnderwritingEmployeeProfileModal.Toggle_Deliverable_Address:
                return 'Toggle Deliverable Address';
            case UnderwritingEmployeeProfileModal.Remove_Phone:
                return 'Remove Phone';
            case UnderwritingEmployeeProfileModal.Primary_Phone:
                return 'Set Primary Phone';
            case UnderwritingEmployeeProfileModal.Remove_Email:
                return 'Remove Email';
            case UnderwritingEmployeeProfileModal.Delete_User_History:
                return 'Delete User History';
            case UnderwritingEmployeeProfileModal.Primary_Email:
                return 'Set Primary Email';
            case UnderwritingEmployeeProfileModal.Refund_Email:
                return 'Email Preview';
            default:
                return 'Unknown Type';
        }
    }, [openEmployeeProfileModal]);

    const renderModalContent = useMemo(() => {
        switch (openEmployeeProfileModal?.type) {
            case UnderwritingEmployeeProfileModal.Remove_Address:
                return <RemoveAddressForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Primary_Address:
                return <PrimaryAddressForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Toggle_Deliverable_Address:
                return <ToggleDeliverableAddressForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Remove_Phone:
                return <RemovePhoneForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Primary_Phone:
                return <PrimaryPhoneForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Remove_Email:
                return <RemoveEmailForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Delete_User_History:
                return <DeleteUserHistoryForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Primary_Email:
                return <PrimaryEmailForm {...openEmployeeProfileModal.props} />;
            case UnderwritingEmployeeProfileModal.Refund_Email:
                return <RefundEmailForm {...openEmployeeProfileModal.props} />;
            default:
                return null;
        }
    }, [openEmployeeProfileModal?.type]);

    return (
        <KasModal
            testid='uw-employee-profile-modal'
            title={title}
            open={!!openEmployeeProfileModal}
            onClose={() => setOpenEmployeeProfileModal(null)}>
            <div className='kas-underwriting-employee-profile-modal'>{renderModalContent}</div>
        </KasModal>
    );
};
