import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {
    UnderwritingEmployeeProfileAddressModel,
    UnderwritingEmployeeProfileEmailModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {UnderwritingEmployeeProfileModalProps} from './interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useDispatch} from 'react-redux';
import {eventUpdateUserProfile} from '@/lib/slices/eventSlice';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {PhoneDTO} from '@/models';

interface UnderwritingEmployeeProfileContextModel {
    unmaskedSSN: string | null;
    loadUnmaskedSSN: (gid?: number) => Promise<Completable<string>>;
    addressesState: DataStateInterface<UnderwritingEmployeeProfileAddressModel[]>;
    loadAddressesData: () => Promise<void>;
    phonesState: DataStateInterface<PhoneDTO[]>;
    loadPhonesData: () => Promise<void>;
    emailsState: DataStateInterface<UnderwritingEmployeeProfileEmailModel[]>;
    loadEmailsData: () => Promise<void>;
    openEmployeeProfileModal: UnderwritingEmployeeProfileModalProps | null;
    setOpenEmployeeProfileModal: (value: UnderwritingEmployeeProfileModalProps | null) => void;
    onSubmitAddressAction: (
        path: string,
        body: string,
        method: 'post' | 'put' | 'delete',
    ) => Promise<Completable<any>>;
    onSubmitPhoneAction: (
        path: string,
        body: string,
        method: 'post' | 'put' | 'delete',
    ) => Promise<Completable<any>>;
    onSubmitEmailAction: (
        path: string,
        body: string,
        method: 'post' | 'put' | 'delete',
    ) => Promise<Completable<any>>;
    onRemoveUserHistory: (id: number) => Promise<Completable<any>>;
}

const UnderwritingEmployeeProfileContext = createContext<UnderwritingEmployeeProfileContextModel | undefined>(
    undefined,
);

interface UnderwritingEmployeeProfileProviderProps {
    children: React.ReactNode;
}

export const UnderwritingEmployeeProfileProvider: React.FC<UnderwritingEmployeeProfileProviderProps> = ({
    children,
}) => {
    const dispatch = useDispatch();
    const {showMessage} = useSnackbar();
    const {gid, employeeProfileState} = useUnderwritingProfile();
    const baseUrl = useMemo(() => `/api/secured/underwriting/employee-profile/${gid}`, [gid]);
    const [unmaskedSSN, setUnmaskedSSN] = useState<string | null>(null);
    const [openEmployeeProfileModal, setOpenEmployeeProfileModal] =
        useState<UnderwritingEmployeeProfileModalProps | null>(null);
    const [addressesState, setAddressesState] =
        useState(getDefaultState<UnderwritingEmployeeProfileAddressModel[]>());
    const [phonesState, setPhonesState] = useState(getDefaultState<PhoneDTO[]>());
    const [emailsState, setEmailsState] =
        useState(getDefaultState<UnderwritingEmployeeProfileEmailModel[]>());

    const loadUnmaskedSSN = async (id?: number) => {
        const url = `/api/secured/underwriting/employee-profile/${id || ''}/ssn`;
        const response: Completable<string> = await apiRequest(url);

        if (response.value) {
            setUnmaskedSSN(response.value);
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        return response;
    };

    useEffect(() => {
        if (employeeProfileState.loading) {
            setUnmaskedSSN(null);
        }
    }, [employeeProfileState.loading]);

    const loadAddressesData = async () => {
        const url = `${baseUrl}/addresses`;

        setAddressesState(getLoadingState(addressesState));

        const response: Completable<UnderwritingEmployeeProfileAddressModel[]> = await apiRequest(url);

        setAddressesState(getLoadedState(response));
    };

    const loadPhonesData = async () => {
        const url = `${baseUrl}/phones`;

        setPhonesState(getLoadingState(phonesState));

        const response: Completable<PhoneDTO[]> = await apiRequest(url);

        setPhonesState(getLoadedState(response));
    };

    const loadEmailsData = async () => {
        const url = `${baseUrl}/emails`;

        setEmailsState(getLoadingState(emailsState));

        const response: Completable<UnderwritingEmployeeProfileEmailModel[]> = await apiRequest(url);

        setEmailsState(getLoadedState(response));
    };

    const onSubmitEmployeeProfileAction = async (
        path: string,
        body: string,
        method: 'post' | 'put' | 'delete',
        onSuccess: () => Promise<void>,
    ) => {
        const url = `${baseUrl}/${path}`;
        const response = await apiRequest(url, {method, body});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await onSuccess();
            dispatch(eventUpdateUserProfile());
            setOpenEmployeeProfileModal(null);
        }

        return response;
    };

    const onRemoveUserHistory = async (id: number) => {
        const url = `/api/secured/power/user/${id}/history`;
        const response = await apiRequest(url, {method: 'delete'});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await loadEmailsData();
            dispatch(eventUpdateUserProfile());
            setOpenEmployeeProfileModal(null);
        }

        return response;
    };

    const onSubmitAddressAction = async (path: string, body: string, method: 'post' | 'put' | 'delete') => {
        return onSubmitEmployeeProfileAction(path, body, method, loadAddressesData);
    };

    const onSubmitPhoneAction = async (path: string, body: string, method: 'post' | 'put' | 'delete') => {
        return onSubmitEmployeeProfileAction(path, body, method, loadPhonesData);
    };

    const onSubmitEmailAction = async (path: string, body: string, method: 'post' | 'put' | 'delete') => {
        return onSubmitEmployeeProfileAction(path, body, method, loadEmailsData);
    };

    const value: UnderwritingEmployeeProfileContextModel = {
        unmaskedSSN,
        loadUnmaskedSSN,
        addressesState,
        loadAddressesData,
        phonesState,
        loadPhonesData,
        emailsState,
        loadEmailsData,
        openEmployeeProfileModal,
        setOpenEmployeeProfileModal,
        onSubmitAddressAction,
        onSubmitPhoneAction,
        onSubmitEmailAction,
        onRemoveUserHistory,
    };

    return (
        <UnderwritingEmployeeProfileContext.Provider value={value}>
            {children}
        </UnderwritingEmployeeProfileContext.Provider>
    );
};

export function useUnderwritingEmployeeProfile() {
    const context = useContext(UnderwritingEmployeeProfileContext);
    if (!context) {
        throw new Error(
            'useUnderwritingEmployeeProfile must be used within UnderwritingEmployeeProfileProvider',
        );
    }
    return context;
}
