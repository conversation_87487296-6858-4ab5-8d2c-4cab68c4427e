import React, {useEffect} from 'react';
import {
    ProfileItem,
    useUnderwritingBanks,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {BanksDetails} from './components';
import {Typography} from '@mui/material';
import {KasDotsMenuItemProps} from '@/components';
import {UnderwritingProfileItemModel} from '@/models';

interface BanksProps {
    item: UnderwritingProfileItemModel;
}

export const Banks = ({item}: BanksProps) => {
    const {banksState, loadBanksData, showBankAccountModal} = useUnderwritingBanks();

    const menuItems: KasDotsMenuItemProps[] = [
        {
            ContentComponent: <Typography variant='body1'>Add Bank</Typography>,
            onClick: showBankAccountModal,
        },
    ];

    useEffect(() => {
        loadBanksData().then();
    }, []);

    return (
        <ProfileItem
            onRefresh={loadBanksData}
            item={item}
            loading={banksState.loading}
            loadingError={banksState.error}
            loaded={!!banksState.data}
            menuItems={menuItems}
            DetailsComponent={<BanksDetails />}
        />
    );
};
