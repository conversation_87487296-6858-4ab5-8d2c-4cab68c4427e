import './styles.scss';

import React from 'react';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {BankInfo, CreateEditBankAccountModal, PreviewBankAccountModal} from './components';
import {KasLoadingBackDrop, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';

export const BanksDetails = () => {
    const {
        banksState: {loading, data, error},
        loadBanksData,
        openBankAccountPreviewModal,
    } = useUnderwritingBanks();

    return (
        <div className='kas-underwriting-banks-details'>
            {loading && <KasLoadingBackDrop />}
            <div className='kas-underwriting-banks-details__list'>
                <KasSwitch>
                    <KasSwitchWhen condition={!!data && !!data.length && !error}>
                        {data?.map((item) => <BankInfo key={item.gid} bank={item} />)}
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!error}>
                        <KasLoadingError error={error} onTryAgain={loadBanksData} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!data?.length}>
                        <NoResultsView text='This user doesn’t have any bank accounts.' />
                    </KasSwitchWhen>
                </KasSwitch>
            </div>
            <CreateEditBankAccountModal />
            {openBankAccountPreviewModal && <PreviewBankAccountModal />}
        </div>
    );
};
