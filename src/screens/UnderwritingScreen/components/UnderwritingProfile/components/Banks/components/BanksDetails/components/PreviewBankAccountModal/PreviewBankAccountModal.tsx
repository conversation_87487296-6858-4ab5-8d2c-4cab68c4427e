import React, {useEffect, useState} from 'react';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {Ka<PERSON>Loa<PERSON>, <PERSON>sLoading<PERSON>rror, KasModal, KasNoResults, KasSwitch, KasSwitch<PERSON>hen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {XmlInfo} from './components';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const PreviewBankAccountModal = () => {
    const {gid} = useUnderwritingProfile();
    const {openBankAccountPreviewModal, toggleBankAccountPreviewModal} = useUnderwritingBanks();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [xmlData, setXmlData] = useState<string>('');

    const loadData = () => {
        (async () => {
            setLoading(true);
            const response = await apiRequest(
                `/api/secured/underwriting/banks/${gid}/report?id=${openBankAccountPreviewModal?.gid}`,
            );

            if (response.value || response.value === '') {
                setXmlData(response.value);
            } else {
                setError(response.error || DEFAULT_ERROR_MSG);
            }

            setLoading(false);
        })();
    };

    useEffect(loadData, []);

    return (
        <KasModal
            title='Report Preview'
            open={!!openBankAccountPreviewModal}
            onClose={() => toggleBankAccountPreviewModal(null)}>
            <KasSwitch>
                <KasSwitchWhen condition={loading && !xmlData}>
                    <KasLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!error}>
                    <KasLoadingError error={error} onTryAgain={loadData} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={xmlData === ''}>
                    <KasNoResults text='No data found' p={2} bgcolor='var(--color-grey)' />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!xmlData && !!openBankAccountPreviewModal}>
                    <XmlInfo
                        xmlString={xmlData}
                        validationDate={openBankAccountPreviewModal?.validation_date}
                    />
                </KasSwitchWhen>
            </KasSwitch>
        </KasModal>
    );
};
