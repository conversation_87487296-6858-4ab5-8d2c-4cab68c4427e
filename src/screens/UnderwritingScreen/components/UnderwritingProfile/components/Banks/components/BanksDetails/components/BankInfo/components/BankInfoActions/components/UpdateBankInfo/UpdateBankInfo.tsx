import './styles.scss';

import React, {useEffect, useState} from 'react';
import {ActionCell} from '@/components/table/cells';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {Button, FormHelperText, TextField, Grid2} from '@mui/material';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {KasModal} from '@/components';
import {UpdateBankAccountValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import {DEFAULT_SUCCESS_MSG} from '@/constants';
import { TestableProps } from '@/screens/UnderwritingScreen/interfaces/testable';

interface UpdateBankInfoProps extends TestableProps {
    title: string;
    entityId: number;
    Icon: React.ReactNode;
    type: 'override' | 'flag' | 'unflag' | 'primary';
}

export const UpdateBankInfo = ({entityId, title, Icon, type, testid}: UpdateBankInfoProps) => {
    const [openModal, setOpenModal] = useState(false);
    const {showMessage} = useSnackbar();
    const {loadBanksData} = useUnderwritingBanks();
    const [submitting, setSubmitting] = useState(false);
    const [actionError, setActionError] = useState('');

    const onSubmit = async (values: UpdateBankAccountValues) => {
        const url = `/api/secured/underwriting/banks/${type === 'flag' || 'unflag' ? `flag?action=${type}` : type}`;
        const payload = {
            entity_class: 'Bank',
            entity_id: entityId,
            comment: values.comment,
        };
        const body = JSON.stringify(type === 'primary' ? {...payload, disbursement: false} : payload);

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'put', body});

        if (response.error) {
            setActionError(response.error);
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await loadBanksData();
            setOpenModal(false);
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        setActionError('');
    }, [formik.values]);

    return (
        <>
            <ActionCell Icon={Icon} onClick={() => setOpenModal(true)} testid={testid + '-update-cell'} />
            <KasModal title={title} open={openModal} onClose={() => setOpenModal(false)} testid={testid + '-update-modall'}>
                <form className='kas-underwriting-update-bank-info-form' onSubmit={formik.handleSubmit}>
                    <Grid2 container>
                        <Grid2 size={12}>
                            <TextField
                                fullWidth
                                size='small'
                                name='comment'
                                disabled={submitting}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                label='Comment'
                                variant='outlined'
                                error={!!formik.errors.comment && formik.touched.comment}
                                helperText={formik.touched.comment && formik.errors.comment}
                            />
                        </Grid2>
                    </Grid2>
                    {actionError && <FormHelperText error={true}>{actionError}</FormHelperText>}
                    <div className='kas-underwriting-update-bank-info-form__footer'>
                        <Grid2 container justifyContent='flex-end' spacing={2}>
                            <Grid2 size={3}>
                                <Button
                                    variant='outlined'
                                    fullWidth
                                    size='small'
                                    onClick={() => setOpenModal(false)}>
                                    Cancel
                                </Button>
                            </Grid2>
                            <Grid2 size={3}>
                                <Button
                                    variant='contained'
                                    fullWidth
                                    size='small'
                                    type='submit'
                                    disabled={!formik.isValid || submitting}>
                                    OK
                                </Button>
                            </Grid2>
                        </Grid2>
                    </div>
                </form>
            </KasModal>
        </>
    );
};
