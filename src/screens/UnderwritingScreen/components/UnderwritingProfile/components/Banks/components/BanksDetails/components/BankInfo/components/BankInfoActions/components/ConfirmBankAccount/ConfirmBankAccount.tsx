import React, {useState} from 'react';
import {ActionCell} from '@/components/table/cells';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {CircularProgress} from '@mui/material';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {KasFlaggedIcon} from '@/components';
import {BankModel} from '@/interfaces';

interface ConfirmBankAccountProps {
    bank: BankModel;
}

export const ConfirmBankAccount = ({bank}: ConfirmBankAccountProps) => {
    const {showMessage} = useSnackbar();
    const {loadBanksData} = useUnderwritingBanks();
    const [loading, setLoading] = useState(false);

    const confirmationBankAccount = async () => {
        setLoading(true);

        const response = await apiRequest(`/api/secured/underwriting/banks/confirmation?id=${bank.gid}`, {
            method: 'PUT',
        });

        if (response.value) {
            await loadBanksData();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setLoading(false);
    };

    return (
        <ActionCell
            disabled={!bank.active || loading}
            key={`confirmed-${bank.gid}`}
            Icon={
                loading ? (
                    <CircularProgress size={18} />
                ) : (
                    <KasFlaggedIcon
                        flagged={bank.confirmed}
                        titleAccess={bank.confirm_date ? `Confirmed: ${bank.confirm_date}` : 'Confirm Bank'}
                    />
                )
            }
            onClick={confirmationBankAccount}
        />
    );
};
