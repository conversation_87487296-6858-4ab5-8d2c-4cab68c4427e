import './styles.scss';

import React, {useEffect, useState} from 'react';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {Button, FormControl, FormHelperText, InputLabel, Select, TextField, Grid2} from '@mui/material';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {CreateEditBankAccountValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import MenuItem from '@mui/material/MenuItem';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {KasMaskInput} from '@/components';
import {DEFAULT_SUCCESS_MSG} from '@/constants';
import {BankModel} from '@/interfaces';

export const CreateEditBankAccountForm = () => {
    const {showMessage} = useSnackbar();
    const {gid} = useUnderwritingProfile();
    const {loadBanksData, closeBankAccountModal, selectedBankAccount} = useUnderwritingBanks();
    const [submitting, setSubmitting] = useState(false);
    const [actionError, setActionError] = useState('');

    const onSubmit = async (values: CreateEditBankAccountValues) => {
        const method = selectedBankAccount ? 'put' : 'post';
        const url = `/api/secured/underwriting/banks/${gid}/bank/${selectedBankAccount ? '?id=' + selectedBankAccount.gid : ''}`;
        const payload: Partial<BankModel> = {account_type: values.accountType};

        if (!selectedBankAccount) {
            payload.aba_number = values.abaNumber;
            payload.account_number = values.accountNumber;
        }

        const body = JSON.stringify(payload);

        setSubmitting(true);

        const response = await apiRequest(url, {method, body});

        if (response.error) {
            setActionError(response.error);
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await loadBanksData();
            closeBankAccountModal();
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            abaNumber: selectedBankAccount?.aba_number || '',
            accountNumber: selectedBankAccount?.account_number || '',
            accountType: selectedBankAccount?.account_type || '',
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        setActionError('');
    }, [formik.values]);

    return (
        <form className='kas-underwriting-create-edit-bank-account-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasMaskInput
                        formik={formik}
                        name='abaNumber'
                        disabled={submitting || !!selectedBankAccount?.aba_number}
                        mask='*********'
                        maskPlaceholder=''>
                        <TextField
                            fullWidth
                            name='abaNumber'
                            size='small'
                            variant='outlined'
                            placeholder={formik.values.abaNumber}
                            label='ABA Number'
                            disabled={submitting || !!selectedBankAccount?.aba_number}
                            value={formik.values.abaNumber}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={!!formik.errors.abaNumber && formik.touched.abaNumber}
                            helperText={formik.touched.abaNumber && formik.errors.abaNumber}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={6}>
                    <KasMaskInput
                        formik={formik}
                        name='accountNumber'
                        disabled={submitting || !!selectedBankAccount?.account_number}
                        mask='***************'
                        maskPlaceholder=''>
                        <TextField
                            fullWidth
                            name='accountNumber'
                            size='small'
                            variant='outlined'
                            placeholder={formik.values.accountNumber}
                            label='Account Number'
                            disabled={submitting || !!selectedBankAccount?.account_number}
                            value={formik.values.accountNumber}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={!!formik.errors.accountNumber && formik.touched.accountNumber}
                            helperText={formik.touched.abaNumber && formik.errors.accountNumber}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={12}>
                    <FormControl
                        fullWidth
                        size='small'
                        variant='outlined'
                        error={formik.touched.accountType && !!formik.errors.accountType}>
                        <InputLabel id='select-label'>Account Type</InputLabel>
                        <Select
                            labelId='select-label'
                            id='select-field'
                            name='accountType'
                            label='Account Type'
                            value={formik.values.accountType}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <MenuItem value='Checking'>Checking</MenuItem>
                            <MenuItem value='Savings'>Savings</MenuItem>
                            <MenuItem value='Prepaid Card'>Prepaid Card</MenuItem>
                        </Select>
                        {formik.touched.accountType && !!formik.errors.accountType && (
                            <FormHelperText error={true}>{formik.errors.accountType}</FormHelperText>
                        )}
                    </FormControl>
                </Grid2>
            </Grid2>
            {actionError && <FormHelperText error={true}>{actionError}</FormHelperText>}
            <div className='kas-underwriting-create-edit-bank-account-form__footer'>
                <Grid2 container justifyContent='flex-end' spacing={2}>
                    <Grid2 size={3}>
                        <Button variant='outlined' fullWidth size='small' onClick={closeBankAccountModal}>
                            Close
                        </Button>
                    </Grid2>
                    <Grid2 size={3}>
                        <Button
                            variant='contained'
                            fullWidth
                            size='small'
                            type='submit'
                            disabled={!formik.isValid || submitting}>
                            Save
                        </Button>
                    </Grid2>
                </Grid2>
            </div>
        </form>
    );
};
