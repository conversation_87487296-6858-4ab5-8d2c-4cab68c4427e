import React, {useMemo} from 'react';
import {Paper, Stack, Grid2} from '@mui/material';
import {KasInfo} from '@/components';
import Box from '@mui/material/Box';

interface XmlInfoProps {
    xmlString: string;
    validationDate?: string;
}

export const XmlInfo = ({xmlString, validationDate}: XmlInfoProps) => {
    const parseXmlData = (element: Element | null | undefined, selector: string): string | null => {
        const selectedNode = element?.querySelector(selector);

        return selectedNode ? selectedNode.textContent : null;
    };

    const parseXMLToHtml = useMemo(() => {
        if (!xmlString) {
            return <div>No XML data available</div>;
        }

        const parser = new DOMParser();
        const xml = parser.parseFromString(xmlString, 'application/xml');

        const root = xml.querySelector('xml-response');
        const clearProductsRequest = root?.querySelector('clear-products-request');
        const trackingNumber = parseXmlData(root, 'tracking-number') || null;
        const action = parseXmlData(root, 'action') || null;
        const denyCodes = parseXmlData(root, 'deny-codes') || null;
        const exceptionDescriptions = parseXmlData(root, 'exception-descriptions')?.split('|') || [];
        const denyDescriptions = parseXmlData(root, 'deny-descriptions')?.split('|') || [];
        const clearBankBehavior =
            root?.querySelector('clear-bank-behavior') || root?.querySelector('clear-bank');
        const clearBankAction = parseXmlData(clearBankBehavior, 'action') || null;
        const clearBankScore =
            clearBankBehavior?.querySelector('cbb-score')?.textContent ||
            clearBankBehavior?.querySelector('clear-bank-score')?.textContent ||
            null;
        const clearBankCodes =
            parseXmlData(clearBankBehavior, 'clear-bank-reason-codes')?.replace(/,/g, '|') ||
            parseXmlData(clearBankBehavior, 'deny-codes')?.replace(/,/g, '|') ||
            null;
        const reasonDescriptions =
            parseXmlData(clearBankBehavior, 'cbb-reason-code-description')?.split('|') ||
            parseXmlData(clearBankBehavior, 'clear-bank-reason-code-description')?.split('|') ||
            [];
        const accounts = root?.querySelector('accounts') || root?.querySelector('early-warning-histories');
        const accountNodes =
            accounts?.querySelectorAll('account') ||
            accounts?.querySelectorAll('early-warning-history') ||
            [];

        const renderList = (list: string[]) => {
            if (!list.length) {
                return null;
            }

            return (
                <Box pl={2}>
                    <ul>
                        {list.map((item, index) => (
                            <li key={index}>{item}</li>
                        ))}
                    </ul>
                </Box>
            );
        };

        return (
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Paper elevation={0}>
                        <Stack flexDirection='row' spacing={2} useFlexGap flexWrap='wrap' p={1.5}>
                            <KasInfo label='Report Version:'>
                                {parseXmlData(clearProductsRequest, 'control-file-name')} v
                                {parseXmlData(clearProductsRequest, 'control-file-version-number')}
                            </KasInfo>
                            <KasInfo label='Report Date:'>{validationDate}</KasInfo>
                            <KasInfo label='Tracking #:'>{trackingNumber}</KasInfo>
                        </Stack>
                    </Paper>
                </Grid2>
                <Grid2 size={12}>
                    <Paper elevation={0}>
                        <Stack flexDirection='row' spacing={2} useFlexGap flexWrap='wrap' p={1.5}>
                            <KasInfo label='Action:'>{action}</KasInfo>
                            <KasInfo label='Deny Codes:'>{denyCodes}</KasInfo>
                            <KasInfo label='Exception Descriptions:'>
                                {renderList(exceptionDescriptions)}
                            </KasInfo>
                            <KasInfo label='Deny Descriptions:'>{renderList(denyDescriptions)}</KasInfo>
                        </Stack>
                    </Paper>
                </Grid2>
                <Grid2 size={12}>
                    <Paper elevation={0}>
                        <Stack flexDirection='row' spacing={2} useFlexGap flexWrap='wrap' p={1.5}>
                            <KasInfo label='ClearBank Action:'>{clearBankAction}</KasInfo>
                            <KasInfo label='ClearBank Score:'>{clearBankScore}</KasInfo>
                            <KasInfo label='ClearBank Codes:'>{clearBankCodes}</KasInfo>
                            <KasInfo label='Reason Descriptions:'>{renderList(reasonDescriptions)}</KasInfo>
                        </Stack>
                    </Paper>
                </Grid2>
                <Grid2 size={12}>
                    <div className='kas-table'>
                        <div className='kas-table-content'>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Bank</th>
                                        <th>Bank Risk</th>
                                        <th>ABA</th>
                                        <th>Account</th>
                                        <th>Acct Risk</th>
                                        <th>Acct Age</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {Array.from(accountNodes).map((account, index) => {
                                        const bankName = parseXmlData(account, 'bank-name') || null;
                                        const bankRiskLevel =
                                            parseXmlData(account, 'bank-risk-level') ||
                                            parseXmlData(account, 'high-risk-bank') ||
                                            '';
                                        const routingNumber =
                                            parseXmlData(account, 'routing-number') ||
                                            parseXmlData(account, 'bank-routing-number') ||
                                            '';
                                        const accountNumber =
                                            parseXmlData(account, 'account-number') ||
                                            parseXmlData(account, 'masked-bank-account-number') ||
                                            '';
                                        const accountRiskLevel =
                                            parseXmlData(account, 'account-risk-level') ||
                                            parseXmlData(account, 'account-behavior-code') ||
                                            '';
                                        const accountAgeCode =
                                            parseXmlData(account, 'account-age-code') || null;
                                        let accountAge = 'N/A';

                                        switch (accountAgeCode) {
                                            case '0':
                                                accountAge = 'Never seen';
                                                break;
                                            case '1':
                                                accountAge = '< 30 days';
                                                break;
                                            case '2':
                                                accountAge = '30-59 days';
                                                break;
                                            case '3':
                                                accountAge = '60-89 days';
                                                break;
                                            case '4':
                                                accountAge = '90-179 days';
                                                break;
                                            case '5':
                                                accountAge = '180-360 days';
                                                break;
                                            case '6':
                                                accountAge = '> 1 year';
                                                break;
                                            case '7':
                                                accountAge = '> 2 years';
                                                break;
                                            case '8':
                                                accountAge = '> 3 years';
                                                break;
                                            case '9':
                                                accountAge = '> 4 years';
                                                break;
                                            default:
                                                accountAge = 'N/A';
                                                break;
                                        }

                                        return (
                                            <tr key={index}>
                                                <td>{bankName}</td>
                                                <td>{bankRiskLevel}</td>
                                                <td>{routingNumber}</td>
                                                <td>{accountNumber}</td>
                                                <td>{accountRiskLevel}</td>
                                                <td>{accountAge}</td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </Grid2>
            </Grid2>
        );
    }, [xmlString, parseXmlData]);

    return <>{parseXMLToHtml}</>;
};
