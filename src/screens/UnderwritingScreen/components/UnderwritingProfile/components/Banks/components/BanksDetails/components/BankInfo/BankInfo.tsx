import './styles.scss';

import React, {useEffect, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Stack} from '@mui/material';
import {
    KasExpandIcon,
    KasFlaggedIcon,
    KasInfo,
    KasLink,
    KasLoading,
    KasLoadingError,
    KasStrike,
    KasSwitch,
    KasSwitchWhen,
} from '@/components';
import {ReportProblemOutlined} from '@mui/icons-material';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable} from '@/interfaces';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {BankInfoActions, BankInfoDuplicateAccounts} from './components';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {BankDetailsModel, BankModel} from '@/interfaces';

interface BankInfoProps {
    bank: BankModel;
}

export const BankInfo = ({bank}: BankInfoProps) => {
    const {gid} = useUnderwritingProfile();
    const {showBankAccountModal, toggleBankAccountPreviewModal} = useUnderwritingBanks();
    const [expanded, setExpanded] = useState(false);
    const [bankState, setBankState] = useState(getDefaultState<BankDetailsModel>());

    const loadData = async () => {
        setBankState(getLoadingState(bankState));

        const response: Completable<BankDetailsModel> = await apiRequest(
            `/api/secured/underwriting/banks/${gid}/bank?id=${bank.gid}`,
        );

        setBankState(getLoadedState(response));
    };

    useEffect(() => {
        if (expanded && !bankState.data && !bankState.loading) {
            loadData().then();
        }
    }, [expanded]);

    return (
        <div className='kas-underwriting-bank-info' data-testid='uw-bank-info'>
            <Accordion disableGutters expanded={expanded} elevation={0}>
                <AccordionSummary
                    component='div'
                    aria-controls={bank.gid.toString()}
                    id={bank.gid.toString()}
                    expandIcon={
                        <KasExpandIcon
                            expanded={true}
                            onClick={() => setExpanded(!expanded)}
                            testid='uw-bank-info'
                        />
                    }>
                    <Stack direction='row' spacing={3} px={1}>
                        <KasInfo label='ID'>
                            <KasStrike isStrike={!bank.active} testid='uw-bank-info-id'>
                                <KasLink onClick={() => toggleBankAccountPreviewModal(bank)}>
                                    {bank.gid}
                                </KasLink>
                            </KasStrike>
                        </KasInfo>
                        <KasInfo label='Type'>
                            <KasStrike isStrike={!bank.active} testid='uw-bank-info-type'>
                                <KasLink onClick={() => showBankAccountModal(bank)}>
                                    {bank.account_type}
                                </KasLink>
                            </KasStrike>
                        </KasInfo>
                        <KasInfo label='Provider'>
                            <KasStrike isStrike={!bank.active}>{bank.validation_provider}</KasStrike>
                        </KasInfo>
                        <KasInfo label='Date'>
                            <KasStrike isStrike={!bank.active}>{bank.date_received}</KasStrike>
                        </KasInfo>
                        <KasInfo label='ABA Number'>
                            <KasStrike isStrike={!bank.active}>
                                {bank.name ? (
                                    <abbr title={bank.name}>{bank.aba_number}</abbr>
                                ) : (
                                    bank.aba_number
                                )}
                            </KasStrike>
                        </KasInfo>
                        <KasInfo label='Account Number'>
                            <KasStrike isStrike={!bank.active}>{bank.account_number}</KasStrike>
                        </KasInfo>
                        <KasInfo label='Source'>
                            <KasStrike isStrike={!bank.active}>{bank.entry_source}</KasStrike>
                        </KasInfo>
                        <KasInfo label='Primary'>
                            <KasFlaggedIcon flagged={bank.primary_account} testid='uw-bank-info-primary' />
                        </KasInfo>
                        <KasInfo label='Validated'>
                            {bank.status && bank.status.match(/.*(error|failed).*/i) ? (
                                <ReportProblemOutlined
                                    color='error'
                                    fontSize='small'
                                    titleAccess='ERROR: Failed to Pull Bank Report'
                                />
                            ) : (
                                <KasFlaggedIcon flagged={bank.validated} testid='uw-bank-info-validated' />
                            )}
                        </KasInfo>
                        <KasInfo label='Override'>
                            <KasFlaggedIcon flagged={bank.override} testid='uw-bank-info-override' />
                        </KasInfo>
                        <KasInfo label='Override By'>{bank.override_user_name}</KasInfo>
                        <KasInfo label='Action'>
                            <BankInfoActions bank={bank} />
                        </KasInfo>
                    </Stack>
                </AccordionSummary>
                <AccordionDetails>
                    <div className='kas-underwriting-bank-info__details' data-testid='uw-bank-info-details'>
                        <KasSwitch>
                            <KasSwitchWhen condition={bankState.loading}>
                                <KasLoading />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!bankState.error}>
                                <KasLoadingError error={bankState.error} onTryAgain={loadData} />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!bankState.data}>
                                <Stack direction='row' spacing={3} px={2} useFlexGap flexWrap='wrap'>
                                    <KasInfo label='Created'>{bankState.data?.create_time}</KasInfo>
                                    <KasInfo label='Last Updated'>{bankState.data?.last_update_time}</KasInfo>
                                    <KasInfo label='Confirm Requested'>
                                        {bankState.data?.confirm_requested || null}
                                    </KasInfo>
                                    <KasInfo label='Confirmed'>{bankState.data?.confirmed || null}</KasInfo>
                                    <KasInfo label='Comments'>
                                        {bankState.data?.comments?.length
                                            ? bankState.data.comments.map((comment, index) => (
                                                  <p key={`comment-${index}-${bank.gid}`}>{comment}</p>
                                              ))
                                            : null}
                                    </KasInfo>
                                    <KasInfo label='Duplicate accounts'>
                                        {bankState.data?.duplicates?.length ? (
                                            <BankInfoDuplicateAccounts
                                                duplicates={bankState.data.duplicates}
                                            />
                                        ) : null}
                                    </KasInfo>
                                </Stack>
                            </KasSwitchWhen>
                        </KasSwitch>
                    </div>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
