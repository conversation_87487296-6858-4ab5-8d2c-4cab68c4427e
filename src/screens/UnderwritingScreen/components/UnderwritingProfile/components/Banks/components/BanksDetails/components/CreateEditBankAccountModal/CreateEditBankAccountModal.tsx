import React from 'react';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {KasModal} from '@/components';
import {CreateEditBankAccountForm} from './components';

export const CreateEditBankAccountModal = () => {
    const {openBankAccountModal, closeBankAccountModal, selectedBankAccount} = useUnderwritingBanks();

    return (
        <KasModal
            title={`${selectedBankAccount ? 'Edit' : 'Add'} Bank Account`}
            open={openBankAccountModal}
            onClose={closeBankAccountModal}
            testid='uw-bank-create-edit'
        >
            <CreateEditBankAccountForm />
        </KasModal>
    );
};
