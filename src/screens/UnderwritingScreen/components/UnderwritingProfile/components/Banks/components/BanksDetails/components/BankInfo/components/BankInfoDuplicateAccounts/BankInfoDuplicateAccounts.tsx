import React, {Fragment} from 'react';
import {KasUnderwritingSharedLink} from '@/components';

interface BankInfoDuplicateAccountsProps {
    duplicates: number[];
}

export const BankInfoDuplicateAccounts = ({duplicates}: BankInfoDuplicateAccountsProps) => {
    return (
        <>
            {duplicates.map((id, index) => {
                return (
                    <Fragment key={id}>
                        {index ? ', ' : ''}
                        <KasUnderwritingSharedLink id={id} />
                    </Fragment>
                );
            })}
        </>
    );
};
