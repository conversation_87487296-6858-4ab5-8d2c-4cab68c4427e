import './styles.scss';

import React, {useEffect, useState} from 'react';
import {ActionCell} from '@/components/table/cells';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {Al<PERSON>, Button, FormHelperText, Grid2} from '@mui/material';
import {useUnderwritingBanks} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/components';
import {KasModal} from '@/components';
import {useFormik} from 'formik';
import {LibraryBooks} from '@mui/icons-material';
import {DEFAULT_SUCCESS_MSG} from '@/constants';

interface PullBankReportProps {
    url: string;
}

export const PullBankReport = ({url}: PullBankReportProps) => {
    const title = 'Pull Bank Report';
    const [openModal, setOpenModal] = useState(false);
    const {showMessage} = useSnackbar();
    const {loadBanksData} = useUnderwritingBanks();
    const [submitting, setSubmitting] = useState(false);
    const [actionError, setActionError] = useState('');

    const onSubmit = async () => {
        setSubmitting(true);

        const response = await apiRequest(url, {method: 'post'});

        if (response.error) {
            setActionError(response.error);
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            await loadBanksData();
            setOpenModal(false);
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    useEffect(() => {
        setActionError('');
    }, [formik.values]);

    return (
        <>
            <ActionCell
                Icon={<LibraryBooks color='primary' titleAccess={title} />}
                onClick={() => setOpenModal(true)}
                testid='uw-bank-pull-report'
            />
            <KasModal title={title} open={openModal} onClose={() => setOpenModal(false)} testid='uw-bank-pull-report'>
                <form className='kas-underwriting-pull-bank-report-form' onSubmit={formik.handleSubmit}>
                    <Grid2 container spacing={2}>
                        <Grid2 size={12}>
                            <Alert severity='info'>Pull New Clarity Report?</Alert>
                        </Grid2>
                    </Grid2>
                    {actionError && <FormHelperText error={true}>{actionError}</FormHelperText>}
                    <div className='kas-underwriting-pull-bank-report-form__footer'>
                        <Grid2 container justifyContent='flex-end' spacing={2}>
                            <Grid2 size={3}>
                                <Button
                                    variant='outlined'
                                    fullWidth
                                    size='small'
                                    onClick={() => setOpenModal(false)}>
                                    Cancel
                                </Button>
                            </Grid2>
                            <Grid2 size={3}>
                                <Button
                                    variant='contained'
                                    fullWidth
                                    size='small'
                                    type='submit'
                                    disabled={!formik.isValid || submitting}>
                                    OK
                                </Button>
                            </Grid2>
                        </Grid2>
                    </div>
                </form>
            </KasModal>
        </>
    );
};
