import React from 'react';
import {Stack} from '@mui/material';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {AssuredWorkload, Delete, FolderOpen, LocationOn} from '@mui/icons-material';
import {DownloadCell} from '@/components/table/cells';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {useSecured} from '@/hooks/useSecured';
import {ConfirmBankAccount, PullBankReport, UpdateBankInfo} from './components';
import {BankModel} from '@/interfaces';

interface BankInfoActionsProps {
    bank: BankModel;
}

export const BankInfoActions = ({bank}: BankInfoActionsProps) => {
    const {hasAnyRole} = useSecured();
    const {gid} = useUnderwritingProfile();

    return (
        <Stack direction='row' spacing={1}>
            {bank.active && !bank.primary_account && (
                <UpdateBankInfo
                    entityId={bank.gid}
                    title={`Change bank with routing: ${bank.aba_number} and account: ${bank.account_number} to primary?`}
                    Icon={<LocationOn color='primary' titleAccess='Set Primary' />}
                    type='primary'
                    testid='uw-bank-set-primary'
                />
            )}
            {hasAnyRole(['KASH_ADMIN', 'KASH_SUPPORT:MANAGER']) && (
                <UpdateBankInfo
                    entityId={bank.gid}
                    title='Override Bank'
                    Icon={<AssuredWorkload color='primary' titleAccess='Override Bank' />}
                    type='override'
                    testid='uw-bank-set-override'
                />
            )}
            <ConfirmBankAccount bank={bank} />
            <KasSwitch>
                <KasSwitchWhen condition={bank.active}>
                    <UpdateBankInfo
                        entityId={bank.gid}
                        title='Mark Bank Inactive'
                        Icon={<Delete color='error' titleAccess='Mark Bank Inactive' />}
                        type='flag'
                        testid='uw-bank-set-inactive'
                    />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!bank.active && hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER'])}>
                    <UpdateBankInfo
                        entityId={bank.gid}
                        title='Mark Bank Active'
                        Icon={<FolderOpen color='primary' titleAccess='Mark Bank Active' />}
                        type='unflag'
                        testid='uw-bank-set-active'
                    />
                </KasSwitchWhen>
            </KasSwitch>
            {bank.active && !bank.validated && hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER']) && (
                <PullBankReport url={`/api/secured/underwriting/banks/${gid}/report?id=${bank.gid}`} />
            )}
            <DownloadCell
                key={`download-${bank.gid}`}
                titleAccess='Download Report'
                params={JSON.stringify({
                    path: `/secured/uw/profile/employee/${gid}/reports/bank/${bank.gid}/download`,
                })}
                testid={`uw-bank-info`}
            />
        </Stack>
    );
};
