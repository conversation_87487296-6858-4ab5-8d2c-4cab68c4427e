import React, {createContext, useContext, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {Completable} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DataStateInterface} from '@/interfaces';
import {BankModel} from '@/interfaces';

interface UnderwritingBanksContextModel {
    selectedBankAccount: BankModel | null;
    openBankAccountModal: boolean;
    showBankAccountModal: (bank?: BankModel) => void;
    toggleBankAccountPreviewModal: (bank: BankModel | null) => void;
    openBankAccountPreviewModal: BankModel | null;
    closeBankAccountModal: () => void;
    banksState: DataStateInterface<BankModel[]>;
    loadBanksData: () => Promise<void>;
}

const UnderwritingBanksContext = createContext<UnderwritingBanksContextModel | undefined>(undefined);

interface UnderwritingBanksProviderProps {
    children: React.ReactNode;
}

export const UnderwritingBanksProvider: React.FC<UnderwritingBanksProviderProps> = ({children}) => {
    const {gid} = useUnderwritingProfile();
    const baseUrl = useMemo(() => `/api/secured/underwriting/banks/${gid}`, [gid]);
    const [selectedBankAccount, setSelectedBankAccount] = useState<BankModel | null>(null);
    const [openBankAccountModal, setOpenBankAccountModal] = useState(false);
    const [openBankAccountPreviewModal, setOpenBankAccountPreviewModal] = useState<BankModel | null>(null);
    const [banksState, setBanksState] = useState(getDefaultState<BankModel[]>());

    const toggleBankAccountPreviewModal = (value: BankModel | null) => {
        setOpenBankAccountPreviewModal(value);
    };

    const showBankAccountModal = (bank?: BankModel) => {
        setSelectedBankAccount(bank ? bank : null);
        setOpenBankAccountModal(true);
    };

    const closeBankAccountModal = () => {
        setOpenBankAccountModal(false);
        setSelectedBankAccount(null);
    };

    const loadBanksData = async () => {
        setBanksState(getLoadingState(banksState));

        const response: Completable<BankModel[]> = await apiRequest(baseUrl);

        setBanksState(getLoadedState(response));
    };

    const value: UnderwritingBanksContextModel = {
        selectedBankAccount,
        openBankAccountModal,
        showBankAccountModal,
        closeBankAccountModal,
        toggleBankAccountPreviewModal,
        openBankAccountPreviewModal,
        banksState,
        loadBanksData,
    };

    return <UnderwritingBanksContext.Provider value={value}>{children}</UnderwritingBanksContext.Provider>;
};

export function useUnderwritingBanks() {
    const context = useContext(UnderwritingBanksContext);
    if (!context) {
        throw new Error('useUnderwritingBanks must be used within UnderwritingBanksProvider');
    }
    return context;
}
