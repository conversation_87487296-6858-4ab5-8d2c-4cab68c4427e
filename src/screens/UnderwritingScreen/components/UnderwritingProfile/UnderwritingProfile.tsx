import './styles.scss';

import React, {useEffect, useRef, useState} from 'react';
import {ProfileActionModal, ProfileAlerts, ProfileInfo, ProfileList} from './components';
import {useUnderwritingProfile} from '@/screens/UnderwritingScreen/components';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {KasErrorBoundary} from '@/components';

export const UnderwritingProfile = () => {
    const {activeProfile} = useAppSelector(selectUnderwritingState);
    const {profile} = useUnderwritingProfile();
    const [loaded, setLoaded] = useState(false);
    const containerRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        if (activeProfile?.uid === profile.uid && !loaded) {
            setLoaded(true);
        }
    }, [activeProfile?.uid, profile.uid]);

    if (!loaded) {
        return null;
    }

    return (
        <div
            className='kas-underwriting-profile'
            ref={containerRef}
            key={profile.uid}
            hidden={activeProfile?.uid !== profile.uid}>
            <ProfileAlerts />
            <ProfileInfo />
            <ProfileList />
            <KasErrorBoundary>
                <ProfileActionModal />
            </KasErrorBoundary>
        </div>
    );
};
