import './styles.scss';

import React from 'react';
import {
    UnderwritingWelcome,
    UnderwritingSearch,
    UnderwritingResults,
    UnderwritingTabs,
    UnderwritingProfile,
    UnderwritingProfileProvider,
} from '@/screens/UnderwritingScreen/components';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {cancelSearch, selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {KasLoadingBackDrop} from '@/components';
import useStickyObserver from '@/hooks/useStickyObserver';

export const UnderwritingContent = () => {
    const dispatch = useAppDispatch();
    const {containerRef} = useStickyObserver();
    const {isWelcomeScreen, searchValues, profiles, activeProfile, searchLoading} =
        useAppSelector(selectUnderwritingState);

    const onCancelSearch = () => {
        dispatch(cancelSearch());
    };

    if (!searchValues.length && isWelcomeScreen && !profiles.length) {
        return <UnderwritingWelcome />;
    }

    return (
        <div ref={containerRef} className='kas-underwriting-content'>
            <UnderwritingTabs />
            <div
                className='kas-underwriting-content__wrap'
                key={activeProfile?.uid || 'search'}
                hidden={!!activeProfile}>
                {searchLoading && <KasLoadingBackDrop onCancel={onCancelSearch} />}
                <UnderwritingSearch />
                <UnderwritingResults />
            </div>
            {profiles.map((profile) => (
                <UnderwritingProfileProvider key={profile.uid} profile={profile}>
                    <UnderwritingProfile />
                </UnderwritingProfileProvider>
            ))}
        </div>
    );
};
