import './styles.scss';

import React, {useState} from 'react';
import emptyImage from './sources/empty-image.svg';
import Image from 'next/image';
import {MessageView, SelectableSearchForm} from '@/views';
import {KasLoadingBackDrop} from '@/components';
import {Button} from '@mui/material';
import {SEARCH_OPTIONS} from '@/screens/UnderwritingScreen/data';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {cancelSearch, handleSearch, selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {SelectableSearchValueModel} from '@/interfaces';

export const UnderwritingWelcome = () => {
    const dispatch = useAppDispatch();
    const {searchLoading} = useAppSelector(selectUnderwritingState);
    const [selectedValue, setSelectedValue] = useState<string | null>(null);

    const onSubmit = async (values: SelectableSearchValueModel) => {
        dispatch(handleSearch(values));
    };

    const onCancelSearch = () => {
        dispatch(cancelSearch());
    };

    return (
        <div className='kas-underwriting-welcome'>
            {searchLoading && <KasLoadingBackDrop onCancel={onCancelSearch} />}
            <MessageView
                title='You don’t have any generated reports'
                subtitle='Please choose section to start'
                Image={<Image src={emptyImage} priority alt='Please choose section to start' />}>
                <div className='kas-underwriting-welcome__content'>
                    <SelectableSearchForm
                        options={SEARCH_OPTIONS}
                        placeholder='Enter ID, phone number, name, employer etc.'
                        loading={searchLoading}
                        selectedValue={selectedValue}
                        onSubmit={onSubmit}
                    />
                    <div className='kas-underwriting-welcome__options'>
                        {SEARCH_OPTIONS.map((item, index) => (
                            <Button
                                key={index}
                                disabled={searchLoading}
                                type='submit'
                                onClick={() => setSelectedValue(item.label)}
                                size={'small'}
                                variant={'outlined'}>
                                {item.label.replace(':', '')}
                            </Button>
                        ))}
                    </div>
                </div>
            </MessageView>
        </div>
    );
};
