import './styles.scss';

import React from 'react';
import {Button, Stack, Typography} from '@mui/material';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {useUnderwriting} from '@/screens/UnderwritingScreen/useUnderwriting';
import {DeleteOutline, UnfoldLess, UnfoldMore} from '@mui/icons-material';
import {KasAddSectionMenu} from '@/components';

export const UnderwritingHead = () => {
    const {
        addVisibleProfileItem,
        updateVisibleProfileItems,
        activeProfileHiddenItems,
        activeProfileVisibleItems,
        expandedItems,
        expandAllItems,
        collapseAllItems,
    } = useUnderwriting();
    const {activeProfile} = useAppSelector(selectUnderwritingState);

    const onRemoveAll = () => {
        updateVisibleProfileItems([]);
    };

    return (
        <div className='kas-underwriting-head'>
            <Typography variant='h3'>Underwriting Dashboard</Typography>
            {activeProfile && (
                <Stack direction='row' spacing={1}>
                    <Button
                        variant='outlined'
                        color='error'
                        size='small'
                        title='Remove All Sections'
                        disabled={!activeProfileVisibleItems.length}
                        onClick={onRemoveAll}>
                        <DeleteOutline fontSize='small' />
                    </Button>
                    <Button
                        variant='outlined'
                        size='small'
                        title='Hide All Sections'
                        disabled={!expandedItems.length}
                        onClick={collapseAllItems}>
                        <UnfoldLess fontSize='small' />
                    </Button>
                    <Button
                        variant='outlined'
                        size='small'
                        title='Show All Sections'
                        disabled={expandedItems.length === activeProfileVisibleItems.length}
                        onClick={expandAllItems}>
                        <UnfoldMore fontSize='small' />
                    </Button>
                    <KasAddSectionMenu items={activeProfileHiddenItems} onAddItem={addVisibleProfileItem} />
                </Stack>
            )}
        </div>
    );
};
