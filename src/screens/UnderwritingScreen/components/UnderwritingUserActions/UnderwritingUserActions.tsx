import React from 'react';
import {
    RemoveUserActionButton,
    UnlinkUser<PERSON>ctionButton,
    UnlockUserActionButton,
    UserActionButtonProps,
} from './components';
import {useSecured} from '@/hooks/useSecured';
import {Stack} from '@mui/material';

interface UnderwritingUserActionsProps extends UserActionButtonProps {
    locked: boolean;
    employmentVerified: boolean;
}

export const UnderwritingUserActions = ({
    locked,
    employmentVerified,
    ...rest
}: UnderwritingUserActionsProps) => {
    const {hasAnyRole} = useSecured();

    if (!hasAnyRole(['KASH_ADMIN', 'KASH_POWERUSER'])) {
        return null;
    }

    return (
        <Stack direction='row' alignItems='center' spacing={1}>
            {locked && <UnlockUserActionButton {...rest} />}
            {employmentVerified ? <UnlinkUserActionButton {...rest} /> : <RemoveUserActionButton {...rest} />}
        </Stack>
    );
};
