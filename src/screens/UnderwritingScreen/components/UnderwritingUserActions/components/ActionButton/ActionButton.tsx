import React from 'react';
import {ActionCell} from '@/components/table/cells';
import {Delete, Lock} from '@mui/icons-material';
import {KasUnlinkIcon} from '@/icons';
import {Button} from '@mui/material';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';
import {GlobalModal, GlobalModalActionProps, useGlobalModal} from '@/components';

export interface UserActionButtonProps extends GlobalModalActionProps {
    size?: 'small' | 'medium';
}

interface UserActionButtonExtendedProps extends UserActionButtonProps, TestableProps {
    modalType: GlobalModal.Unlink_User | GlobalModal.Remove_User | GlobalModal.Unlock_User;
    title: string;
    Icon: React.ReactElement;
    color?: 'error' | 'warning';
}

const ActionButton = ({
    userId,
    uid,
    size = 'medium',
    title,
    color,
    modalType,
    Icon,
    testid,
    onSuccess,
}: UserActionButtonExtendedProps) => {
    const {showGlobalModal} = useGlobalModal();

    const onClickHandler = () => {
        showGlobalModal({type: modalType, props: {userId, uid, onSuccess}});
    };

    return size === 'medium' ? (
        <Button
            variant='outlined'
            color={color}
            size='small'
            title={title}
            onClick={onClickHandler}
            data-testid={`${testid}-action-button`}>
            {Icon}
        </Button>
    ) : (
        <ActionCell Icon={Icon} onClick={onClickHandler} testid={testid} />
    );
};

export const UnlinkUserActionButton = (props: UserActionButtonProps) => {
    const title = 'Unlink User Account';

    return (
        <ActionButton
            {...props}
            testid='uw-action-user-unlink'
            title={title}
            color='warning'
            modalType={GlobalModal.Unlink_User}
            Icon={<KasUnlinkIcon color='warning' fontSize='small' titleAccess={title} />}
        />
    );
};

export const RemoveUserActionButton = (props: UserActionButtonProps) => {
    const title = 'Remove User';

    return (
        <ActionButton
            {...props}
            testid='uw-action-user-remove'
            title={title}
            color='error'
            modalType={GlobalModal.Remove_User}
            Icon={<Delete color='error' fontSize='small' titleAccess={title} />}
        />
    );
};

export const UnlockUserActionButton = (props: UserActionButtonProps) => {
    const title = 'Unlock User';

    return (
        <ActionButton
            {...props}
            testid='uw-action-user-unlock'
            title={title}
            color='warning'
            modalType={GlobalModal.Unlock_User}
            Icon={<Lock color='warning' fontSize='small' titleAccess={title} />}
        />
    );
};
