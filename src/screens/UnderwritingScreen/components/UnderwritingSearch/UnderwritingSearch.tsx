import './styles.scss';

import React from 'react';
import {Button, Chip, Paper, Stack, Typography, Grid2} from '@mui/material';
import {Close} from '@mui/icons-material';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {
    fetchAccounts,
    handleSearch,
    removeSearchValue,
    resetSearch,
    selectUnderwritingState,
} from '@/lib/slices/underwritingSlice';
import {SelectableSearchValueModel} from '@/interfaces';
import {SEARCH_OPTIONS} from '@/screens/UnderwritingScreen/data';
import {SelectableSearchForm} from '@/views';

export const UnderwritingSearch = () => {
    const dispatch = useAppDispatch();
    const {searchLoading, searchValues} = useAppSelector(selectUnderwritingState);

    const onSubmit = async (values: SelectableSearchValueModel) => {
        const result = await dispatch(handleSearch(values));

        return fetchAccounts.fulfilled.match(result);
    };

    const onRemoveSearchValue = async (item: SelectableSearchValueModel) => {
        dispatch(removeSearchValue(item));
    };

    return (
        <Paper className='kas-underwriting-search' elevation={0}>
            <Typography variant='subtitle1' mb={2}>
                Search
            </Typography>
            <Grid2 container spacing={1}>
                {!!searchValues.length && (
                    <Grid2>
                        <Stack direction='row' spacing={1}>
                            {searchValues.map((item) => (
                                <Chip
                                    key={item.text}
                                    label={item.text}
                                    disabled={searchLoading}
                                    variant='filled'
                                    onDelete={() => onRemoveSearchValue(item)}
                                    deleteIcon={<Close />}
                                />
                            ))}
                        </Stack>
                    </Grid2>
                )}
                <Grid2 size={6}>
                    <Grid2 container spacing={1}>
                        <Grid2 size={9.5}>
                            <SelectableSearchForm
                                options={SEARCH_OPTIONS}
                                placeholder='Enter ID, phone number, name, employer etc.'
                                loading={searchLoading}
                                onSubmit={onSubmit}
                            />
                        </Grid2>
                        <Grid2 size={2.5}>
                            <Button
                                onClick={() => {
                                    dispatch(resetSearch());
                                }}
                                fullWidth
                                variant='outlined'
                                disabled={!searchValues.length}>
                                Reset
                            </Button>
                        </Grid2>
                    </Grid2>
                </Grid2>
            </Grid2>
        </Paper>
    );
};
