import './styles.scss';

import React, {useMemo} from 'react';
import {ResultsTable} from './components';
import {ColumnDef} from '@tanstack/react-table';
import {UnderwritingProfileTableModel} from '@/interfaces';
import {UnderwritingProfileTableColumns} from './tables/UnderwritingProfilesTable';
import {
    underwritingApplicationStatus,
    underwritingEmail,
    underwritingFullName,
    underwritingId,
    underwritingPhone,
} from '@/utils/UnderwritingUtils';
import {Paper} from '@mui/material';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {ErrorView, NoResultsView} from '@/views';

export const UnderwritingResults = () => {
    const {searchResult, searchValues, searchError} = useAppSelector(selectUnderwritingState);

    const data: UnderwritingProfileTableModel[] = useMemo(() => {
        return searchResult.map((item) => {
            const name = underwritingFullName(item);
            const name_id = underwritingId(item);

            return {
                full_name: `${name} [${name_id}]`,
                name,
                name_id,
                phone: underwritingPhone(item),
                email: underwritingEmail(item),
                status: underwritingApplicationStatus(item),
                origin_data: item,
            };
        });
    }, [searchResult]);

    if (searchError) {
        return <ErrorView error={searchError} />;
    }

    if (!searchResult.length) {
        return (
            <NoResultsView
                text={
                    searchValues.length
                        ? 'There are no results based on this request'
                        : 'Search result will appear here'
                }
            />
        );
    }

    return (
        <Paper className='kas-underwriting-results' elevation={0}>
            <ResultsTable
                columns={
                    UnderwritingProfileTableColumns as ColumnDef<UnderwritingProfileTableModel, unknown>[]
                }
                data={data}
            />
        </Paper>
    );
};
