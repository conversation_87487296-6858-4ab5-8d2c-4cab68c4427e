import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {UnderwritingProfileTableModel} from '@/interfaces';
import {AccountCircle, Assignment} from '@mui/icons-material';
import React from 'react';
import {Chip} from '@mui/material';
import {UserActionsCell} from '@/screens/UnderwritingScreen/components/UnderwritingResults/components';
import {underwritingStatusColor} from '@/utils/UnderwritingUtils';
import {KasCopyText} from '@/components';

const columnHelper = createColumnHelper<UnderwritingProfileTableModel>();

export const UnderwritingProfileTableColumns = [
    columnHelper.accessor('full_name', {
        id: 'full_name',
        header: 'Name',
        cell: (props: CellContext<UnderwritingProfileTableModel, string>) => {
            const {origin_data, name, name_id} = props.row.original;
            const icon = () => {
                if (origin_data.employee_id && origin_data.user_id) {
                    return null;
                }
                return origin_data.employee_id ? (
                    <Assignment style={{fontSize: '14px'}} />
                ) : (
                    <AccountCircle style={{fontSize: '14px'}} />
                );
            };

            return (
                <KasCopyText textToCopy={`${name} [${name_id}]`} testid='uw-result-name'>
                    <strong>{name}</strong> [{name_id}] {icon()}
                </KasCopyText>
            );
        },
    }),
    columnHelper.accessor('origin_data.employer_name', {
        id: 'employer_name',
        header: 'Employer',
    }),
    columnHelper.accessor('phone', {
        id: 'phone',
        header: 'Phone Number',
        cell: (props: CellContext<UnderwritingProfileTableModel, string>) => (
            <KasCopyText testid='uw-result-phone'>{props.getValue()}</KasCopyText>
        ),
    }),
    columnHelper.accessor('email', {
        id: 'email',
        header: 'Email',
        cell: (props: CellContext<UnderwritingProfileTableModel, string>) => (
            <KasCopyText testid='uw-result-email'>{props.getValue()}</KasCopyText>
        ),
    }),
    columnHelper.accessor('status', {
        id: 'status',
        header: 'Status',
        cell: (props) => {
            const {status} = props.row.original;

            return (
                <Chip data-testid='uw-result-status'
                    label={status}
                    variant='outlined'
                    color={underwritingStatusColor(props.row.original.origin_data)}
                />
            );
        },
    }),
    columnHelper.accessor('origin_data.last_login_time', {
        id: 'last_login_time',
        header: 'Last Login',
        cell: (props) => {
            const {origin_data} = props.row.original;

            return origin_data.last_login_ip ? (
                <abbr title={`IP: ${origin_data.last_login_ip}`}>{origin_data.last_login_time}</abbr>
            ) : (
                origin_data.last_login_time
            );
        },
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<UnderwritingProfileTableModel, number>) => (
            <UserActionsCell data={props.row.original.origin_data} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
