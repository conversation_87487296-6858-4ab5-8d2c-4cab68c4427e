import React from 'react';
import {Stack} from '@mui/material';
import {OpenProfileActionButton} from '../../components';
import {UnderwritingDTO} from '@/models';
import {UnderwritingUserActions} from '@/screens/UnderwritingScreen/components';

export const UserActionsCell = ({data}: {data: UnderwritingDTO}) => {
    return (
        <Stack direction='row' alignItems='center' justifyContent='flex-end' spacing={1}>
            {data.user_id && (
                <UnderwritingUserActions
                    size='small'
                    uid={data.uid}
                    userId={data.user_id}
                    locked={data.locked}
                    employmentVerified={data.employment_verified}
                />
            )}
            <OpenProfileActionButton data={data} />
        </Stack>
    );
};
