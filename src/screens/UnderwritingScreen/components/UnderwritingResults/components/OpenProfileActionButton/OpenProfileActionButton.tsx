import React from 'react';
import {CallMade} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {UnderwritingDTO} from '@/models';
import {useAppDispatch} from '@/lib/hooks';
import {showProfileDetails} from '@/lib/slices/underwritingSlice';

interface OpenProfileActionButtonProps {
    data: UnderwritingDTO;
}

export const OpenProfileActionButton = ({data}: OpenProfileActionButtonProps) => {
    const dispatch = useAppDispatch();

    const onClickHandler = () => {
        dispatch(showProfileDetails(data));
    };

    return (
        <ActionCell Icon={<CallMade color='primary' titleAccess='Show Details' />} onClick={onClickHandler}
            testid='uw-action-profile-open' />
    );
};
