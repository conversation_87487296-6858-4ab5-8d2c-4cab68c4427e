import {KasTableHeadActions} from '@/components/table/KasTableHeadActions/KasTableHeadActions';
import {KasTableContent} from '@/components/table/KasTableContent/KasTableContent';
import {KasTablePagination} from '@/components/table';
import React, {useMemo, useState} from 'react';
import {useTable} from '@/hooks/useTable';
import {KasTableProps} from '@/components/KasTable';
import {Grid2, SelectChangeEvent} from '@mui/material';
import {KasSelect} from '@/components';
import MenuItem from '@mui/material/MenuItem';
import {uniqElements} from '@/utils/ArrayUtils';
import {UnderwritingProfileTableModel} from '@/interfaces';
import {PaginationState} from '@tanstack/react-table';

export const ResultsTable = ({columns, data, renderExpand}: KasTableProps<UnderwritingProfileTableModel>) => {
    const {table, setPagination} = useTable<UnderwritingProfileTableModel>(data || [], columns);
    const [selectedStatus, setSelectedStatus] = useState('');
    const [selectedEmployer, setSelectedEmployer] = useState('');

    const uniqStatuses = useMemo(() => {
        return uniqElements(table.getPrePaginationRowModel().rows, (item) => item.original.status);
    }, [table]);

    const uniqEmployers = useMemo(() => {
        return uniqElements(
            table.getPrePaginationRowModel().rows,
            (item) => item.original.origin_data.employer_name,
        );
    }, [table]);

    const onChange = (id: string) => (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;

        if (id === 'employer_name') {
            setSelectedEmployer(newValue);
        }

        if (id === 'status') {
            setSelectedStatus(newValue);
        }

        table.getColumn(id)?.setFilterValue(newValue !== 'All' ? newValue : '');
    };

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    return (
        <div className='kas-designed-table'>
            <KasTableHeadActions<UnderwritingProfileTableModel> table={table}>
                <Grid2 container spacing={1}>
                    <Grid2 size={2}>
                        <KasSelect
                            labelId='kas-accounting-revenue-filter-method'
                            value={selectedStatus}
                            label='Status'
                            onChange={onChange('status')}>
                            <MenuItem key={'all'} value={''}>
                                All
                            </MenuItem>
                            {uniqStatuses.map((status) => (
                                <MenuItem key={status} value={status}>
                                    {status}
                                </MenuItem>
                            ))}
                        </KasSelect>
                    </Grid2>
                    <Grid2 size={2}>
                        <KasSelect
                            labelId='kas-accounting-revenue-filter-method'
                            value={selectedEmployer}
                            label='Employer'
                            onChange={onChange('employer_name')}>
                            <MenuItem key={'all'} value={''}>
                                All
                            </MenuItem>
                            {uniqEmployers.map((status) => (
                                <MenuItem key={status} value={status}>
                                    {status}
                                </MenuItem>
                            ))}
                        </KasSelect>
                    </Grid2>
                </Grid2>
            </KasTableHeadActions>
            <KasTableContent table={table} renderExpand={renderExpand} />
            <KasTablePagination table={table} onChange={handleChange} />
        </div>
    );
};
