import './styles.scss';

import React from 'react';
import {underwritingFullName, underwritingId} from '@/utils/UnderwritingUtils';
import {UnderwritingSearchDTO} from '@/models';
import {Stack} from '@mui/material';

export const TabLabel = ({item}: {item: UnderwritingSearchDTO}) => {
    return (
        <Stack direction='row' spacing={0.5} className='kas-underwriting-tab-label'>
            <div className='kas-underwriting-tab-label__name'>{underwritingFullName(item)}</div>
            <div>[{underwritingId(item)}]</div>
        </Stack>
    );
};
