import React from 'react';
import {Stack} from '@mui/material';
import {useUnderwriting} from '@/screens/UnderwritingScreen/useUnderwriting';
import {KasLink} from '@/components';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {scrollToBlockById} from '@/utils/ContentUtils';
import {generateProfileItemId} from '@/utils/UnderwritingUtils';

export const QuickLinks = ({offsetTopScroll}: {offsetTopScroll: number}) => {
    const {activeProfileVisibleItems, expandedItems, updateExpandedItem} = useUnderwriting();
    const {activeProfile} = useAppSelector(selectUnderwritingState);

    if (!activeProfile || !activeProfileVisibleItems.length) {
        return null;
    }

    return (
        <Stack direction='row' useFlexGap flexWrap='wrap' spacing={1} sx={{fontSize: '14px'}}>
            {activeProfileVisibleItems.map((item) => (
                <KasLink
                    key={item.id}
                    onClick={async () => {
                        const id = generateProfileItemId(item.id, activeProfile.uid);

                        if (expandedItems.includes(item.id)) {
                            scrollToBlockById(id, offsetTopScroll);
                        } else {
                            updateExpandedItem(item.id, true);
                            // TODO: wait for the element to re-render in the DOM with a different block height.
                            await new Promise((resolve) => setTimeout(resolve, 500));
                            scrollToBlockById(id, offsetTopScroll);
                        }
                    }}>
                    {item.title}
                </KasLink>
            ))}
        </Stack>
    );
};
