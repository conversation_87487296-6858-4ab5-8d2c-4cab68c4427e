import React, {useState} from 'react';
import {Divider, Stack} from '@mui/material';
import {QuickLinks, TabLabel} from '@/screens/UnderwritingScreen/components/UnderwritingTabs/components';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {
    clearProfiles,
    removeProfile,
    selectUnderwritingState,
    setActiveProfile,
} from '@/lib/slices/underwritingSlice';
import {KasCopyText} from '@/components';
import {SearchTabsView} from '@/views';
import Box from '@mui/material/Box';

export const UnderwritingTabs = () => {
    const dispatch = useAppDispatch();
    const {profiles, activeProfile} = useAppSelector(selectUnderwritingState);
    const [offsetTopScroll, setOffsetTopScroll] = useState(0);

    return (
        <SearchTabsView
            items={profiles}
            activeItem={activeProfile}
            onClickItem={(item) => dispatch(setActiveProfile(item))}
            onRemoveItem={(item) => dispatch(removeProfile(item.uid))}
            onCloseAll={() => dispatch(clearProfiles())}
            getItemLabel={(item) => <TabLabel item={item} />}
            isItemActive={(item, activeItem) => activeItem?.uid === item.uid}
            setOffsetTopScroll={setOffsetTopScroll}>
            {activeProfile && (
                <Box pt={1}>
                    <Divider />
                    <Stack direction='row' justifyContent='space-between' spacing={1} py={2}>
                        <KasCopyText>{activeProfile.employer_name}</KasCopyText>
                        <QuickLinks key={activeProfile?.uid} offsetTopScroll={offsetTopScroll} />
                    </Stack>
                </Box>
            )}
        </SearchTabsView>
    );
};
