import {AddressDTO, RecipientDTO} from '@/models';
import {RestrictPermissionType} from '@/models/restrictDTO';

export interface UnderwritingEmployeeProfileIntegrationModel {
    identifier: string;
    type: string;
    create_time: string;
    loan_references: UnderwritingLoanReferencesModel[];
}

export interface UnderwritingLoanReferencesModel {
    loan_reference_id: string;
    received: string;
}

export interface UnderwritingEmployeeProfileItemModel {
    gid: number;
    first_name: string;
    last_name: string;
    ssn: string | undefined;
    dob: string;
    status: string;
    initial_hire_date: string;
    last_hire_date: string;
    termination_date: string;
    payroll_group_id: number;
    id_at_employer: number;
    email_at_employer: string;
    job_code: string;
    position: string;
    department: string;
    full_part_time: string;
    pay_type: string;
    pay_rate: string;
    estimated_annual_hours_worked: string;
    commission: string;
    benefit_eligible_employee: boolean;
    income_flagged: boolean;
    phone1: string;
    phone1_type: string;
    phone2: string;
    phone2_type: string;
    payroll_deduction_status: string;
    payroll_deduction_ever_revoked: boolean;
    arbitration_agreement_status: string;
    arbitration_agreement_ever_revoked: boolean;
    current_loan_id: number;
    paper_notices: boolean;
    privacy_agreement_sent: boolean;
    merge_id: number;
    manual_review_date: string;
    bankruptcy_date: string;
    deceased_date: string;
    holiday_calendar_mode: string;
    user_id: number;
    guarantee: boolean;
    bank_data_source: string;
    bank_aba: string;
    bank_account_number: string;
    bank_account_type: string;
    primary_email: string;
    primary_phone: string;
    primary_address: string;
    identity_verified: boolean;
    benefit_ineligible_rule: string;
    employer_id: number;
    employer_name: string;
    primary_bank_id: number;
    account_balance: string;
    loan_balance: number;
    refund: number;
    workflow: string;
    employer_locked: boolean;
    ssn_locked: boolean;
    dob_locked: boolean;
    direct_deposit_account_number: string;
    direct_deposit_reported_date: string;
    application_status: string;
    backup_ach_status: string;
    merged_date: string;
    merged_from: number[] | null;
    bankruptcy_chapter: string;
    open_bankruptcy_petition: boolean;
    deceased: boolean;
    politically_exposed: string;
    politically_exposed_verify_time: string;
    phone_cnam: string;
    hardship: string;
    hardship_expiry_date: string;
    referral_code: string;
    review_matches_by_ip: {
        ip_address: string;
        last_seen: string;
        match_id: number;
        match_last_seen: string;
        link_comment: string;
    }[];
    refinance_eligible: boolean;
    refinance_eligible_date: string;
    collection_agency: string;
    deposit_confirmation_required: boolean;
    recent_account_change: boolean;
    scra_open_date: string;
    scra_close_date: string;
    integrations: UnderwritingEmployeeProfileIntegrationModel[];
    application_precheck: boolean;
    application_precheck_rule: string;
    restrictions: {
        [key in keyof RestrictPermissionType]: string | null;
    };
}

export interface UnderwritingEmployerCriteriaPropertyModel {
    op: string;
    value: string;
}

export interface UnderwritingEmployerCriteriaPropertiesModel {
    [key: string]: UnderwritingEmployerCriteriaPropertyModel[];
}

export interface UnderwritingEmployerCriteriaItemModel {
    result: string;
    step?: number;
    properties?: UnderwritingEmployerCriteriaPropertiesModel;
}

export interface UnderwritingEmployerCriteriaModel {
    rule: string;
    criteria: UnderwritingEmployerCriteriaItemModel[];
}

export interface UnderwritingEmployerModel {
    gid: number;
    mnemonic: string;
    name: string;
    industry: string;
    active: boolean;
    start_date: string;
    primary_employer_id: number;
    primary_employer_name: string;
    primary_employer_mnemonic: string;
    workflow: string;
    hr_name: string;
    hr_phone: string;
    hr_email: string;
    payroll_frequency: string;
    mode: string;
    sample_date: string;
    criteria: UnderwritingEmployerCriteriaModel[];
    deduction_mode: string;
    deduction_export?: string;
    deduction_consolidate?: boolean;
    payroll_group_id: number;
    payroll_deduction_lead_days?: number;
    census_upload_run_date?: string;
    payroll_upload_run_date?: string;
    deduction_upload_run_date?: string;
    employer_deduction_lead_days?: string;
}

export interface UnderwritingEmployeeProfileAddressModel extends AddressDTO {
    gid: number;
    zip_type: string;
}

export interface UnderwritingEmployeeProfileEmailModel {
    gid: number;
    employee_id: number;
    source: string;
    active: boolean;
    current: boolean;
    last_updated: string;
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: string;
    loan_id: number;
    history: boolean;
}

export interface UnderwritingEmployeeProfileEmailPreviewModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    employee_id: number;
    bank_id: number;
}
