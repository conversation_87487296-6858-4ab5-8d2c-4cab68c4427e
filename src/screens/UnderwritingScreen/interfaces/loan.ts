import {LoanStatusType, PaymentDTO, RecipientDTO} from '@/models';
import {AttachmentDTO} from '@/models/attachmentDTO';

export interface ScraModificationModel {
    start_date: string;
    end_date: string;
}

export interface UnderwritingLoanModel {
    gid: number;
    employee_id: number;
    amount: string;
    balance: string;
    installment_amount: string;
    start_date: string;
    close_date: string;
    loan_status: LoanStatusType;
    litigation: boolean;
    scra_modifications: ScraModificationModel[];
}

export interface UnderwritingLoanPayoffModel {
    loan_id: number;
    amount: string;
    until_date: string;
}

export interface UnderwritingLoanEmailPreviewModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    attachments: AttachmentDTO[];
    gid: number;
    employee_id: number;
    loan_id: number;
    source: string;
    active: boolean;
    current: boolean;
    history: boolean;
    last_updated: string;
    start_date: string | null;
    frequency: string;
    installment: string | null;
    amount: string | null;
    option: string;
    payment_type: string;
    partial_payment: boolean;
    request_date: string | null;
    bank_id: number;
    payments: PaymentDTO[];
    strict: boolean;
    revocation_reason: string;
}

export interface UnderwritingLoanLetterPreviewModel {
    sent_date: string;
    recipients: RecipientDTO[];
    id: number;
    url: string;
    html: string;
    letter_type: string;
    third_party_id: number;
    tracking_number: string;
    created: string;
    updated: string;
    sendable: boolean;
    loan_id?: number;
    offset_days: number | null;
    recovery_ratios?: number[];
    recovery_ratio?: number;
}
