export interface UnderwritingIPHistoryMatchModel {
    user_id: number;
    full_name: string;
    last_seen: string;
    manual_review_date: string | null;
}

export interface UnderwritingIPHistoryModel {
    ip_address: string;
    user_id: number;
    last_seen: string;
    whitelist: boolean;
    blacklist: boolean;
    mobile: boolean;
    description: string;
    matches: UnderwritingIPHistoryMatchModel[] | null;
}

export interface UnderwritingIPInfoModel {
    city?: string;
    country?: string;
    region?: string;
    postal?: string;
    timezone?: string;
    loc?: string;
    ip?: string;
    hostname?: string;
}
