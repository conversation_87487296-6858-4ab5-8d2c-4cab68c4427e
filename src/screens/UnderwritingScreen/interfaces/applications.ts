import {MetadataModel} from '@/interfaces';
import {CommentDTO} from '@/models/restrictDTO';
import {RecipientDTO} from '@/models';

export interface UnderwritingApplicationHistoryModel {
    gid: number;
    employee_id: number;
    session_id: string;
    loan_purpose: string;
    amount: string;
    max_kash: string;
    loan_id: number;
    document_by_signed_loan_id: number;
    document_by_signed_payroll_authorization_id: number;
    document_by_signed_arbitration_id: number;
    credit_authorized: boolean;
    user_agent: string;
    bank_id: number;
    calc_net_pay: number;
    device: string;
    context: string;
    create_time: string;
    last_update_time: string;
    decision_result: string;
    decision_rules?: string[];
    apr: string;
    interest_rate: string;
    origination_fee: string;
    processing_fee: string;
    address_state: string;
    credit_report_id: number;
    pre_qualify: boolean;
    signed: boolean;
    application_time: string;
    queues: string;
    send_email: string;
    pending_comm_count: number;
}

export interface UnderwritingDocumentHistoryModel {
    gid: number;
    type: string;
    name: string;
    upload_ts: string;
    active: boolean;
    verify_time: string;
    metadata: MetadataModel;
    last_update_user: string;
}

export interface DocumentQueueModel {
    gid: number;
    metadata: MetadataModel;
    last_update_user: string;
    key: string;
    date: string;
    comments?: CommentDTO[];
}

export interface UnderwritingVerificationHistoryModel {
    gid: number;
    application_id: number;
    employee_name: string;
    queue_code: string;
    loan_id: number;
    queue_time: string;
    verify_time: string;
    verify_user: string;
    archived: boolean;
    uploaded: boolean;
}

export interface UnderwritingVerificationPersistentHistoryModel {
    code: string;
    queue_time: string;
    verify_time: string;
    persistance_expiry_time: string;
    approval_expiry_time: string;
}

export interface UnderwritingEmployeeQueueModel {
    gid: string;
    code: string;
    queued: boolean;
    verified: boolean;
    queue_time: string;
    verify_time: string;
}

export interface UnderwritingApplicationTapeModel {
    applicationId: number;
    workflow: string;
    campaign: string;
    purpose: string;
    additionalIncome: string;
    offerings?: {
        [key: string]: number[];
    };
    schedule?: string[];
    applicationDate: string;
    firstInstallmentDate?: string;
    disbursementDate: string;
    expiresDate: string;
    disbursementAmount?: string;
    amount?: string;
    term?: number;
    origFee?: string;
    procFee?: string;
    intRateTarget?: number;
    aprTarget?: number;
    origFeeMax?: string;
    procFeeMax?: string;
    intRate?: number;
    renderIntRate?: number;
    intAmount?: string;
    apr?: number;
    renderApr?: number;
    installmentAmount?: string;
    installmentCount?: number;
    context: string;
    decision: boolean;
    depositEligible: boolean;
    employeeId: number;
    employeeStatus: string;
    employeePayType: string;
    employeeContractType: string;
    employeeDob: string;
    employeeAge: number;
    employeeTenure?: number;
    addressId: number;
    street1: string;
    street2?: string;
    city: string;
    zip: string;
    state: string;
    stateLicenseStatus: string;
    ssnHash: string;
    employerId: number;
    employerMnemonic: string;
    employerName: string;
    employerIndustryCode: string;
    employerIndustrySubcode: string;
    payrollGroupId: number;
    payrollGroupFrequency: string;
    payrollGroupMode: string;
    bankruptcy: boolean;
    deceased: boolean;
    wageType?: string;
    wageFunction?: string;
    wagePeriods?: number;
    wageAnnualized?: string;
    wagePerPay?: string;
    wageCalculated?: string;
    wageVariance?: string;
    wageRatio?: number;
    wageAdditional: string;
    dtiRatio?: number;
    scores?: {
        [key: string]: {
            action?: string;
            score: number;
            date: string;
            reasons?: {
                [key: string]: string;
            };
            hasScore: boolean;
        };
    };
    creditReportingAgency?: string;
    creditScore?: number;
    creditApproved?: boolean;
    riskReportingAgency?: string;
    riskScore?: number;
    device: string;
    loanOriginationSource: string;
    checkpoint: string;
    applicationTime?: string;
    declines?: string[];
    pre_qualify?: boolean;
    loanId?: number;
    previousLoanId?: number;
    previousLoanPayoffAmount?: string;
    escrows: [];
    history: {
        [key: string]: {
            status: string;
            amount: string;
            balance: string;
            currentDlqDays: number;
            maxDlqDays: number;
            collections: boolean;
        };
    };
    ruleDetails: {};
    reviewDetails: {};
    verification: {};
    guarantee?: {
        amount: string;
        term: string;
        rate: string;
        apr: string;
        fees: string;
    };
    leadgen?: {
        provider: string;
        source: string;
        campaign: string;
        medium: string;
        content: string;
    };
    partnerPpc?: {
        creditWorthy: boolean;
        orderCount: string;
        spendingLimit: string;
    };
    numberOfHoursWorked12mo?: string;
    wageLastDate?: string;
    error?: {
        message: string;
    };
}

export interface UnderwritingEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    entity_class: string;
    entity_id: number;
}
export interface UnderwritingEmailPreviewModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    gid: number;
    employee_id: number;
    application_id: number;
    source: string;
    active: boolean;
    current: boolean;
    history: boolean;
    last_updated: string;
    start_date: string | null;
    frequency: string;
    installment: string | null;
    amount: string | null;
    option: string;
    payment_type: string;
    partial_payment: boolean;
    request_date: string | null;
    bank_id: number;
    payments: string[];
    strict: boolean;
    revocation_reason: string;
    html: string;
}
