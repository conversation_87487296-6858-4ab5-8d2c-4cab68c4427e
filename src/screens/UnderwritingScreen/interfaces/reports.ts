export interface UnderwritingRiskPreviewReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    active: boolean;
    report: string;
    override: boolean;
    override_user_name: string;
    comments: string[];
}

export interface UnderwritingFraudPreviewReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    product: string;
    active: boolean;
    report: string;
    override: boolean;
    override_user_name: string;
    comments: string[];
}
