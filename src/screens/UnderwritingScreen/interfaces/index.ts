export * from './actions';
export * from './applications';
export * from './direct-deposit';
export * from './employee-profile';
export * from './ip-history';
export * from './loan';
export * from './modification-history';
export * from './payroll-history';
export * from './referrals';
export * from './reports';
export * from './transactions';

export interface SearchValueModel {
    param: string;
    value: string;
    text: string;
}

export interface UnderwritingUserProfileSocialModel {
    type: string;
    email: string | null;
    emailVerified: boolean;
    phone: string | null;
    phoneVerified: boolean;
    name: string;
    dob: string | null;
    created_date: string;
    last_changed: string;
}

export interface UnderwritingUserProfileItemModel {
    gid: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified: boolean;
    phone: string;
    phone_verified: boolean;
    phone_cnam: string;
    employment_verified: boolean;
    manual_review_date: string;
    last_login_time: string;
    last_login_ip: string;
    create_time: string;
    last_update_time: string;
    locked: boolean;
    socials: UnderwritingUserProfileSocialModel[];
    email_pending: string | null;
    email_added: string;
    email_verified_date: string | null;
    phone_pending: string | null;
    phone_added: string;
    phone_verified_date: string | null;
    employment_verified_date: string;
    employee_id: number;
    login_attempts: 0;
    zendesk_id: string;
    password_reset_request_time: string | null;
    build_version: string | null;
}
