import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {
    UnderwritingDTO,
    UnderwritingProfileItemModel,
    UnderwritingProfileItemType,
    UnderwritingSearchDTO,
} from '@/models';
import {addProfile, handleSharedProfile, setActiveProfile} from '@/lib/slices/underwritingSlice';
import {underwritingHash, underwritingId} from '@/utils/UnderwritingUtils';
import {PROFILES_KEY} from '@/constants';
import {KasAddSectionMenuItem, KasDotsMenuItemProps} from '@/components';
import {Typography, useTheme} from '@mui/material';
import {
    DEFAULT_PROFILE_ITEMS,
    EMPLOYEE_PROFILE_ITEMS,
    PROFILE_ITEMS_CONFIG_KEY,
    USER_PROFILE_ITEMS,
} from '@/screens/UnderwritingScreen/data';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {uniqElements} from '@/utils/ArrayUtils';
import {useHashHandler} from '@/hooks/useHashHandler';
import {UnderwritingEmployeeProfileItemModel} from '@/screens/UnderwritingScreen/interfaces';

interface UnderwritingContextModel {
    addVisibleProfileItem: (value: UnderwritingProfileItemModel) => void;
    activeProfileVisibleItems: UnderwritingProfileItemModel[];
    activeProfileHiddenItems: KasAddSectionMenuItem<UnderwritingProfileItemModel>[];
    updateVisibleProfileItems: (value: UnderwritingProfileItemModel[]) => void;
    createDeleteMenuItem: (itemType: UnderwritingProfileItemType) => KasDotsMenuItemProps;
    expandedItems: string[];
    updateExpandedItem: (id: string, value: boolean) => void;
    expandAllItems: () => void;
    collapseAllItems: () => void;
    activeEmployeeProfile: UnderwritingEmployeeProfileItemModel | null;
    setActiveEmployeeProfile: (value: UnderwritingEmployeeProfileItemModel | null) => void;
    getProfileItemUniqId: (id: string) => string;
}

const UnderwritingContext = createContext<UnderwritingContextModel | undefined>(undefined);

interface UnderwritingProviderProps {
    children: React.ReactNode;
    activeProfile: UnderwritingSearchDTO | null;
}

export const UnderwritingProvider = ({children, activeProfile}: UnderwritingProviderProps) => {
    const theme = useTheme();
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const {hashMatch, updateRoute} = useHashHandler();
    const [visibleProfileItems, setVisibleProfileItems] = useState<UnderwritingProfileItemModel[]>([]);
    const [activeEmployeeProfile, setActiveEmployeeProfile] =
        useState<UnderwritingEmployeeProfileItemModel | null>(null);

    const getDefaultItems = () => {
        return !activeProfile?.employee_id && activeProfile?.user_id
            ? USER_PROFILE_ITEMS
            : EMPLOYEE_PROFILE_ITEMS;
    };

    const activeProfileVisibleItems = useMemo(() => {
        if (!activeProfile) return [];
        const defaultItems = getDefaultItems();

        return visibleProfileItems.filter((item) => defaultItems.some(({id}) => item.id === id));
    }, [activeProfile, visibleProfileItems]);

    const activeProfileHiddenItems = useMemo(() => {
        if (!activeProfile) return [];
        const defaultItems = getDefaultItems();
        const hiddenItems = defaultItems.filter(
            (item) => !visibleProfileItems.find(({id}) => id === item.id),
        );

        return hiddenItems.map((data) => ({title: data.title, data}));
    }, [activeProfile, visibleProfileItems]);

    const addVisibleProfileItem = (value: UnderwritingProfileItemModel) => {
        const sections = [...visibleProfileItems, value];

        updateVisibleProfileItems(sections);
    };

    const createDeleteMenuItem = (itemType: UnderwritingProfileItemType): KasDotsMenuItemProps => ({
        ContentComponent: (
            <Typography variant='body1' color={theme.palette.error.main}>
                Delete
            </Typography>
        ),
        onClick: () => {
            const removedItemId = visibleProfileItems.find(({type}) => type === itemType)?.id;
            const newProfileItems = visibleProfileItems.filter((item) => item.type !== itemType);

            updateVisibleProfileItems(newProfileItems);

            if (removedItemId) {
                updateExpandedItem(removedItemId, false);
            }
        },
    });

    const updateVisibleProfileItems = (sections: UnderwritingProfileItemModel[]) => {
        setVisibleProfileItems(sections);
        dispatch(updateUserPreferences({underwriting: {sections}}));
    };

    const setSortedProfileItems = () => {
        const sections = userPreferences.value?.underwriting?.sections;

        if (sections) {
            setVisibleProfileItems(sections);
        } else {
            setVisibleProfileItems(DEFAULT_PROFILE_ITEMS);
        }
    };

    const getProfileItemUniqId = (id: string) => `underwriting-profile-${id}-${activeProfile?.uid}`;

    const handleRouteChange = () => {
        const profileType = hashMatch.hash;
        const profileId = hashMatch.type;

        setActiveEmployeeProfile(null);

        if (profileType && profileId) {
            dispatch(handleSharedProfile(profileType, profileId));
        } else {
            dispatch(setActiveProfile(null));
        }
    };

    const [expandedItems, setExpandedItems] = useState<string[]>(() => {
        const storageValue = localStorage.getItem(PROFILE_ITEMS_CONFIG_KEY);
        return storageValue ? storageValue.split(',') : [];
    });

    const updateExpandedItem = (id: string, value: boolean) => {
        setExpandedItems((prev) => {
            const newExpandedItems = value ? [...prev, id] : prev.filter((item) => item !== id);

            return uniqElements(newExpandedItems, (item) => item);
        });
    };

    const expandAllItems = () => {
        setExpandedItems(visibleProfileItems.map(({id}) => id));
    };

    const collapseAllItems = () => {
        setExpandedItems([]);
    };

    const updateExpandedItemsConfig = () => {
        if (expandedItems.length > 0) {
            localStorage.setItem(PROFILE_ITEMS_CONFIG_KEY, expandedItems.join(','));
        } else {
            localStorage.removeItem(PROFILE_ITEMS_CONFIG_KEY);
        }
    };

    useEffect(updateExpandedItemsConfig, [expandedItems]);

    useEffect(setSortedProfileItems, []);

    useEffect(() => {
        const storageProfiles = JSON.parse(localStorage.getItem(PROFILES_KEY) || '[]') as UnderwritingDTO[];

        storageProfiles.forEach((profile) => dispatch(addProfile(profile)));
    }, [dispatch]);

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    useEffect(() => {
        if (activeProfile) {
            updateRoute({
                hash: underwritingHash(activeProfile),
                type: underwritingId(activeProfile).toString(),
                value: null,
            });
        } else {
            updateRoute();
        }
    }, [activeProfile]);

    const value: UnderwritingContextModel = {
        addVisibleProfileItem,
        activeProfileVisibleItems,
        activeProfileHiddenItems,
        updateVisibleProfileItems,
        createDeleteMenuItem,
        expandedItems,
        updateExpandedItem,
        expandAllItems,
        collapseAllItems,
        activeEmployeeProfile,
        setActiveEmployeeProfile,
        getProfileItemUniqId,
    };

    return <UnderwritingContext.Provider value={value}>{children}</UnderwritingContext.Provider>;
};

export function useUnderwriting() {
    const context = useContext(UnderwritingContext);
    if (!context) {
        throw new Error('useUnderwriting must be used within UnderwritingProvider');
    }
    return context;
}
