import {SelectModel} from '@/interfaces';
import {UnderwritingProfileItemType, UnderwritingProfileItemModel} from '@/models';

export const INDEPENDENT_SEARCH_PARAMS = ['lo', 'us', 'el', 'dd', 'ss', 'ap'];
export const SEARCH_OPTIONS: SelectModel<string>[] = [
    {
        id: 'ad',
        label: 'address:',
        value: 'ad',
    },
    {
        id: 'lo',
        label: 'loan:',
        value: 'lo',
    },
    {
        id: 'el',
        label: 'employee:',
        value: 'el',
    },
    {
        id: 'er',
        label: 'employer:',
        value: 'er',
    },
    {
        id: 'em',
        label: 'email:',
        value: 'em',
    },
    {
        id: 'ph',
        label: 'phone:',
        value: 'ph',
    },
    {
        id: 'us',
        label: 'user:',
        value: 'us',
    },
    {
        id: 'na',
        label: 'name:',
        value: 'na',
    },
    {
        id: 'ei',
        label: 'eid:',
        value: 'ei',
    },
    {
        id: 'dd',
        label: 'direct deposit:',
        value: 'dd',
    },
    {
        id: 'ss',
        label: 'ssn:',
        value: 'ss',
    },
    {
        id: 'ip',
        label: 'ip address:',
        value: 'ip',
    },
    {
        id: 'ap',
        label: 'application:',
        value: 'ap',
    },
];

export const PROFILE_ITEMS_CONFIG_KEY = 'underwriting-profile-items-config';

export const AVAILABLE_PROFILE_ITEMS: UnderwritingProfileItemModel[] = [
    {
        id: '1',
        title: 'Actions',
        type: UnderwritingProfileItemType.Actions,
    },
    {
        id: '3',
        title: 'Employee Profile',
        type: UnderwritingProfileItemType.Employee_Profile,
    },
    {
        id: '4',
        title: 'Employer',
        type: UnderwritingProfileItemType.Employer,
    },
    {
        id: '5',
        title: 'Loans',
        type: UnderwritingProfileItemType.Loans,
    },
    {
        id: '6',
        title: '3rd-Party Reports',
        type: UnderwritingProfileItemType.Reports,
    },
    {
        id: '7',
        title: 'Banks',
        type: UnderwritingProfileItemType.Banks,
    },
    {
        id: '8',
        title: 'Transactions',
        type: UnderwritingProfileItemType.Transactions,
    },
    {
        id: '9',
        title: 'Payroll History',
        type: UnderwritingProfileItemType.Payroll_History,
    },
    {
        id: '10',
        title: 'Applications',
        type: UnderwritingProfileItemType.Applications,
    },
    {
        id: '11',
        title: 'Modification History',
        type: UnderwritingProfileItemType.Modification_History,
    },
    {
        id: '12',
        title: 'Referrals',
        type: UnderwritingProfileItemType.Referrals,
    },
    {
        id: '13',
        title: 'Direct Deposits',
        type: UnderwritingProfileItemType.Direct_Deposits,
    },
    {
        id: '14',
        title: 'IP History',
        type: UnderwritingProfileItemType.IP_History,
    },
];

export const DEFAULT_PROFILE_ITEMS = AVAILABLE_PROFILE_ITEMS.filter(({type}) =>
    [
        UnderwritingProfileItemType.Actions,
        UnderwritingProfileItemType.Employee_Profile,
        UnderwritingProfileItemType.Employer,
        UnderwritingProfileItemType.Loans,
        UnderwritingProfileItemType.Reports,
        UnderwritingProfileItemType.Banks,
        UnderwritingProfileItemType.Transactions,
        UnderwritingProfileItemType.Applications,
        UnderwritingProfileItemType.Modification_History,
        UnderwritingProfileItemType.IP_History,
    ].includes(type),
);

export const EMPLOYEE_PROFILE_ITEMS = AVAILABLE_PROFILE_ITEMS.filter(({type}) =>
    [
        UnderwritingProfileItemType.Actions,
        UnderwritingProfileItemType.Employee_Profile,
        UnderwritingProfileItemType.Employer,
        UnderwritingProfileItemType.Loans,
        UnderwritingProfileItemType.Reports,
        UnderwritingProfileItemType.Banks,
        UnderwritingProfileItemType.Transactions,
        UnderwritingProfileItemType.Payroll_History,
        UnderwritingProfileItemType.Applications,
        UnderwritingProfileItemType.Modification_History,
        UnderwritingProfileItemType.Referrals,
        UnderwritingProfileItemType.Direct_Deposits,
        UnderwritingProfileItemType.IP_History,
    ].includes(type),
);

export const USER_PROFILE_ITEMS = AVAILABLE_PROFILE_ITEMS.filter(({type}) =>
    [
        UnderwritingProfileItemType.Actions,
        UnderwritingProfileItemType.Modification_History,
        UnderwritingProfileItemType.IP_History,
    ].includes(type),
);
