import './styles.scss';

import React, {useEffect, useMemo} from 'react';
import {UnderwritingContent, UnderwritingHead, UnderwritingWelcome} from './components';
import {useAppSelector} from '@/lib/hooks';
import {selectUnderwritingState} from '@/lib/slices/underwritingSlice';
import {UnderwritingProvider} from './useUnderwriting';
import {underwritingFullName, underwritingId} from '@/utils/UnderwritingUtils';

export const UnderwritingScreen = () => {
    const {isWelcomeScreen, searchValues, profiles, activeProfile} = useAppSelector(selectUnderwritingState);

    const showWelcomeView = useMemo(
        () => !searchValues.length && isWelcomeScreen && !profiles.length,
        [searchValues, isWelcomeScreen, profiles],
    );

    useEffect(() => {
        if (activeProfile) {
            document.title = `${underwritingFullName(activeProfile)} [${underwritingId(activeProfile)}]`;
        } else {
            document.title = 'Underwriting';
        }
    }, [activeProfile]);

    return (
        <div className='kas-underwriting-screen'>
            <UnderwritingProvider activeProfile={activeProfile}>
                <UnderwritingHead />
                {showWelcomeView ? <UnderwritingWelcome /> : <UnderwritingContent />}
            </UnderwritingProvider>
        </div>
    );
};
