import './styles.scss'

import React from 'react';
import { useOnboardingEmployer } from '../useOnboardingEmployer';
import {OnboardingEmployerDetailItemModel, OnboardingEmployerDetailItemType, DEFAULT_EMPLOYER_DETAIL_SECTION} from '@/models/OnboardingConfigDTO';
import {SortableList} from '@/views';
import { OnboardingEmployerDetailBasicInfo } from './OnboardingEmployerDetailBasicInfo/OnboardingEmployerDetailBasicInfo';
import { OnboardingEmployerDetailPayroll } from './OnboardingEmployerDetailPayroll/OnboardingEmployerDetailPayroll';
import OnboardingListFooter from './OnboardingListFooter/OnboardingListFooter';
import { OnboardingEmployerDetailContact } from './OnboardingEmployerDetailContact/OnboardingEmployerDetailContact';
import { OnboardingEmployerDetailCensus } from './OnboardingEmployerDetailCensus/OnboardingEmployerDetailCensus';
import { OnboardingEmployerDetailBilling } from './OnboardingEmployerDetailBilling/OnboardingEmployerDetailBilling';
import { OnboardingEmployerDetailReconciliation } from './OnboardingEmployerDetailReconciliation/OnboardingEmployerDetailReconciliation';
import { OnboardingEmployerDetailRepayment } from './OnboardingEmployerDetailRepayment/OnboardingEmployerDetailRepayment';
import { OnboardingEmployerDetailEmailBlackList } from './OnboardingEmployerDetailEmailBlackList/OnboardingEmployerDetailEmailBlackList';

export const OnboardingEmployerDetailList = () => {
    
    const {formik} = useOnboardingEmployer();
    
    
    const renderItems = (item: OnboardingEmployerDetailItemModel) => {

        switch (item.type) {

            case OnboardingEmployerDetailItemType.BasicInfo:
                return <OnboardingEmployerDetailBasicInfo key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Payroll:
                return <OnboardingEmployerDetailPayroll key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Repayment:
                return <OnboardingEmployerDetailRepayment key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Contacts:
                return <OnboardingEmployerDetailContact  key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Census:
                return <OnboardingEmployerDetailCensus key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Reconciliation:
                return <OnboardingEmployerDetailReconciliation key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.Billing:
                return <OnboardingEmployerDetailBilling key={item.type} item={item}/>
            case OnboardingEmployerDetailItemType.EmailBlackList:
                return <OnboardingEmployerDetailEmailBlackList key={item.type} item={item}/>
        }
    }

    return (
        <form style={{display: 'contents'}} onSubmit={formik.handleSubmit}>
            <div className='kas-onboarding-profile-list'>
                <SortableList<OnboardingEmployerDetailItemModel>
                    items={DEFAULT_EMPLOYER_DETAIL_SECTION}
                    updateItems={()=>{}}
                    renderItem={renderItems}
                />
            </div>
            <OnboardingListFooter />
        </form>
    );
};
