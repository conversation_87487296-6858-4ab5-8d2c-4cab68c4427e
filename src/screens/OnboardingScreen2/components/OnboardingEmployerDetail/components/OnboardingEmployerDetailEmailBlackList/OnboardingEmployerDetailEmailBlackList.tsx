
import React from 'react';
import { OnboardingDetailItem } from '../OnboardingDetailItem';
import { OnboardingEmployerDetailEmailBlackListContents } from './OnboardingEmployerDetailEmailBlackListContents';
import { OnboardingEmployerDetailItemModel } from '@/models/OnboardingConfigDTO';


interface OnboardingEmployerDetailEmailBlackListProps  {
    item: OnboardingEmployerDetailItemModel
}
export const OnboardingEmployerDetailEmailBlackList = ({item}:OnboardingEmployerDetailEmailBlackListProps ) => {
    
    return (
        <OnboardingDetailItem
            item={item}
            DetailsComponent={<OnboardingEmployerDetailEmailBlackListContents />}

        />
    );
};
