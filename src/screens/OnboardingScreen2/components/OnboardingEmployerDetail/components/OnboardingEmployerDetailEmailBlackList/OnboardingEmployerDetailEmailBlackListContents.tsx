
import React, {ChangeEvent} from 'react';
import { useOnboardingEmployer } from '../../useOnboardingEmployer';
import {Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField} from '@mui/material';
import { Field, FieldArray } from 'formik';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';



export const OnboardingEmployerDetailEmailBlackListContents = ( ) => {
 
    const {formik, formEnabled} = useOnboardingEmployer();

    return (
        <FieldArray name="email_blacklist" validateOnChange={false}>
            {({ push, remove }) => (
                <div style={{padding: '15px'}}>
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>Email Domain</TableCell>
                                    <TableCell>
                                        <Button
                                            variant="contained"
                                            onClick={() => push("")}
                                            disabled={!formEnabled}
                                        >
                                            <AddIcon fontSize='small' />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {(formik.values.email_blacklist || []).map((_, index) => (
                                    <TableRow key={index}>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`email_blacklist.${index}`}
                                                fullWidth
                                                value={formik.values.email_blacklist[index] || ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`email_blacklist.${index}`, value === "" ? undefined : value);
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Button
                                                variant="outlined"
                                                color="error"
                                                onClick={() => remove(index)}
                                                disabled={!formEnabled}
                                            >
                                                <DeleteIcon fontSize='small'  />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </div>
            )}
        </FieldArray>
    );
};

