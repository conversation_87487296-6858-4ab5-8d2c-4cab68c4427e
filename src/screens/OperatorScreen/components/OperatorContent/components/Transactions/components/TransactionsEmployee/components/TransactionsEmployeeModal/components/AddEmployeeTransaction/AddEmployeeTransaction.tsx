import React, {useEffect, useState} from 'react';
import {Ka<PERSON><PERSON><PERSON><PERSON>, KasNoResult<PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ErrorView} from '@/views';
import {EmployeeTransactionForm} from './../../components';
import {LookupDTO} from '@/models';
import {useTransactionsEmployee} from './../../../../useTransactionsEmployee';

export const AddEmployeeTransaction = () => {
    const {employee} = useTransactionsEmployee();
    const [balanceState, setBalanceState] = useState(getDefaultState<LookupDTO>());

    const loadDetails = async () => {
        const url = `/api/secured/ui/lookup/employee/${employee.value}/balance`;

        setBalanceState(getLoadingState(balanceState));
        const response: Completable<LookupDTO> = await apiRequest(url);
        setBalanceState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={balanceState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!balanceState.error}>
                <ErrorView error={balanceState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!balanceState.data}>
                <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!balanceState.data}>
                {!!balanceState.data && <EmployeeTransactionForm balance={balanceState.data} />}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
