import React, {useState} from 'react';
import {
    KasAutocompleteField,
    KasAutocompleteSelectField,
    KasDatePickerFormField,
    KasIn<PERSON>,
    KasModalFooter,
} from '@/components';
import {TextField, Grid2, InputAdornment} from '@mui/material';
import {useFormik} from 'formik';
import {EmployeeTransactionFormValues, validationSchema} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import dayjs from 'dayjs';
import {OperatorTransactionModel} from '@/interfaces';
import {AMOUNT_TYPE_OPTIONS} from './data';
import {useTransactionsEmployee} from './../../../../useTransactionsEmployee';
import {EmployeeTitle} from '@/screens/OperatorScreen/components/OperatorContent/components/Transactions/components';
import {LookupDTO} from '@/models';
import {BalanceCalculator} from './components';

interface EditEmployeeTransactionFormProps {
    data?: OperatorTransactionModel;
    balance?: LookupDTO;
}

export const EmployeeTransactionForm = ({data, balance}: EditEmployeeTransactionFormProps) => {
    const {showMessage} = useSnackbar();
    const {employee, setOpenModal, loadData, transactionTypesState} = useTransactionsEmployee();
    const [submitting, setSubmitting] = useState(false);
    const allLoans = ['fee', 'returned'].some((type) => data?.transaction_type.includes(type));

    const onSubmit = async (values: EmployeeTransactionFormValues) => {
        const url = `/api/secured/operator/transactions`;
        const payload = {
            amount: Number(values.amount) * Number(values.amountType),
            effective_date: dayjs(values.effectiveDate).format('YYYYMMDD'),
            employee_id: employee.value,
            ...(data && {gid: data.gid}),
            loan_id: values.loan?.id || '',
            transaction_type: values.transactionType,
        };

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: data ? 'put' : 'post',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadData();
        }

        setSubmitting(false);
    };

    const formik = useFormik<EmployeeTransactionFormValues>({
        validateOnMount: true,
        initialValues: {
            amount: data ? Math.abs(Number(data.amount)) : '',
            effectiveDate: data?.effective_date || dayjs().format('YYYYMMDD'),
            transactionType: data?.transaction_type || '',
            amountType: data ? (Number(data.amount) > 0 ? 1 : -1) : '',
            loan: data?.loan_id ? {id: data.loan_id.toString(), text: data.loan_id.toString()} : null,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 container size={12}>
                    <Grid2 size={4}>
                        <KasInfo label='Employee'>
                            <EmployeeTitle data={employee.label} />
                        </KasInfo>
                    </Grid2>
                    {balance && <BalanceCalculator values={formik.values} balance={balance} />}
                </Grid2>
                <Grid2 size={8}>
                    <KasAutocompleteField
                        loading={transactionTypesState.loading}
                        disabled={submitting}
                        name='transactionType'
                        label='Transaction Type'
                        options={transactionTypesState.data || []}
                        formik={formik}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <KasAutocompleteSelectField
                        formik={formik}
                        disabled={submitting}
                        name='loan'
                        label='Loan'
                        url={`/api/secured/ui/lookup/employee/${employee.value}/loan?all=${allLoans}`}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='effectiveDate'
                        label='Effective Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <KasAutocompleteField
                        disabled={submitting}
                        name='amountType'
                        label='Amount Type'
                        options={AMOUNT_TYPE_OPTIONS}
                        formik={formik}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        value={formik.values.amount}
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            input: {
                                startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                            },
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
