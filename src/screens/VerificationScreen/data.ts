import {SelectModel} from '@/interfaces';
import {
    VerificationQueueCountStateType,
    VerificationQueueItemType,
    VerificationQueueOutdatedStateType,
    VerificationQueueParams,
    VerificationQueueStateType,
    VerificationQueueTableStateType,
} from '@/screens/VerificationScreen/interfaces';
import {getDefaultState} from '@/utils/DataStateUtils';
import {VerificationQueueModel} from '@/interfaces';

export const VERIFICATION_LINKED_EMPLOYEE_ID = 'kas-verification-linked-employee';
export const VERIFICATION_DUPLICATE_EMPLOYEE_ID = 'kas-verification-duplicate-employee';

export const VERIFICATION_PERIOD_LIST: SelectModel<string>[] = [
    {
        id: 'all',
        value: 'all',
        label: 'All Queued items',
    },
    {
        id: '30',
        value: '30',
        label: 'Last 30 days',
    },
    {
        id: '45',
        value: '45',
        label: 'Last 45 days',
    },
    {
        id: '90',
        value: '90',
        label: 'Last 90 days',
    },
    {
        id: '180',
        value: '180',
        label: 'Last 180 days',
    },
];

export const VERIFICATION_INITIAL_QUEUE_STATE: VerificationQueueStateType = Object.entries(
    VerificationQueueItemType,
).reduce(
    (acc, [_, value]) => ({
        ...acc,
        [value]: getDefaultState<VerificationQueueModel[]>(),
    }),
    {} as VerificationQueueStateType,
);

export const VERIFICATION_INITIAL_QUEUE_TABLE_STATE: VerificationQueueTableStateType = Object.entries(
    VerificationQueueItemType,
).reduce(
    (acc, [_, value]) => ({
        ...acc,
        [value]: [],
    }),
    {} as VerificationQueueTableStateType,
);

export const VERIFICATION_INITIAL_QUEUE_OUT_OF_DATE_STATE: VerificationQueueOutdatedStateType =
    Object.entries(VerificationQueueItemType).reduce(
        (acc, [_, value]) => ({
            ...acc,
            [value]: [],
        }),
        {} as VerificationQueueOutdatedStateType,
    );

export const VERIFICATION_INITIAL_QUEUE_COUNT_STATE: VerificationQueueCountStateType = Object.entries(
    VerificationQueueItemType,
).reduce(
    (acc, [_, value]) => ({
        ...acc,
        [value]: getDefaultState<VerificationQueueModel[]>(),
    }),
    {} as VerificationQueueCountStateType,
);

export const DEFAULT_VERIFICATION_QUEUE_PARAMS: VerificationQueueParams = Object.entries(
    VerificationQueueItemType,
).reduce((acc, [_, value]) => {
    acc[value] = {
        verified: false,
        denied: false,
        followup: false,
        docs: !(value === VerificationQueueItemType.Challenge || value === VerificationQueueItemType.PEP),
        lookup: VERIFICATION_PERIOD_LIST[1].value,
        ...(value === VerificationQueueItemType.Bank && {strict: false}),
    };
    return acc;
}, {} as VerificationQueueParams);
