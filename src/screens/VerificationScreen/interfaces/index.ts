import {DataStateInterface} from '@/interfaces';
import {VerificationQueueCountModel, VerificationQueueModel} from '@/interfaces';
import {Dayjs} from 'dayjs';
import {ReactNode} from 'react';
import {AlertProps} from '@mui/material';

export enum VerificationQueueItemType {
    Income = 'INCOME',
    Deposit = 'ALLOTMENT',
    Address = 'ADDRESS',
    Identity = 'IDENTITY',
    Challenge = 'CHALLENGE',
    PEP = 'PEP',
    Bankruptcy = 'BANKRUPTCY',
    OFAC = 'OFAC',
    Bank = 'BANK',
    Fraud = 'FRAUD',
    Spouse = 'SPOUSE',
    Retiree_Income = 'RETIREE_INCOME',
    Retiree_Bank = 'RETIREE_BANK',
    Primary_Bank = 'PRIMARY_BANK',
}

export interface VerificationQueueParamsModel {
    verified: boolean;
    denied: boolean;
    followup: boolean;
    docs: boolean;
    lookup: string;
    strict?: boolean;
}

export interface VerificationQueueLookingUserModel {
    id: string;
    userId: number;
    userName: string;
    queue: VerificationQueueItemType;
    profileId: number;
    entryTime: Dayjs;
    updatedTime: Dayjs;
}

export interface VerificationQueueSuccessActionModel {
    id: string;
    userId: number;
    message: ReactNode;
    severity: AlertProps['severity'];
    queue: VerificationQueueItemType;
}

export type VerificationQueueStateType = Record<
    VerificationQueueItemType,
    DataStateInterface<VerificationQueueModel[]>
>;

export type VerificationQueueTableStateType = Record<VerificationQueueItemType, VerificationQueueModel[]>;

export type VerificationQueueOutdatedStateType = Record<VerificationQueueItemType, string[]>;

export type VerificationQueueCountStateType = Record<
    VerificationQueueItemType,
    DataStateInterface<VerificationQueueCountModel>
>;
export type VerificationQueueParams = Record<VerificationQueueItemType, VerificationQueueParamsModel>;
