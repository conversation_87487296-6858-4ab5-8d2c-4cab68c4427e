import './styles.scss';

import React from 'react';
import {
    VerificationProfileProvider,
    VerificationFilter,
    VerificationProfile,
    VerificationQueue,
} from './components';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import Box from '@mui/material/Box';

export const VerificationContent = () => {
    const {selectedProfileId} = useVerification();

    return (
        <div className='kas-verification-content'>
            <div hidden={!!selectedProfileId}>
                <Box mb={2}>
                    <VerificationFilter />
                </Box>
                <VerificationQueue />
            </div>
            {selectedProfileId && (
                <VerificationProfileProvider key={selectedProfileId} profileId={selectedProfileId}>
                    <VerificationProfile />
                </VerificationProfileProvider>
            )}
        </div>
    );
};
