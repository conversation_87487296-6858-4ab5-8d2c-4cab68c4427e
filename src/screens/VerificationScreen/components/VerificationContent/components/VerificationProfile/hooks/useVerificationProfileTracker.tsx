import {useCallback, useEffect, useRef} from 'react';
import {useGoogleAnalytics} from '@/hooks/useGoogleAnalytics';
import {useAppSelector} from '@/lib/hooks';
import {selectUser} from '@/lib/slices/userSlice';

export function useVerificationProfileTracker(activeQueue: string, profileId: number) {
    const {trackEvent} = useGoogleAnalytics();
    const user = useAppSelector(selectUser);
    const totalActiveTime = useRef<number>(0);
    const lastActive = useRef<number>(Date.now());
    const hasUserActed = useRef<boolean>(false);

    const sendTrackingEvent = useCallback(
        (action: string) => {
            const elapsed = totalActiveTime.current + (Date.now() - lastActive.current);

            trackEvent({
                action,
                category: activeQueue,
                user: `${user.value?.first_name} ${user.value?.last_name}`,
                profile_id: `${profileId}`,
                value: Math.round(elapsed),
            });
        },
        [user.value?.gid],
    );

    const trackUserAction = (action: string) => {
        hasUserActed.current = true;
        sendTrackingEvent(action);
    };

    useEffect(() => {
        const onVisibilityChange = () => {
            if (document.visibilityState === 'hidden') {
                totalActiveTime.current += Date.now() - lastActive.current;
            } else {
                lastActive.current = Date.now();
            }
        };

        document.addEventListener('visibilitychange', onVisibilityChange);

        return () => {
            document.removeEventListener('visibilitychange', onVisibilityChange);

            if (!hasUserActed.current) {
                sendTrackingEvent('incomplete-exit');
            }
        };
    }, [profileId]);

    return {trackUserAction};
}
