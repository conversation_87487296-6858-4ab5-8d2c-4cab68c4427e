import React from 'react';
import {Stack} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';
import {KasInfo} from '@/components';
import {VerificationInfo} from './../../../components';

interface AddressCardProps {
    data: VerificationQueueDetailsModel;
    showEmail?: boolean;
}

export const AddressCard = ({data, showEmail = true}: AddressCardProps) => {
    return (
        <VerificationCardWrap title='Address Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='Address'>
                    {data.address_line_1}
                    {data.address_line_2 ? `, ${data.address_line_2}` : ''}
                    <br />
                    {data.address_city}, {data.address_state}{' '}
                    <abbr title={data.address_zip_type}>{data.address_zip}</abbr>
                </KasInfo>
                {showEmail && (
                    <VerificationInfo label='Email' tooltip={`Added: ${data.employee_email_added}`}>
                        {data.employee_email}
                    </VerificationInfo>
                )}
            </Stack>
        </VerificationCardWrap>
    );
};
