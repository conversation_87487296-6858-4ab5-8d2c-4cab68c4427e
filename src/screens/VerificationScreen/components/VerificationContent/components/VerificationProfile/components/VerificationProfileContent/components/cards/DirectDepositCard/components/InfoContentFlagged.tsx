import './styles.scss';

import React from 'react';
import { Box, Tooltip } from '@mui/material';
import { KasFlaggedIcon } from '@/components/KasFlaggedIcon/KasFlaggedIcon';
import { TestableProps } from '@/screens/UnderwritingScreen/interfaces/testable';

interface InfoContentFlaggedProps extends TestableProps {
  children: React.ReactNode;
  flagged?: boolean;
  tooltip?: React.ReactNode;
}

const InfoContentFlagged: React.FC<InfoContentFlaggedProps> = ({ 
  children, 
  flagged, 
  tooltip, 
  testid
}) => {
  return (
    <Box className='info-content-flagged' data-testid={`${testid}-info-flagged`}>
      {children}
      {flagged !== undefined && (
        <Tooltip 
          title={tooltip} 
          arrow
          slotProps={{
            tooltip: {
              className: 'info-content-flagged__tooltip'
            }
          }}
        >
          <KasFlaggedIcon flagged={flagged} />
        </Tooltip>
      )}
    </Box>
  );
};

export default InfoContentFlagged; 