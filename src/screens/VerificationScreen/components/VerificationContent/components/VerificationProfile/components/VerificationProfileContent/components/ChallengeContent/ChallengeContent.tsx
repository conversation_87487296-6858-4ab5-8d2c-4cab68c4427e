import React from 'react';
import {CreditReportCard, DefaultInfoCard, SkiptraceReportCard} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const ChallengeContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Grid2 container spacing={1}>
            <Grid2 size={12}>
                <DefaultInfoCard data={data} />
            </Grid2>
            <Grid2 size={12}>
                <SkiptraceReportCard data={data} />
            </Grid2>
            <Grid2 size={12}>
                <CreditReportCard data={data} />
            </Grid2>
        </Grid2>
    );
};
