import React, {useEffect, useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {SkiptraceReportDetailsTabType, SkiptraceReportModel} from '@/interfaces';
import {PullFreshReportModal, SkiptraceCardItem} from './components';
import {EmptyCard, ErrorCard, LoadingCard} from './../index';
import {SkiptraceReportCardProvider} from '@/screens/VerificationScreen/components/VerificationContent/components/VerificationProfile/components/VerificationProfileContent/components/cards/SkiptraceReportCard/useSkiptraceReportCard';
import Box from '@mui/material/Box';

interface SkiptraceReportCardProps {
    data: VerificationQueueDetailsModel;
    defaultTab?: SkiptraceReportDetailsTabType;
}

const CARD_TITLE = 'Skiptrace Report';

export const SkiptraceReportCard = ({data, defaultTab}: SkiptraceReportCardProps) => {
    const [skiptraceState, setSkiptraceState] = useState(getDefaultState<SkiptraceReportModel[]>());
    const [openPullFreshReportModal, setOpenPullFreshReportModal] = useState(false);

    const loadData = async () => {
        setSkiptraceState(getLoadingState(skiptraceState));
        const response = await apiRequest(`/api/secured/underwriting/reports/${data.employee_id}/skiptrace`);
        setSkiptraceState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={skiptraceState.loading}>
                <LoadingCard title={CARD_TITLE} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!skiptraceState.error}>
                <ErrorCard title={CARD_TITLE} error={skiptraceState.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!skiptraceState.data}>
                {!!skiptraceState.data?.length ? (
                    <SkiptraceReportCardProvider skiptraceData={skiptraceState.data}>
                        <Grid2 container rowGap={2}>
                            {skiptraceState.data.map((skiptrace, index) => (
                                <Grid2 key={skiptrace.gid} size={12}>
                                    <SkiptraceCardItem
                                        skiptrace={skiptrace}
                                        title={CARD_TITLE}
                                        employeeId={data.employee_id}
                                        defaultTab={defaultTab}
                                        expanded={index === 0}
                                        onPullReport={() => setOpenPullFreshReportModal(true)}
                                    />
                                </Grid2>
                            ))}
                        </Grid2>
                    </SkiptraceReportCardProvider>
                ) : (
                    <EmptyCard
                        title={CARD_TITLE}
                        text={`No Skiptrace Reports Found for Employee ID#: ${data.employee_id}`}
                        actions={
                            <Box my={-1}>
                                <Button
                                    onClick={() => setOpenPullFreshReportModal(true)}
                                    variant='outlined'
                                    size='small'>
                                    Pull Report
                                </Button>
                            </Box>
                        }
                    />
                )}
                {openPullFreshReportModal && (
                    <PullFreshReportModal
                        employeeId={data.employee_id}
                        onClose={() => setOpenPullFreshReportModal(false)}
                        onSuccess={loadData}
                    />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
