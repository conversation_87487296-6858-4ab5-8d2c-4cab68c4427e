import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    pepDefinition: Yup.string().required(DEFAULT_VALIDATION_MSG),
    pepStatus: Yup.string().when('pepDefinition', (actionType: string[], schema) => {
        if (actionType.includes('Yes') || actionType.includes('Unsure')) {
            return schema.required(DEFAULT_VALIDATION_MSG);
        }
        return schema;
    }),
    position: Yup.string(),
    approval: Yup.string(),
});

export type PEPFormValues = Yup.Asserts<typeof validationSchema>;
