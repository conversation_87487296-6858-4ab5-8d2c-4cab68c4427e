import React, {useMemo} from 'react';
import {Chip, Stack, Tooltip, Typography} from '@mui/material';
import {diffChars} from 'diff';
import Box from '@mui/material/Box';

interface MatchChipProps {
    data: number;
    expectedValue: string;
    receivedValue: string;
}

export const MatchChip = ({data, expectedValue, receivedValue}: MatchChipProps) => {
    const diff = diffChars(expectedValue, receivedValue, {ignoreCase: true});
    const value = Math.round(data * 100);

    const color = useMemo(() => {
        if (value >= 90) {
            return 'success';
        } else if (value >= 75) {
            return 'warning';
        } else {
            return 'error';
        }
    }, [value]);

    return (
        <Tooltip
            sx={{cursor: 'help'}}
            title={
                <Box sx={{p: 1}}>
                    <Typography variant='body1'>
                        <strong>Expected:</strong> {expectedValue}
                    </Typography>
                    <Typography variant='body1'>
                        <strong>Received:</strong> {receivedValue}
                    </Typography>

                    <Stack spacing={0.5} pt={1}>
                        <Typography variant='body1'>
                            <strong>Comparison:</strong>
                        </Typography>
                        <Typography variant='body1'>
                            {diff.map((part, index) => (
                                <span
                                    key={index}
                                    style={{
                                        color: part.removed
                                            ? '#FF6B6B'
                                            : part.added
                                              ? 'var(--color-warning)'
                                              : 'inherit',
                                    }}>
                                    {part.value}
                                </span>
                            ))}
                        </Typography>
                    </Stack>
                </Box>
            }>
            <Chip label={`${value}% match`} size='small' color={color} variant='outlined' />
        </Tooltip>
    );
};
