import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Grid2, TextField} from '@mui/material';
import {useFormik} from 'formik';
import {CommentDocumentFormValues, validationSchema} from './schema';
import {useVerificationDocuments} from './../../../../index';

export const CommentDocumentForm = ({documentId}: {documentId: number}) => {
    const {addDocumentComment, setActiveModal} = useVerificationDocuments();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: CommentDocumentFormValues) => {
        setSubmitting(true);
        await addDocumentComment(documentId, values.comment);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={1}>
                <Grid2 size={12}>
                    <TextField
                        autoFocus
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setActiveModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
