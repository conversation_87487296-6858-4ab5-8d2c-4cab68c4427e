import React from 'react';
import {Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import {VerificationQueuePriorLoanModel} from '@/interfaces';

interface BorrowerHistoryProps {
    priorLoans: VerificationQueuePriorLoanModel;
    loanAmount?: number;
}

export const BorrowerHistory = ({priorLoans, loanAmount}: BorrowerHistoryProps) => {
    return (
        <Stack flexDirection='row' justifyContent='flex-end' columnGap={2} whiteSpace='nowrap'>
            <KasInfo label='Current loan'>{loanAmount ? toCurrency(loanAmount) : null}</KasInfo>
            <KasInfo label='Prior loans'>{priorLoans.count || null}</KasInfo>
            {!!priorLoans.count && <KasInfo label='Prior loans amount'>{priorLoans.amount}</KasInfo>}
        </Stack>
    );
};
