import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {VerificationDocumentModalType} from './../../interfaces';
import {useVerificationDocuments} from './../../index';
import {CommentDocumentForm, DeleteDocumentForm, PEPForm, UploadDocumentsForm} from './components';
import {DocumentMetadataView} from '@/views';

export const DocumentsModal = () => {
    const {activeModal, setActiveModal} = useVerificationDocuments();

    const title = useMemo(() => {
        switch (activeModal?.type) {
            case VerificationDocumentModalType.Add_Comment:
                return 'Please enter a comment';
            case VerificationDocumentModalType.Delete:
                return 'Delete Document';
            case VerificationDocumentModalType.Document_Metadata:
                return `Document: #${activeModal.props.documentId}`;
            case VerificationDocumentModalType.Upload_Documents:
                return 'Verification File Upload';
            case VerificationDocumentModalType.PEP_Form:
                return 'Kashable PEP Form';
            default:
                return 'Empty Modal';
        }
    }, [activeModal]);

    const renderModalContent = useMemo(() => {
        switch (activeModal?.type) {
            case VerificationDocumentModalType.Add_Comment:
                return <CommentDocumentForm {...activeModal.props} />;
            case VerificationDocumentModalType.Delete:
                return <DeleteDocumentForm {...activeModal.props} />;
            case VerificationDocumentModalType.Document_Metadata:
                return <DocumentMetadataView {...activeModal.props} />;
            case VerificationDocumentModalType.Upload_Documents:
                return <UploadDocumentsForm />;
            case VerificationDocumentModalType.PEP_Form:
                return <PEPForm />;
            default:
                return '';
        }
    }, [activeModal?.type]);

    return (
        <KasModal title={title} open={!!activeModal} onClose={() => setActiveModal(null)}>
            {renderModalContent}
        </KasModal>
    );
};
