import React from 'react';
import {Grid2, Paper} from '@mui/material';
import {
    VerificationAdditionalInfo,
    VerificationFollowUpComments,
    VerificationProfileAlerts,
} from './components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import Box from '@mui/material/Box';

export const VerificationProfileInfo = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Box pt={1}>
            <Paper elevation={0}>
                <Grid2 container py={2} px={3} spacing={1}>
                    <Grid2 size={12}>
                        <VerificationProfileAlerts data={data} />
                    </Grid2>
                    <Grid2 size={12}>
                        <VerificationFollowUpComments data={data} />
                    </Grid2>
                    <VerificationAdditionalInfo data={data} />
                </Grid2>
            </Paper>
        </Box>
    );
};
