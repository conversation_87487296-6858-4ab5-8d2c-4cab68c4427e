import {defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasUnderwritingSharedLink} from '@/components';
import {VerificationQueueDuplicatesModel} from '@/interfaces';

const columnHelper = createColumnHelper<VerificationQueueDuplicatesModel>();

const _defaultInfoColumn = defaultInfoColumn<VerificationQueueDuplicatesModel>;

export const DuplicateEmployeesTableColumns = [
    columnHelper.accessor('loan_id', {
        id: 'expander',
        header: '',
        cell: (props) => {
            return props.getValue() ? expandCell(props) : null;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    }),
    columnHelper.accessor('employee_id', {
        id: 'employee_id',
        header: 'Employee',
        cell: (props) => {
            const {name, employee_id} = props.row.original;

            return (
                <>
                    {name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
    }),
    _defaultInfoColumn('employer_name', 'Employer'),
    _defaultInfoColumn('application_status', 'Status'),
    columnHelper.accessor('reason', {
        id: 'reason',
        header: 'Reason',
        cell: (props) => {
            const {reason, reason_code} = props.row.original;

            return <abbr title={reason}>{reason_code}</abbr>;
        },
    }),
    _defaultInfoColumn('ip_address', 'IP Address'),
];
