import React from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {KasLoading} from '@/components';

export const LoadingCard = ({title}: {title: string}) => {
    return (
        <Paper elevation={0}>
            <Stack flexDirection='row' alignItems='center' px={3} py={2.5} columnGap={2}>
                <Typography variant='subtitle1'>{title}</Typography>
                <KasLoading />
            </Stack>
        </Paper>
    );
};
