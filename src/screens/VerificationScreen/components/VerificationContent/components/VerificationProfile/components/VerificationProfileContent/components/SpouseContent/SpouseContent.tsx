import React from 'react';
import {AddressCard, DefaultInfoCard, DocumentsCard, VerificationDocumentsProvider} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const SpouseContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Grid2 container spacing={1}>
            <Grid2 container size={4} spacing={1}>
                <Grid2 size={12}>
                    <DefaultInfoCard data={data} />
                </Grid2>
                <Grid2 size={12} alignSelf='stretch'>
                    <AddressCard data={data} showEmail={false} />
                </Grid2>
            </Grid2>
            <Grid2 size={8} alignSelf='stretch'>
                <VerificationDocumentsProvider queue={data}>
                    <DocumentsCard />
                </VerificationDocumentsProvider>
            </Grid2>
        </Grid2>
    );
};
