import React from 'react';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {VerificationProfileSectionType} from './../../../../interfaces';
import {
    AddressSection,
    ContactInformationSection,
    DocumentsSection,
    EmploymentSection,
    PlaidSection,
    SSNSection,
} from './components';

export const ProfileVisibleSections = () => {
    const {detailsState, visibleSections} = useVerificationProfile();

    if (!detailsState.data) {
        return null;
    }

    return (
        <>
            {visibleSections.includes(VerificationProfileSectionType.SSN) && (
                <SSNSection data={detailsState.data} />
            )}
            {visibleSections.includes(VerificationProfileSectionType.Employment) && (
                <EmploymentSection data={detailsState.data} />
            )}
            {visibleSections.includes(VerificationProfileSectionType.Phone_Number) && (
                <ContactInformationSection data={detailsState.data} />
            )}
            {visibleSections.includes(VerificationProfileSectionType.Address) && (
                <AddressSection data={detailsState.data} />
            )}
            {visibleSections.includes(VerificationProfileSectionType.Plaid) && (
                <PlaidSection data={detailsState.data} />
            )}
            {visibleSections.includes(VerificationProfileSectionType.Documents) && (
                <DocumentsSection data={detailsState.data} />
            )}
        </>
    );
};
