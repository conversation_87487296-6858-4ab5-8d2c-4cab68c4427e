import './styles.scss';

import React, {useCallback, useState} from 'react';
import {Badge, Stack, useTheme} from '@mui/material';
import {KasInfo, KasLink, KasModal} from '@/components';
import {VERIFICATION_HASH} from '@/constants';
import {VerificationQueueModel} from '@/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {Circle} from '@mui/icons-material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {getLookingProfileUsersDescription} from '@/utils/VerificationUtils';

interface CurrentQueuesProps {
    queues: VerificationQueueModel[];
    previousQueues: VerificationQueueModel[][];
}

export const CurrentQueues = ({queues, previousQueues}: CurrentQueuesProps) => {
    const {palette} = useTheme();
    const {lookingUsers} = useVerification();
    const {profileId} = useVerificationProfile();
    const [openModal, setOpenModal] = useState(false);

    const getLookingUsers = useCallback(
        (value: VerificationQueueModel) => {
            return lookingUsers.filter((item) => value.code === item.queue && item.profileId === value.gid);
        },
        [lookingUsers],
    );

    const renderQueue = useCallback(
        (value: VerificationQueueModel) => {
            const curLookingUsers = getLookingUsers(value);
            const lookingProfileUsersDescription = getLookingProfileUsersDescription(curLookingUsers);
            let color = palette.warning.main;
            let title = `${value.code} Queue QUEUED on ${value.queue_time}`;

            const getCode = (value: string) => {
                const result = value.toLowerCase().replace('_', ' ');

                return result === 'allotment' ? 'deposit' : result;
            };

            if (!!value.verify_time) {
                color = palette.success.main;
                title = `${value.code} Queue APPROVED by ${value.verify_user} on ${value.verify_time}`;
            } else if (!!value.decline_time) {
                color = palette.error.main;
                title = `${value.code} Queue DECLINED by ${value.decline_user} on ${value.decline_time}`;
            } else if (value.archived) {
                color = palette.action.active;
                title = `${value.code} Queue EXPIRED on ${value.create_time}`;
            }

            if (curLookingUsers.length) {
                title = title + `\nLooking Users: ${lookingProfileUsersDescription}`;
            }

            return (
                <Badge
                    key={value.code}
                    badgeContent={value.gid !== profileId ? curLookingUsers.length : 0}
                    color='info'
                    variant='dot'>
                    <abbr title={title} key={value.code} className='kas-current-queue'>
                        <KasLink
                            disabled={value.gid === profileId}
                            href={`/secured/verification#${VERIFICATION_HASH}:${value.code}|${value.gid}`}>
                            <div style={{display: 'flex', alignItems: 'center'}}>
                                <Circle sx={{fontSize: 'var(--small-text-size)', color}} />
                                <span className='kas-current-queue__code'>{getCode(value.code)}</span>
                            </div>
                        </KasLink>
                    </abbr>
                </Badge>
            );
        },
        [queues, lookingUsers],
    );

    return (
        <KasInfo label='Other queues'>
            <Stack flexDirection='row' flexWrap='wrap' useFlexGap columnGap={1.5}>
                {queues.map(renderQueue)}
                {!!previousQueues.length && (
                    <KasLink
                        onClick={() => {
                            setOpenModal(true);
                        }}>
                        Past Queues
                    </KasLink>
                )}
            </Stack>
            <KasModal
                title='Past Queues'
                open={openModal}
                size='small'
                onClose={() => {
                    setOpenModal(false);
                }}>
                {previousQueues.map((item, i) => (
                    <Stack
                        key={`past-queue-${i}`}
                        flexDirection='row'
                        flexWrap='wrap'
                        useFlexGap
                        columnGap={1.5}>
                        {item.map(renderQueue)}
                    </Stack>
                ))}
            </KasModal>
        </KasInfo>
    );
};
