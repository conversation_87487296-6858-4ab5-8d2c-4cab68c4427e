import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography} from '@mui/material';
import {KasExpandIcon} from '@/components';
import {CreditReportView} from '@/views/CreditReportView/CreditReportView';
import {CreditReportModel} from '@/interfaces';

interface CreditCardItemProps {
    credit: CreditReportModel;
    title: string;
    expanded: boolean;
}

export const CreditCardItem = ({credit, title, expanded = false}: CreditCardItemProps) => {
    const [isAccordionOpen, setIsAccordionOpen] = useState(expanded);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                onDoubleClick={handleAccordionChange}>
                <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                    <Typography variant='subtitle1'>{title}</Typography>
                    <Typography variant='body1'>
                        ({credit.reporting_agency} {credit.report_date})
                    </Typography>
                </Stack>
            </AccordionSummary>
            <AccordionDetails>
                <Box px={3} pb={2}>
                    <CreditReportView credit={credit} config={{experian: {shortView: true}}} />
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
