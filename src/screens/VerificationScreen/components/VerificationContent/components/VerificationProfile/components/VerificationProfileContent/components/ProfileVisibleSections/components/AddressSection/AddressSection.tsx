import React from 'react';
import {useVerificationProfile} from '../../../../../../../index';
import {VerificationProfileSectionType} from '../../../../../../interfaces';
import {AddressCard} from './../../../cards';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const AddressSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection} = useVerificationProfile();

    return (
        <div hidden={activeSection !== VerificationProfileSectionType.Address}>
            <AddressCard data={data} />
        </div>
    );
};
