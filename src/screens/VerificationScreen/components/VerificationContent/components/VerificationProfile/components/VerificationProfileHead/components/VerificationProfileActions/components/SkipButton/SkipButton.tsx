import React from 'react';
import {Button} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';

export const SkipButton = () => {
    const {changeSelectedProfileId, nextUser} = useVerification();

    const goToNextUser = () => {
        if (nextUser) {
            changeSelectedProfileId(nextUser.gid);
        }
    };

    return (
        <Button
            variant='outlined'
            size='small'
            title={nextUser?.employee_name || ''}
            disabled={!nextUser}
            onClick={goToNextUser}>
            SKIP
        </Button>
    );
};
