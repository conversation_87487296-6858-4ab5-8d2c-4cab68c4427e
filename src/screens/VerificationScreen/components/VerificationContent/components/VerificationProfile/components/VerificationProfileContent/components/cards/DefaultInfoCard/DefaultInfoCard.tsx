import React from 'react';
import {Stack} from '@mui/material';
import {KasCopyText, KasInfo, KasMaskedSSN, KasUnderwritingSharedLink} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';
import {VerificationInfo} from './../../../components';

export const DefaultInfoCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Employee Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='ID'>
                    {data.employee_id ? <KasUnderwritingSharedLink id={data.employee_id} /> : null}
                </KasInfo>
                <KasInfo label='Name'>
                    <KasCopyText>{data.employee_name}</KasCopyText>
                </KasInfo>
                <VerificationInfo label='Email' tooltip={`Added: ${data.employee_email_added}`}>
                    {data.employee_email}
                </VerificationInfo>
                <VerificationInfo label='Phone' tooltip={`Added: ${data.employee_phone_added}`}>
                    {data.employee_phone}
                </VerificationInfo>
                <KasInfo label='SSN'>{data.employee_ssn && <KasMaskedSSN ssn={data.employee_ssn} />}</KasInfo>
                <KasInfo label='DOB'>{data.employee_dob}</KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
