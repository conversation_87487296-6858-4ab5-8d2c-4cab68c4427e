import React, {useMemo} from 'react';
import {Stack, Box, Typography} from '@mui/material';
import {KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';
import {toCurrency} from '@/utils/FormatUtils';
import InfoContentFlagged from './components/InfoContentFlagged';
import {RefundAmount} from './../components';

export const DirectDepositCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    // Helper function to generate flag info and tooltip
    const getFlagInfoAndTooltip = (documents?: string[] | null): [boolean?, React.ReactNode?] => {
        if (documents == null) {
            return [undefined, undefined];
        }

        const hasDocuments = documents.length > 0;
        const tooltip = hasDocuments ? (
            <Box>
                <Typography>Seen on files:</Typography>
                {documents.map((doc, index) => (
                    <Typography key={index}>{doc}</Typography>
                ))}
            </Box>
        ) : (
            'Not found in files'
        );

        return [hasDocuments, tooltip];
    };

    const [abaFlags, abaTooltip] = useMemo(
        () => getFlagInfoAndTooltip(data.aba_number_documents),
        [data.aba_number_documents],
    );

    const [amountFlags, amountTooltip] = useMemo(
        () => getFlagInfoAndTooltip(data.amount_documents),
        [data.amount_documents],
    );

    const [accountFlags, accountTooltip] = useMemo(
        () => getFlagInfoAndTooltip(data.account_number_documents),
        [data.account_number_documents],
    );

    return (
        <VerificationCardWrap title='Direct Deposit Information'>
            <Stack spacing={2}>
                <KasInfo label='ABA Number'>
                    <InfoContentFlagged
                        flagged={abaFlags}
                        tooltip={abaTooltip}
                        testid='direct-deposit-aba-number'>
                        {data.aba_number}
                    </InfoContentFlagged>
                </KasInfo>
                <KasInfo label='Account Number'>
                    <InfoContentFlagged
                        flagged={accountFlags}
                        tooltip={accountTooltip}
                        testid='direct-deposit-account-number'>
                        {data.acct_number}
                    </InfoContentFlagged>
                </KasInfo>
                <KasInfo label='Installment Amount'>
                    <InfoContentFlagged
                        flagged={amountFlags}
                        tooltip={amountTooltip}
                        testid='direct-deposit-amount'>
                        {data.amount_rounded && data.amount_rounded !== data.amount ? (
                            <>
                                {data.amount_rounded ? toCurrency(data.amount_rounded) : null} (
                                {data.amount ? toCurrency(data.amount) : null})
                            </>
                        ) : (
                            <>{data.amount ? toCurrency(data.amount) : null}</>
                        )}
                    </InfoContentFlagged>
                </KasInfo>
                <KasInfo label='Loan Amount'>{toCurrency(data.loan_amount)}</KasInfo>
                <KasInfo label='Start Date'>{data.installment_date}</KasInfo>
                <KasInfo label='Refund Amount'>
                    {data.refund_amount ? <RefundAmount amount={data.refund_amount} /> : null}
                </KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
