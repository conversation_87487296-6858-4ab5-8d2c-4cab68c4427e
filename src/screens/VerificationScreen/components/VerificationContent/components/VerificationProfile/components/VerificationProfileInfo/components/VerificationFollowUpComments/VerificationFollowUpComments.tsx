import React, {useMemo} from 'react';
import {KasInfo} from '@/components';
import {CommentDTO} from '@/models/restrictDTO';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {Paper, useTheme} from '@mui/material';
import Box from '@mui/material/Box';

enum FollowUpCommentType {
    APP = 'APP',
    QUEUE = 'QUEUE',
}

interface FollowUpCommentModel extends CommentDTO {
    type: FollowUpCommentType;
}

export const VerificationFollowUpComments = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {palette} = useTheme();

    const comments: FollowUpCommentModel[] = useMemo(() => {
        return [
            ...data.comments.map((item) => ({...item, type: FollowUpCommentType.QUEUE})),
            ...data.comments_all.map((item) => ({...item, type: FollowUpCommentType.APP})),
        ];
    }, [data.comments, data.comments_all]);

    if (!data.comments.length && !data.comments_all.length) {
        return null;
    }

    return (
        <Paper elevation={0}>
            <Box py={1} px={2}>
                <KasInfo label='Follow-up Comments'>
                    {comments.map((comment) => (
                        <div
                            key={comment.gid}
                            style={{color: comment.type === 'APP' ? palette.text.disabled : 'inherit'}}>
                            [ {comment.timestamp} ][{' '}
                            <span style={{color: palette.primary.main}}>{comment.user_name}</span> ][{' '}
                            {comment.type} ]: <i>{comment.text}</i>
                        </div>
                    ))}
                </KasInfo>
            </Box>
        </Paper>
    );
};
