import React from 'react';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {EmployerDetailsCard, EVSInformationCard} from './../../../cards';
import {VerificationProfileSectionType} from '../../../../../../interfaces';
import {useVerificationProfile} from '../../../../../../../index';
import {Grid2} from '@mui/material';

export const EmploymentSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection, isEVSReportExist} = useVerificationProfile();

    return (
        <div hidden={activeSection !== VerificationProfileSectionType.Employment}>
            <Grid2 container spacing={1}>
                {!isEVSReportExist && (
                    <Grid2 size={6}>
                        <EmployerDetailsCard data={data} />
                    </Grid2>
                )}
                <Grid2 size={isEVSReportExist ? 12 : 6}>
                    <EVSInformationCard data={data} />
                </Grid2>
            </Grid2>
        </div>
    );
};
