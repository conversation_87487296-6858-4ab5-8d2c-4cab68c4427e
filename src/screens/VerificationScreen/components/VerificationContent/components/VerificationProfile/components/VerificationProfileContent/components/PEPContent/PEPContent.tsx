import React from 'react';
import {DefaultInfoCard, DocumentsCard, VerificationDocumentsProvider} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const PEPContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Grid2 container spacing={1}>
            <Grid2 size={4}>
                <DefaultInfoCard data={data} />
            </Grid2>
            <Grid2 size={8} alignSelf='stretch'>
                <VerificationDocumentsProvider queue={data}>
                    <DocumentsCard showPEP />
                </VerificationDocumentsProvider>
            </Grid2>
        </Grid2>
    );
};
