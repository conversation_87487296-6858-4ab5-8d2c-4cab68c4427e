import React from 'react';
import {Grid2, Paper, Stack} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {BorrowerHistory, CurrentQueues, SystemMessages} from './components';
import {Notifications} from '@mui/icons-material';
import Box from '@mui/material/Box';

export const VerificationAdditionalInfo = ({data}: {data: VerificationQueueDetailsModel}) => {
    const showSystemMessages = data.system_messages.length > 0;
    const gridSize = showSystemMessages ? 6 : 12;

    return (
        <>
            <Grid2 size={gridSize}>
                <Paper elevation={0} style={{minHeight: '100%'}}>
                    <Stack flexDirection='row' justifyContent='space-between' columnGap={2} px={2} py={1}>
                        {!!data.current_queues.length && (
                            <CurrentQueues
                                queues={data.current_queues}
                                previousQueues={data.previous_queues}
                            />
                        )}
                        <BorrowerHistory priorLoans={data.prior_loans} loanAmount={data.loan_amount} />
                    </Stack>
                </Paper>
            </Grid2>
            {showSystemMessages && (
                <Grid2 size={gridSize}>
                    <Paper elevation={0} style={{minHeight: '100%'}}>
                        <Stack flexDirection='row' columnGap={1} pl={1} pr={2} py={1}>
                            <Box pt={1}>
                                <Notifications fontSize='small' color='primary' />
                            </Box>
                            <SystemMessages messages={data.system_messages} />
                        </Stack>
                    </Paper>
                </Grid2>
            )}
        </>
    );
};
