import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography, useTheme} from '@mui/material';
import {VerificationQueueReviewMatchesModel} from '@/interfaces';
import {KasExpandIcon, KasPureTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {LinkedEmployeesTableColumns} from './tables';
import {VERIFICATION_LINKED_EMPLOYEE_ID} from '@/screens/VerificationScreen/data';

export const LinkedEmployeesCard = ({data}: {data: VerificationQueueReviewMatchesModel[]}) => {
    const {palette} = useTheme();
    const [isAccordionOpen, setIsAccordionOpen] = useState(true);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <div id={VERIFICATION_LINKED_EMPLOYEE_ID}>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    sx={{paddingLeft: '8px'}}
                    expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                    onDoubleClick={handleAccordionChange}>
                    <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                        <Typography variant='subtitle1' color={palette.error.main}>
                            Linked Employees
                        </Typography>
                    </Stack>
                </AccordionSummary>
                <AccordionDetails>
                    <Box px={3}>
                        <KasPureTable<VerificationQueueReviewMatchesModel>
                            columns={
                                LinkedEmployeesTableColumns as ColumnDef<
                                    VerificationQueueReviewMatchesModel,
                                    unknown
                                >[]
                            }
                            data={data as VerificationQueueReviewMatchesModel[]}
                        />
                    </Box>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
