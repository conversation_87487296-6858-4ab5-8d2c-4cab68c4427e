import React, {useMemo} from 'react';
import {Grid2, Stack, Typography} from '@mui/material';
import {VerificationQueueApplicationModel} from '@/interfaces';
import {NO_RESULTS_SYMBOL} from '@/constants';
import {Check} from '@mui/icons-material';
import {getQueueTitle} from '@/utils/VerificationUtils';

interface QueueApplicationRowProps {
    item: VerificationQueueApplicationModel;
    isActive: boolean;
}

const QueueApplicationRow = ({item, isActive}: QueueApplicationRowProps) => (
    <>
        <Grid2 size={3}>
            <Stack flexDirection='row' columnGap={0.5}>
                {isActive && <Check color='success' fontSize='small' />}
                <Typography variant='body1'>{item.code}</Typography>
            </Stack>
        </Grid2>
        <Grid2 size={3}>
            <Typography variant='body1'>{item.queue_time || NO_RESULTS_SYMBOL}</Typography>
        </Grid2>
        <Grid2 size={6}>
            <Typography variant='body1'>{item.verify_time || NO_RESULTS_SYMBOL}</Typography>
        </Grid2>
    </>
);

interface QueueApplicationHistoryProps {
    data: VerificationQueueApplicationModel[];
    isActive?: boolean;
}

export const QueueApplicationHistory = ({data, isActive = false}: QueueApplicationHistoryProps) => {
    const filteredData = useMemo(() => {
        return data
            .filter(({queue_time, verify_time}) => queue_time || verify_time)
            .map((item) => ({...item, code: getQueueTitle(item.code)}));
    }, [data]);

    return (
        <Grid2 container p={2} sx={{backgroundColor: 'var(--color-grey)'}} borderRadius={1}>
            <Grid2 size={3} />
            <Grid2 size={3}>
                <Typography variant='subtitle1'>Date Pushed</Typography>
            </Grid2>
            <Grid2 size={6}>
                <Typography variant='subtitle1'>Date Verified</Typography>
            </Grid2>
            {filteredData.map((item, index) => (
                <QueueApplicationRow key={`queue-application-row-${index}`} item={item} isActive={isActive} />
            ))}
        </Grid2>
    );
};
