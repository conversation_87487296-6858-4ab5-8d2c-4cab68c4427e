import React, {createContext, useContext, useEffect, useState} from 'react';
import {SkiptracePreviewReportModel, SkiptraceReportModel, SkiptraceVerifiedItemsModel} from '@/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {VerificationProfileSectionType} from '@/screens/VerificationScreen/components/VerificationContent/components/VerificationProfile/interfaces';

interface SkiptraceReportCardContextModel {
    handleLoadedSkiptraceReport: (value: SkiptracePreviewReportModel) => void;
}

const SkiptraceReportCardContext = createContext<SkiptraceReportCardContextModel | undefined>(undefined);

interface SkiptraceReportCardProviderProps {
    children: React.ReactNode;
    skiptraceData: SkiptraceReportModel[];
}

export const SkiptraceReportCardProvider = ({children, skiptraceData}: SkiptraceReportCardProviderProps) => {
    const {handleAutoVerifiedSection} = useVerificationProfile();
    const [verified, setVerified] = useState<SkiptraceVerifiedItemsModel | null>(null);
    const latestReport = skiptraceData.reduce((latest, current) =>
        new Date(current.report_date) > new Date(latest.report_date) ? current : latest,
    );

    const getVerificationResult = (data: SkiptraceVerifiedItemsModel[]) => {
        return {
            ssn_verified: data.every((record) => record.ssn_verified),
            address_verified: data.every((record) => record.address_verified),
            phone_verified: data.every((record) => record.phone_verified),
        };
    };

    const handleLoadedSkiptraceReport = (value: SkiptracePreviewReportModel) => {
        const isLatest = latestReport.gid === value.gid;

        if (isLatest) {
            const verified = getVerificationResult(value.records);

            setVerified(verified);
        }
    };

    const handleReportsVerification = () => {
        if (verified) {
            const visitedSections: VerificationProfileSectionType[] = [];

            if (verified.address_verified) {
                visitedSections.push(VerificationProfileSectionType.Address);
            }
            if (verified.phone_verified) {
                visitedSections.push(VerificationProfileSectionType.Phone_Number);
            }
            if (verified.ssn_verified) {
                visitedSections.push(VerificationProfileSectionType.SSN);
            }

            handleAutoVerifiedSection(visitedSections);
        }
    };

    useEffect(handleReportsVerification, [verified]);

    const value: SkiptraceReportCardContextModel = {
        handleLoadedSkiptraceReport,
    };

    return (
        <SkiptraceReportCardContext.Provider value={value}>{children}</SkiptraceReportCardContext.Provider>
    );
};

export function useSkiptraceReportCard() {
    const context = useContext(SkiptraceReportCardContext);
    if (!context) {
        throw new Error('useSkiptraceReportCard must be used within SkiptraceReportCardProvider');
    }
    return context;
}
