import React from 'react';
import {Grid2, Skeleton} from '@mui/material';
import Box from '@mui/material/Box';

export const QueueApplicationLoading = () => {
    return (
        <Grid2 container spacing={2}>
            {[1, 2, 3].map((item) => (
                <Grid2 key={item} size={4}>
                    <Skeleton variant='rounded' animation='wave' width={130} height={18} />
                    <Box pt={2} pb={1}>
                        <Skeleton variant='rounded' animation='wave' width={160} height={16} />
                    </Box>
                    <Skeleton variant='rounded' animation='wave' width={160} height={16} />
                </Grid2>
            ))}
        </Grid2>
    );
};
