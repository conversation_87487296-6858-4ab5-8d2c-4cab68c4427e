import './styles.scss';

import React from 'react';
import {Check} from '@mui/icons-material';
import {useVerificationProfile} from './../../../../useVerificationProfile';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {VerificationProfileSectionType} from './../../../../interfaces';

export const SectionCheckMark = ({item}: {item: VerificationProfileSectionType}) => {
    const {activeQueue} = useVerification();
    const {visitedSections, detailsState} = useVerificationProfile();
    const documentCount = detailsState.data?.document_count || 0;

    const isBankWithDocs =
        activeQueue === VerificationQueueItemType.Bank &&
        item === VerificationProfileSectionType.Documents &&
        documentCount > 0;

    if (isBankWithDocs) {
        return <div className='kas-verification-document-checkmark'>{documentCount}</div>;
    }

    return visitedSections.includes(item) ? <Check color='success' /> : null;
};
