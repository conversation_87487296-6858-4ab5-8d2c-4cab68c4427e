import React, {useMemo} from 'react';
import {CreditReportCard, SkiptraceReportCard} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationProfileSectionType} from './../../../../interfaces';
import {useVerificationProfile} from './../../../../useVerificationProfile';
import {SkiptraceReportDetailsTabType} from '@/interfaces';

export const DepositContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection} = useVerificationProfile();
    const skiptraceReportVisibleSections = [
        VerificationProfileSectionType.SSN,
        VerificationProfileSectionType.Phone_Number,
        VerificationProfileSectionType.Address,
    ];
    const creditReportVisibleSections = [
        VerificationProfileSectionType.SSN,
        VerificationProfileSectionType.Address,
    ];
    const skiptraceVisible = !!activeSection && skiptraceReportVisibleSections.includes(activeSection);
    const creditVisible = !!activeSection && creditReportVisibleSections.includes(activeSection);

    const defaultSkiptraceTab = useMemo(() => {
        switch (activeSection) {
            case VerificationProfileSectionType.SSN:
                return SkiptraceReportDetailsTabType.SSNs;
            case VerificationProfileSectionType.Phone_Number:
                return SkiptraceReportDetailsTabType.Phones;
            case VerificationProfileSectionType.Address:
                return SkiptraceReportDetailsTabType.Addresses;
            default:
                return undefined;
        }
    }, [activeSection]);

    if (!skiptraceVisible && !creditVisible) {
        return null;
    }

    return (
        <Grid2 container spacing={1}>
            <Grid2 size={12} hidden={!skiptraceVisible}>
                <SkiptraceReportCard data={data} defaultTab={defaultSkiptraceTab} />
            </Grid2>
            <Grid2 size={12} hidden={!creditVisible}>
                <CreditReportCard data={data} />
            </Grid2>
        </Grid2>
    );
};
