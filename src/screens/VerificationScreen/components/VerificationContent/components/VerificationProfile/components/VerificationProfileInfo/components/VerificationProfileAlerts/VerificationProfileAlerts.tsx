import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Stack, Typography} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import dayjs from 'dayjs';
import {KasLink, KasL<PERSON>ding, KasSwitch, KasSwitchWhen} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import Box from '@mui/material/Box';
import {scrollToBlockById} from '@/utils/ContentUtils';
import {
    VERIFICATION_DUPLICATE_EMPLOYEE_ID,
    VERIFICATION_LINKED_EMPLOYEE_ID,
} from '@/screens/VerificationScreen/data';

const AlertViewDetails = ({onClick}: {onClick: () => void}) => (
    <Typography variant='subtitle1' style={{cursor: 'pointer'}} onClick={onClick}>
        VIEW DETAILS
    </Typography>
);

interface VerificationProfileAlertsProps {
    data: VerificationQueueDetailsModel;
}

export const VerificationProfileAlerts = ({data}: VerificationProfileAlertsProps) => {
    const {loadEmployeeDuplicates, employeeDuplicateState, profileHeaderHeight} = useVerificationProfile();
    const [loadedDuplicates, setLoadedDuplicates] = useState(false);
    const manReviewDate = data.review_date ? dayjs(data.review_date) : null;
    const hasReviewIpMatch = !!(data.review_matches_by_ip && data.review_matches_by_ip.length);
    const isErrorLoadingDuplicates =
        (!!employeeDuplicateState.error || loadedDuplicates) && !employeeDuplicateState.data;

    const scrollToContainer = (id: string) => {
        scrollToBlockById(id, profileHeaderHeight);
    };

    const loadDuplicates = () => {
        (async () => {
            await loadEmployeeDuplicates(data.queue_id);
            setLoadedDuplicates(true);
        })();
    };

    useEffect(loadDuplicates, []);

    return (
        <Stack spacing={1}>
            <KasSwitch>
                <KasSwitchWhen condition={employeeDuplicateState.loading}>
                    <Alert severity='info'>
                        <Stack direction='row' alignItems='center' columnGap={1}>
                            Loading matches for other employees...
                            <KasLoading />
                        </Stack>
                    </Alert>
                </KasSwitchWhen>
                <KasSwitchWhen condition={isErrorLoadingDuplicates}>
                    <Alert severity='error'>
                        <Box display='flex' alignItems='center' flexWrap='wrap'>
                            Something went wrong with loading Duplicate Employees!
                            <Box pl={1}>
                                <KasLink onClick={loadDuplicates}>Try Again</KasLink>
                            </Box>
                        </Box>
                    </Alert>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!employeeDuplicateState.data}>
                    {!!employeeDuplicateState.data?.length && (
                        <Alert
                            severity='warning'
                            action={
                                <AlertViewDetails
                                    onClick={() => scrollToContainer(VERIFICATION_DUPLICATE_EMPLOYEE_ID)}
                                />
                            }>
                            Matches for other employee&apos;s were found under Review.
                        </Alert>
                    )}
                </KasSwitchWhen>
            </KasSwitch>
            {data.pre_qualify && <Alert severity='warning'>Pre-qualify Application</Alert>}
            {hasReviewIpMatch && (
                <Alert
                    severity='warning'
                    action={
                        <AlertViewDetails
                            onClick={() => scrollToContainer(VERIFICATION_LINKED_EMPLOYEE_ID)}
                        />
                    }>
                    IP address matches another employee&apos;s under Review.
                </Alert>
            )}
            {(data.bad_actor || data.account_recent_change || dayjs().diff(manReviewDate) < 0) && (
                <Alert severity='warning'>
                    {data.bad_actor && <p>This Employee is a high fraud risk</p>}
                    {dayjs().diff(manReviewDate) < 0 && (
                        <p>
                            User is flagged for <b>MANUAL REVIEW</b> until {data.review_date}
                        </p>
                    )}
                    {data.account_recent_change && <p>Recent email/phone change</p>}
                </Alert>
            )}
            {dayjs().diff(manReviewDate) > 0 && (
                <Alert severity='warning'>
                    <b>MANUAL REVIEW</b> expired {data.review_date}
                </Alert>
            )}
            {data.verify_date && data.verify_user && (
                <Alert severity='success'>
                    This item was APPROVED by <b>{data.verify_user}</b> on <b>{data.verify_date}</b>
                </Alert>
            )}
            {!(data.verify_date && data.verify_user) && data.decline_date && (
                <Alert severity='warning'>
                    This item was DECLINED on <b>{data.decline_date}</b>
                </Alert>
            )}
        </Stack>
    );
};
