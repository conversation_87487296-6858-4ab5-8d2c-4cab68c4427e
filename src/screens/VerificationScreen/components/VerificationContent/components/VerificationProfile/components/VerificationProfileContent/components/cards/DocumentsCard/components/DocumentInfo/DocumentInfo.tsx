import React from 'react';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {Typography} from '@mui/material';

interface DocumentInfoProps {
    document: DocumentQueueModel;
}

export const DocumentInfo = ({document}: DocumentInfoProps) => {
    return (
        <>
            <Typography variant='body1'>Screenshot from {document.date}</Typography>
            <Typography variant='body1'>{document.key}</Typography>
        </>
    );
};
