import React from 'react';
import {useVerificationProfile} from './../../../../useVerificationProfile';
import {Button, Stack} from '@mui/material';
import {VerificationProfileModalType} from './../../../../interfaces';
import {ApproveButton, SkipButton} from './components';

export const VerificationProfileActions = () => {
    const {profileId, detailsState, setOpenVerificationProfileModal} = useVerificationProfile();

    return (
        <Stack flexDirection='row' alignItems='flex-start' columnGap={1}>
            <SkipButton />
            <ApproveButton />
            <Button
                variant='contained'
                size='small'
                disabled={!detailsState.data}
                onClick={() => {
                    if (!!detailsState.data) {
                        setOpenVerificationProfileModal({
                            type: VerificationProfileModalType.PUSH_TO_QUEUE,
                            props: {
                                applicationId: detailsState.data.application_id,
                                source: profileId,
                            },
                        });
                    }
                }}>
                PUSH TO QUEUE
            </Button>
            <Button
                variant='outlined'
                size='small'
                onClick={() =>
                    setOpenVerificationProfileModal({
                        type: VerificationProfileModalType.SEND_EMAIL,
                        props: {
                            id: profileId,
                        },
                    })
                }>
                SEND EMAIL
            </Button>
            <Button
                variant='outlined'
                size='small'
                onClick={() =>
                    setOpenVerificationProfileModal({
                        type: VerificationProfileModalType.ADD_COMMENT,
                    })
                }>
                ADD COMMENT
            </Button>
            <Button
                variant='contained'
                color='error'
                size='small'
                onClick={() =>
                    setOpenVerificationProfileModal({
                        type: VerificationProfileModalType.DECLINE,
                    })
                }>
                DECLINE
            </Button>
        </Stack>
    );
};
