import React from 'react';
import {Chip, Stack} from '@mui/material';
import {toCurrency} from '@/utils/FormatUtils';

export const RefundAmount = ({amount}: {amount: number}) => {
    return (
        <Stack direction='row' alignItems='center' flexWrap='wrap' useFlexGap spacing={1}>
            <span>{toCurrency(amount)}</span>
            <Chip label='Refund available' size='small' color='success' variant='outlined' />
        </Stack>
    );
};
