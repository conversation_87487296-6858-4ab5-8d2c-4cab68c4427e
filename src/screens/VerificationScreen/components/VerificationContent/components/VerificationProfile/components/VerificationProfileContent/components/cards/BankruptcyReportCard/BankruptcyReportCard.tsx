import React, {useEffect, useMemo, useState} from 'react';
import {Grid2} from '@mui/material';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {BankruptcyCardItem} from './components';
import {EmptyCard, ErrorCard, LoadingCard} from './../index';
import {BankruptcyReportModel} from '@/interfaces';

interface BankruptcyReportCardProps {
    employeeId: number;
}

const CARD_TITLE = 'Bankruptcy Report';

export const BankruptcyReportCard = ({employeeId}: BankruptcyReportCardProps) => {
    const [bankruptcyReportState, setBankruptcyReportState] =
        useState(getDefaultState<BankruptcyReportModel[]>());

    const reportsWithRecords = useMemo(() => {
        if (bankruptcyReportState.data?.length) {
            return bankruptcyReportState.data.filter((item) => !!item.report?.records?.length);
        }

        return [];
    }, [bankruptcyReportState.data]);

    const loadData = async () => {
        setBankruptcyReportState(getLoadingState(bankruptcyReportState));
        const response = await apiRequest(`/api/secured/underwriting/reports/${employeeId}/bankruptcy`);
        setBankruptcyReportState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={bankruptcyReportState.loading}>
                <LoadingCard title={CARD_TITLE} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!bankruptcyReportState.error}>
                <ErrorCard title={CARD_TITLE} error={bankruptcyReportState.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!bankruptcyReportState.data}>
                {!!reportsWithRecords.length ? (
                    <Grid2 container rowGap={2}>
                        {reportsWithRecords.map((item, index) => (
                            <Grid2 key={item.gid} size={12}>
                                <BankruptcyCardItem bankruptcy={item} expanded={index === 0} />
                            </Grid2>
                        ))}
                    </Grid2>
                ) : (
                    <EmptyCard
                        title={CARD_TITLE}
                        text={`No Bankruptcy Reports Found for Employee ID#: ${employeeId}`}
                    />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
