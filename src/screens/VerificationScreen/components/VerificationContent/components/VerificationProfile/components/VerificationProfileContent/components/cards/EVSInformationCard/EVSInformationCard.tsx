import React from 'react';
import {Stack, useTheme} from '@mui/material';
import {KasFlaggedIcon, KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {toCurrency} from '@/utils/FormatUtils';
import {VerificationCardWrap} from './../index';
import {MatchChip} from './components';
import {HiredDateInfo, VerificationInfo} from './../../../components';
import {useVerificationProfile} from './../../../../../useVerificationProfile';

interface EVSInformationCardProps {
    data: VerificationQueueDetailsModel;
    showHiredDate?: boolean;
}

export const EVSInformationCard = ({data, showHiredDate = false}: EVSInformationCardProps) => {
    const {isEVSReportExist} = useVerificationProfile();
    const {palette} = useTheme();

    return (
        <VerificationCardWrap title='EVS Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap columnGap={2} rowGap={1}>
                <Stack rowGap={1}>
                    <KasInfo label='EVS Employer'>
                        {data.evs_employer ? (
                            <>
                                {data.evs_employer}{' '}
                                {data.evs_employer_match && (
                                    <MatchChip
                                        data={data.evs_employer_match}
                                        expectedValue={data.employer_name}
                                        receivedValue={data.evs_employer}
                                    />
                                )}
                            </>
                        ) : null}
                    </KasInfo>
                    <KasInfo label='User-Provided Employer'>{data.employer_name}</KasInfo>
                </Stack>
                <Stack rowGap={1}>
                    <KasInfo label='EVS Name'>
                        {data.evs_name ? (
                            <>
                                {data.evs_name}{' '}
                                {data.evs_name_match && (
                                    <MatchChip
                                        data={data.evs_name_match}
                                        expectedValue={data.employee_name}
                                        receivedValue={data.evs_name}
                                    />
                                )}
                            </>
                        ) : null}
                    </KasInfo>
                    <KasInfo label='User-Provided Name'>{data.employee_name}</KasInfo>
                </Stack>
                <Stack direction='row' flexWrap='wrap' useFlexGap columnGap={2} rowGap={1}>
                    <KasInfo label='Status'>{data.evs_status}</KasInfo>
                    <KasInfo label='Hire Date'>
                        <span
                            style={{color: !!data.evs_hire_date ? palette.success.main : palette.error.main}}>
                            {data.evs_hire_date || 'N/A'}
                        </span>
                    </KasInfo>
                    <KasInfo label='Income'>
                        {typeof data.evs_income === 'number' ? toCurrency(data.evs_income) : 'N/A'}
                    </KasInfo>
                    {showHiredDate && <HiredDateInfo hiredDate={data.hired_date} gid={data.employee_id} />}
                </Stack>
                {isEVSReportExist && (
                    <Stack direction='row' flexWrap='wrap' useFlexGap columnGap={2} rowGap={1}>
                        {data.primary_employer_id !== data.employer_id && (
                            <KasInfo label='Primary Employer'>{data.primary_employer_name}</KasInfo>
                        )}
                        <KasInfo label='Loan Guarantee'>
                            <KasFlaggedIcon flagged={data.loan_guarantee} />
                        </KasInfo>
                        <VerificationInfo label='Email' tooltip={`Added: ${data.employee_email_added}`}>
                            {data.employee_email}
                        </VerificationInfo>
                    </Stack>
                )}
            </Stack>
        </VerificationCardWrap>
    );
};
