import React from 'react';
import {
    Address<PERSON>ard,
    CreditReportCard,
    DefaultInfoCard,
    DocumentsCard,
    DuplicateEmployees<PERSON>ard,
    MapCard,
    SkiptraceReportCard,
    VerificationDocumentsProvider,
} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {SkiptraceReportDetailsTabType} from '@/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';

export const AddressContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {employeeDuplicateState} = useVerificationProfile();
    const hasDuplicates = !!employeeDuplicateState.data?.length;

    return (
        <Grid2 container spacing={1}>
            <Grid2 container size={5} spacing={1}>
                <Grid2 size={12}>
                    <DefaultInfoCard data={data} />
                </Grid2>
                <Grid2 size={12}>
                    <AddressCard data={data} />
                </Grid2>
                <Grid2 size={12}>
                    <MapCard data={data} />
                </Grid2>
            </Grid2>
            <Grid2 size={7}>
                <VerificationDocumentsProvider queue={data}>
                    <DocumentsCard />
                </VerificationDocumentsProvider>
            </Grid2>
            {hasDuplicates && (
                <Grid2 size={12}>
                    <DuplicateEmployeesCard />
                </Grid2>
            )}
            <Grid2 size={12}>
                <SkiptraceReportCard data={data} defaultTab={SkiptraceReportDetailsTabType.Addresses} />
            </Grid2>
            <Grid2 size={12}>
                <CreditReportCard data={data} />
            </Grid2>
        </Grid2>
    );
};
