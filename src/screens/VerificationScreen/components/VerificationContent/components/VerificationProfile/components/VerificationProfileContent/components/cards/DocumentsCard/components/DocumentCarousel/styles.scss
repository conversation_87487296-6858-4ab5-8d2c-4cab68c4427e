.kas-document-carousel {
  position: relative;

  &__info {
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
  }

  .slick-slide {
    height: 100%;
  }

  .slick-list {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .slick-track {
    display: flex;
    align-items: center;
  }

  .slick-arrow {
    width: auto;
    height: auto;
    color: var(--color-disabled);

    &:before {
      display: none;
    }

    &.slick-next {
      right: auto;
      left: 100%;
    }

    &.slick-prev {
      right: 100%;
      left: auto;
    }

    &.slick-disabled {
      opacity: .5;
      pointer-events: none;
    }
  }

  .slick-dots {
    bottom: -20px;

    li button:before {
      color: var(--color-disabled);
      opacity: 1;
      font-size: 10px;
    }

    li.slick-active button:before {
      color: var(--color-text);
      opacity: 1;
    }
  }
}
