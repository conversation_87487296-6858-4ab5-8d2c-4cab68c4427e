import React from 'react';
import {
    DocumentsCard,
    EVSInformationCard,
    IncomeInformationCard,
    VerificationDocumentsProvider,
} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const IncomeContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Grid2 container spacing={1}>
            <Grid2 container size={4} spacing={1}>
                <Grid2 size={12}>
                    <IncomeInformationCard data={data} />
                </Grid2>
                <Grid2 size={12} alignSelf='stretch'>
                    <EVSInformationCard data={data} showHiredDate={true} />
                </Grid2>
            </Grid2>
            <Grid2 size={8} alignSelf='stretch'>
                <VerificationDocumentsProvider queue={data}>
                    <DocumentsCard />
                </VerificationDocumentsProvider>
            </Grid2>
        </Grid2>
    );
};
