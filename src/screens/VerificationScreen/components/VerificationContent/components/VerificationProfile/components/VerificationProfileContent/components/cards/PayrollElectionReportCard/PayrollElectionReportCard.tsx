import React, {useState} from 'react';
import {KasExpandIcon} from '@/components';
import {PayrollReportView} from '@/views';
import {Accordion, AccordionDetails, AccordionSummary, Box, Typography} from '@mui/material';
import {PayrollElectionReportDTO} from '@/models';

export const PayrollElectionReportCard = ({report}: {report: PayrollElectionReportDTO}) => {
    const [isAccordionOpen, setIsAccordionOpen] = useState(true);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                onDoubleClick={handleAccordionChange}>
                <Typography variant='subtitle1' p={1} pl={2}>
                    Payroll Election Report
                </Typography>
            </AccordionSummary>
            <AccordionDetails>
                <Box px={3} pb={2}>
                    <PayrollReportView report={report} />
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
