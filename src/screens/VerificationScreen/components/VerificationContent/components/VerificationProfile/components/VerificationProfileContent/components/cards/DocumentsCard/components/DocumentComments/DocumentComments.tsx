import React from 'react';
import {Stack, Typography, useTheme} from '@mui/material';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import Box from '@mui/material/Box';
import {KasNoResults} from '@/components';

export const DocumentComments = ({data}: {data: DocumentQueueModel}) => {
    const {palette} = useTheme();

    return (
        <Box p={1} pl={5.5}>
            {!!data.comments?.length ? (
                <>
                    <Typography variant='body1' color={palette.text.disabled} mb={1}>
                        Comments:
                    </Typography>
                    <Stack spacing={1}>
                        {data.comments.map((comment) => (
                            <div key={comment.gid}>
                                [{comment.timestamp}][
                                <span style={{color: palette.primary.main}}>{comment.user_name}</span>] -{' '}
                                {comment.text}
                            </div>
                        ))}
                    </Stack>
                </>
            ) : (
                <KasNoResults text='No comments found for this document.' />
            )}
        </Box>
    );
};
