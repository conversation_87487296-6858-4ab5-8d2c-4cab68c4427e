import React from 'react';
import {KasLink} from '@/components';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {useVerificationDocuments} from './../../useVerificationDocuments';

export const DocumentOpenLink = ({document}: {document: DocumentQueueModel}) => {
    const {documentsState, setActiveDocumentIndex} = useVerificationDocuments();

    const showDocument = () => {
        const index = documentsState.data?.findIndex(({gid}) => gid === document.gid);

        if (typeof index === 'number') {
            setActiveDocumentIndex(index);
        }
    };

    return <KasLink onClick={showDocument}>{document.key}</KasLink>;
};
