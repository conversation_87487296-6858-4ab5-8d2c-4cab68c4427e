import './styles.scss';

import React, {CSSProperties, useMemo} from 'react';
import {Typography} from '@mui/material';
import {KasLink} from '@/components';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {getQueueTitle} from '@/utils/VerificationUtils';

export const VerificationActiveQueue = () => {
    const {rawActiveQueue, activeQueue, changeSelectedProfileId} = useVerification();

    const headerStyles: CSSProperties = useMemo(() => {
        if (activeQueue !== rawActiveQueue) {
            return {backgroundColor: '#D1FF05'};
        }
        return {};
    }, [activeQueue, rawActiveQueue]);

    return (
        <div className='kas-verification-active-queue'>
            <KasLink onClick={() => changeSelectedProfileId(null)}>
                <Typography style={headerStyles} variant='h6'>
                    {getQueueTitle(rawActiveQueue).toUpperCase()}
                </Typography>
            </KasLink>
        </div>
    );
};
