import React from 'react';
import {
    AddressCard,
    BankruptcyReportCard,
    DocumentsCard,
    EmployeeDetailsCard,
    VerificationDocumentsProvider,
} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';

export const BankruptcyContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <Grid2 container spacing={1}>
            <Grid2 container size={4} spacing={1}>
                <Grid2 size={12}>
                    <EmployeeDetailsCard data={data} />
                </Grid2>
                <Grid2 size={12} alignSelf='stretch'>
                    <AddressCard data={data} />
                </Grid2>
            </Grid2>
            <Grid2 size={8} alignSelf='stretch'>
                <VerificationDocumentsProvider queue={data}>
                    <DocumentsCard />
                </VerificationDocumentsProvider>
            </Grid2>
            <Grid2 size={12}>
                <BankruptcyReportCard employeeId={data.employee_id} />
            </Grid2>
        </Grid2>
    );
};
