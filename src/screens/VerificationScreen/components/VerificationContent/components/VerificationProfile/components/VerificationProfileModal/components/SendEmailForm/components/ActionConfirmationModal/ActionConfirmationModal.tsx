import React from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModal, KasModalFooter} from '@/components';

interface DeclineConfirmationModalProps {
    text: string;
    onConfirm: () => void;
    onClose: () => void;
}

export const ActionConfirmationModal = ({text, onConfirm, onClose}: DeclineConfirmationModalProps) => {
    const formik = useFormik({
        initialValues: {},
        onSubmit: onConfirm,
    });

    return (
        <KasModal title='Decline Application' open={true} size='small' onClose={onClose}>
            <form onSubmit={formik.handleSubmit}>
                <Grid2 container spacing={2}>
                    <Grid2 size={12}>
                        <Alert severity='warning'>{text}</Alert>
                    </Grid2>
                    <Grid2 size={12}>
                        <KasModalFooter submitText='OK' onCancel={onClose} />
                    </Grid2>
                </Grid2>
            </form>
        </KasModal>
    );
};
