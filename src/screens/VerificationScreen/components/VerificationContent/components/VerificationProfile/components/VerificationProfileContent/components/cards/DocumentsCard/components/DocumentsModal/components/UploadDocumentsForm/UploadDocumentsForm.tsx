import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {useVerificationDocuments} from './../../../../index';
import {UploadFilesView} from '@/views';

export const UploadDocumentsForm = () => {
    const [files, setFiles] = useState<File[]>([]);
    const {queue, uploadDocument, setActiveModal} = useVerificationDocuments();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const formData = new FormData();

        if (files) {
            files.forEach((file) => {
                formData.append('files', file as Blob);
            });
        }

        formData.append('verification_id', queue.queue_id.toString());

        setSubmitting(true);
        await uploadDocument(formData);
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <UploadFilesView loading={submitting} onChange={setFiles} />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={submitting || !files.length}
                        submitText='Upload Documents'
                        onCancel={() => setActiveModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
