import React from 'react';
import {Grid2} from '@mui/material';
import {VerificationProfileSectionType} from '../../../../../../interfaces';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {useVerificationProfile} from '../../../../../../../index';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {
    BankInformationCard,
    DefaultInfoCard,
    DirectDepositCard,
    DocumentsCard,
    EVSInformationCard,
    IncomeInformationCard,
    PayrollElectionReportCard,
    VerificationDocumentsProvider,
} from './../../../cards';

export const DocumentsSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeQueue} = useVerification();
    const {activeSection} = useVerificationProfile();

    return (
        <div hidden={activeSection !== VerificationProfileSectionType.Documents}>
            <Grid2 container spacing={1} alignItems='stretch'>
                <Grid2 container size={4} spacing={1}>
                    <KasSwitch>
                        <KasSwitchWhen condition={activeQueue === VerificationQueueItemType.Deposit}>
                            <Grid2 size={12}>
                                <DirectDepositCard data={data} />
                            </Grid2>
                            <Grid2 size={12}>
                                <EVSInformationCard data={data} showHiredDate={true} />
                            </Grid2>
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={activeQueue === VerificationQueueItemType.Bank}>
                            <Grid2 size={12}>
                                <DefaultInfoCard data={data} />
                            </Grid2>
                            <Grid2 size={12}>
                                <BankInformationCard data={data} />
                            </Grid2>
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={activeQueue === VerificationQueueItemType.Retiree_Bank}>
                            <Grid2 size={12}>
                                <DefaultInfoCard data={data} />
                            </Grid2>
                            <Grid2 size={12}>
                                <IncomeInformationCard data={data} />
                            </Grid2>
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={true}>
                            <Grid2 size={12}>
                                <DefaultInfoCard data={data} />
                            </Grid2>
                        </KasSwitchWhen>
                    </KasSwitch>
                </Grid2>
                <Grid2 size={8}>
                    <VerificationDocumentsProvider queue={data}>
                        <DocumentsCard />
                    </VerificationDocumentsProvider>
                </Grid2>
                {data.payroll_election_report && (
                    <Grid2 size={12}>
                        <PayrollElectionReportCard report={data.payroll_election_report} />
                    </Grid2>
                )}
            </Grid2>
        </div>
    );
};
