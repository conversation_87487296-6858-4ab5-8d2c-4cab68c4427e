import './styles.scss';

import React, {useState} from 'react';
import {useFormik} from 'formik';
import {validationSchema, DeclineFormValues} from './schema';
import {Grid2, TextField} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';

export const DeclineForm = () => {
    const [submitting, setSubmitting] = useState(false);
    const {declineQueue, setOpenVerificationProfileModal} = useVerificationProfile();

    const onSubmit = async (values: DeclineFormValues) => {
        setSubmitting(true);
        await declineQueue(values.comment, true);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-verification-decline-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        type='cancellable'
                        delayText='Saving'
                        onSubmit={formik.handleSubmit}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenVerificationProfileModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
