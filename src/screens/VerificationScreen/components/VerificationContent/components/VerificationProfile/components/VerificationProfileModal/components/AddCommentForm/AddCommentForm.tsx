import './styles.scss';

import React, {useState} from 'react';
import {useFormik} from 'formik';
import {AddCommentFormValues, validationSchema} from './schema';
import {Checkbox, FormControlLabel, Grid2, TextField} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {useVerification} from '@/screens/VerificationScreen/useVerification';

export const AddCommentForm = () => {
    const [submitting, setSubmitting] = useState(false);
    const {addCommentQueue, setOpenVerificationProfileModal, loadDetails} = useVerificationProfile();
    const {changeSelectedProfileId, loadQueue} = useVerification();

    const onSubmit = async (values: AddCommentFormValues) => {
        const callbacks: Array<() => Promise<void>> = [];

        setSubmitting(true);

        if (values.snooze) {
            callbacks.push(async () => {
                changeSelectedProfileId(null);
                await loadQueue();
            });
        } else {
            callbacks.push(loadDetails);
        }

        await addCommentQueue(values.comment, values.snooze, callbacks);
        setOpenVerificationProfileModal(null);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
            snooze: true,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-verification-add-comment-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <FormControlLabel
                        label='Snooze'
                        control={
                            <Checkbox
                                size='small'
                                name='snooze'
                                checked={formik.values.snooze}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenVerificationProfileModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
