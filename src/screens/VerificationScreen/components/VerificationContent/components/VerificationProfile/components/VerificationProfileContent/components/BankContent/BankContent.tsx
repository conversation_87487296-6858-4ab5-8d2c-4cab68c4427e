import React from 'react';
import {BankReportCard, EmployerDetailsCard, EVSInformationCard} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {useVerificationProfile} from './../../../../useVerificationProfile';

export const BankContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {isEVSReportExist} = useVerificationProfile();

    return (
        <Grid2 container spacing={1}>
            <Grid2 size={isEVSReportExist ? 12 : 6}>
                <EVSInformationCard data={data} />
            </Grid2>
            {!isEVSReportExist && (
                <Grid2 size={6}>
                    <EmployerDetailsCard data={data} />
                </Grid2>
            )}
            <Grid2 size={12}>
                <BankReportCard data={data} />
            </Grid2>
        </Grid2>
    );
};
