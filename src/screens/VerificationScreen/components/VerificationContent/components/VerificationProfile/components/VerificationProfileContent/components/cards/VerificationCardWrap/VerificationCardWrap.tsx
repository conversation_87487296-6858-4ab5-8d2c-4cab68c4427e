import React, {ReactNode} from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {PaperOwnProps} from '@mui/material/Paper/Paper';

interface VerificationCardWrapProps extends PaperOwnProps {
    title: string;
    Actions?: ReactNode;
}

export const VerificationCardWrap = ({
    title,
    children,
    Actions = undefined,
    ...rest
}: VerificationCardWrapProps) => {
    return (
        <Paper elevation={0} {...rest} sx={{height: '100%', position: 'relative'}}>
            <Stack p={3} sx={{height: '100%'}}>
                <Stack direction='row' spacing={2} pb={2} justifyContent='space-between' alignItems='center'>
                    <Typography variant='subtitle1'>{title}</Typography>
                    {Actions}
                </Stack>
                {children}
            </Stack>
        </Paper>
    );
};
