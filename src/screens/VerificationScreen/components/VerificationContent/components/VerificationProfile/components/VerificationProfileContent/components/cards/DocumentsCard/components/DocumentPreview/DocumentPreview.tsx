import './styles.scss';

import React, {useEffect, useState} from 'react';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {getDefaultState, getLoadingState} from '@/utils/DataStateUtils';
import {Ka<PERSON><PERSON><PERSON><PERSON>, KasLoadingE<PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {Stack} from '@mui/material';
import {DEFAULT_ERROR_MSG} from '@/constants';

import {PDFPreview, ImagePreview} from './components';

interface DocumentPreviewProps {
    document: DocumentQueueModel;
}
export const DocumentPreview = ({document}: DocumentPreviewProps) => {
    const [imageState, setImageState] = useState(getDefaultState<string>());

    const loadData = async () => {
        setImageState(getLoadingState(imageState));
        const url = `/api/secured/verification/document/${document.gid}?width=1200&height=1200`;
        const response = await fetch(url);

        if (response.ok) {
            const blob = await response.blob();
            const fileUrl = URL.createObjectURL(blob);

            setImageState({
                ...imageState,
                loading: false,
                data: fileUrl,
            });
        } else {
            setImageState({
                ...imageState,
                loading: false,
                error: DEFAULT_ERROR_MSG,
            });
        }
    };

    const isPDF = (url: string) => {
        return url.endsWith('.pdf');
    };

    useEffect(() => {
        loadData().then();
        return () => {
            if (imageState.data) {
                URL.revokeObjectURL(imageState.data);
            }
        };
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={imageState.loading}>
                <Stack height={400} justifyContent='center'>
                    <KasLoading />
                </Stack>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!imageState.error}>
                <Stack height={400} justifyContent='center'>
                    <KasLoadingError error={imageState.error} view='contained' onTryAgain={loadData} />
                </Stack>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!imageState.data}>
                {imageState.data && (
                    <div className='kas-verification-document-preview'>
                        {isPDF(document.key) ? (
                            <PDFPreview src={imageState.data} document={document} />
                        ) : (
                            <ImagePreview src={imageState.data} alt={document.key} />
                        )}
                    </div>
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
