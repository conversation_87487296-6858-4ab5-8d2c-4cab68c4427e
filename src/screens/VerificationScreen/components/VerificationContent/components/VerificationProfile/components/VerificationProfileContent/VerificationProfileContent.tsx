import React, {useMemo} from 'react';
import {useVerificationProfile} from './../../../../components';
import {
    AddressContent,
    BankContent,
    BankruptcyContent,
    ChallengeContent,
    DepositContent,
    FraudContent,
    IdentityContent,
    IncomeContent,
    OFACContent,
    PEPContent,
    ProfileVisibleSections,
    RetireeBankContent,
    RetireeIncomeContent,
    SpouseContent,
} from './components';
import {Stack} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {DuplicateEmployeesCard, LinkedEmployeesCard} from './components/cards';

export const VerificationProfileContent = () => {
    const {activeQueue} = useVerification();
    const {detailsState} = useVerificationProfile();
    const hasLinkedEmployees = !!detailsState.data?.review_matches_by_ip?.length;

    const renderContent = useMemo(() => {
        if (detailsState.data) {
            switch (activeQueue) {
                case VerificationQueueItemType.Income:
                    return <IncomeContent data={detailsState.data} />;
                case VerificationQueueItemType.Deposit:
                    return <DepositContent data={detailsState.data} />;
                case VerificationQueueItemType.Address:
                    return <AddressContent data={detailsState.data} />;
                case VerificationQueueItemType.Identity:
                    return <IdentityContent data={detailsState.data} />;
                case VerificationQueueItemType.Challenge:
                    return <ChallengeContent data={detailsState.data} />;
                case VerificationQueueItemType.PEP:
                    return <PEPContent data={detailsState.data} />;
                case VerificationQueueItemType.Bankruptcy:
                    return <BankruptcyContent data={detailsState.data} />;
                case VerificationQueueItemType.OFAC:
                    return <OFACContent data={detailsState.data} />;
                case VerificationQueueItemType.Bank:
                    return <BankContent data={detailsState.data} />;
                case VerificationQueueItemType.Fraud:
                    return <FraudContent data={detailsState.data} />;
                case VerificationQueueItemType.Spouse:
                    return <SpouseContent data={detailsState.data} />;
                case VerificationQueueItemType.Retiree_Income:
                    return <RetireeIncomeContent data={detailsState.data} />;
                case VerificationQueueItemType.Retiree_Bank:
                    return <RetireeBankContent data={detailsState.data} />;
                default:
                    return null;
            }
        }

        return null;
    }, [activeQueue, detailsState.data]);

    if (!detailsState.data) {
        return null;
    }

    return (
        <Stack rowGap={1}>
            <ProfileVisibleSections />
            {renderContent}
            {activeQueue !== VerificationQueueItemType.Address && <DuplicateEmployeesCard />}
            {hasLinkedEmployees && <LinkedEmployeesCard data={detailsState.data.review_matches_by_ip} />}
        </Stack>
    );
};
