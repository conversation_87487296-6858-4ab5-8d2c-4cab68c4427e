import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography, useTheme} from '@mui/material';
import {VerificationQueueDuplicatesModel} from '@/interfaces';
import {KasExpandIcon} from '@/components';
import {ColumnDef, Row} from '@tanstack/react-table';
import {DuplicateEmployeesTableColumns} from './tables';
import {VERIFICATION_DUPLICATE_EMPLOYEE_ID} from '@/screens/VerificationScreen/data';
import {DuplicateLoanInfo} from '@/screens/VerificationScreen/components/VerificationContent/components/VerificationProfile/components/VerificationProfileContent/components/cards/DuplicateEmployeesCard/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {TableView} from '@/views';

export const DuplicateEmployeesCard = () => {
    const {palette} = useTheme();
    const {employeeDuplicateState} = useVerificationProfile();
    const [isAccordionOpen, setIsAccordionOpen] = useState(true);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    if (!employeeDuplicateState.data?.length) {
        return null;
    }

    return (
        <div id={VERIFICATION_DUPLICATE_EMPLOYEE_ID}>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    sx={{paddingLeft: '8px'}}
                    expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                    onDoubleClick={handleAccordionChange}>
                    <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                        <Typography variant='subtitle1' color={palette.error.main}>
                            Duplicate Employees
                        </Typography>
                    </Stack>
                </AccordionSummary>
                <AccordionDetails>
                    <Box px={3}>
                        <TableView<VerificationQueueDuplicatesModel>
                            loading={employeeDuplicateState.loading}
                            error={employeeDuplicateState.error}
                            data={employeeDuplicateState.data}
                            columns={
                                DuplicateEmployeesTableColumns as ColumnDef<
                                    VerificationQueueDuplicatesModel,
                                    unknown
                                >[]
                            }
                            renderExpand={(row: Row<VerificationQueueDuplicatesModel>) => (
                                <DuplicateLoanInfo data={row.original} />
                            )}
                        />
                    </Box>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
