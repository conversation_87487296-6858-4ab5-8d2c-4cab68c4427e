import React, {useEffect, useState} from 'react';
import {PDFDocument} from 'pdf-lib';
import dayjs from 'dayjs';
import {FormControl, FormHelperText, Grid2, Select, TextField, Typography} from '@mui/material';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {useFormik} from 'formik';
import {PEPFormValues, validationSchema} from './schema';
import MenuItem from '@mui/material/MenuItem';
import {useAppSelector} from '@/lib/hooks';
import {selectUser} from '@/lib/slices/userSlice';
import {useVerificationDocuments} from './../../../../index';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {PEP_DEFINITION_OPTIONS, PEP_STATUS_OPTIONS} from './data';

export const PEPForm = () => {
    const {queue, uploadDocument, setActiveModal} = useVerificationDocuments();
    const user = useAppSelector(selectUser);
    const [submitting, setSubmitting] = useState(false);
    const [documentState, setDocumentState] = useState(getDefaultState<PDFDocument>());

    const getFormData = async (values: PEPFormValues, document: PDFDocument) => {
        const form = document.getForm();
        const appNameField = form.getTextField('Text2');
        const dateField = form.getTextField('Date1_af_date');
        const positionField = form.getTextField('Text3');
        const approvalField = form.getTextField('Text4');
        const screenedByField = form.getTextField('Text5');
        const PEPDefinitionDropdown = form.getDropdown('Dropdown1');
        const PEPStatusDropdown = form.getDropdown('Dropdown2');

        appNameField.setText(`${queue.employee_name} [${queue.employee_id}]`);
        dateField.setText(dayjs().format('DD/MM/YY'));
        positionField.setText(values.position);
        approvalField.setText(values.approval);
        PEPDefinitionDropdown.select(values.pepDefinition);
        if (values.pepStatus) {
            PEPStatusDropdown.select(values.pepStatus);
        }

        if (user.value) {
            const {first_name, last_name, gid} = user.value;

            if (first_name && last_name) {
                screenedByField.setText(first_name[0] + last_name[0]);
            } else {
                screenedByField.setText(`User [${gid}]`);
            }
        }

        const editedPdf = await document.save();
        const formData = new FormData();

        formData.append('files', new Blob([editedPdf], {type: 'application/pdf'}), 'Kashable PEP Form.pdf');
        formData.append('verification_id', queue.queue_id.toString());

        return formData;
    };

    const onSubmit = async (values: PEPFormValues) => {
        if (documentState.data) {
            setSubmitting(true);
            const body = await getFormData(values, documentState.data);
            await uploadDocument(body);
            setSubmitting(false);
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            pepDefinition: '',
            pepStatus: '',
            position: 'Title:  \n' + 'Dates Held: \n' + 'Location(s):\n' + 'Additional Details:',
            approval: 'Reviewed by:\n' + 'Date:\n' + 'Decision and Comments:',
        },
        onSubmit,
        validationSchema,
    });

    const loadDocument = () => {
        (async () => {
            setDocumentState(getLoadingState(documentState));
            try {
                const response = await fetch('/PEPForm.pdf');
                const pdfBytes = await response.arrayBuffer();
                const value = await PDFDocument.load(pdfBytes);
                setDocumentState(getLoadedState({value, pending: false}));
            } catch (e) {
                const error = e as Error;
                setDocumentState(getLoadedState({error: error.message || DEFAULT_ERROR_MSG, pending: false}));
            }
        })();
    };

    useEffect(loadDocument, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={documentState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!documentState.error}>
                <KasLoadingError error={documentState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!documentState.data}>
                <form onSubmit={formik.handleSubmit}>
                    <Grid2 container spacing={2} rowSpacing={2}>
                        <Grid2 size={12}>
                            <Typography variant='subtitle1'>
                                1. A politically exposed person (PEP) is as an individual who is or was
                                entrusted with prominent public functions. A PEP also includes close
                                associates and family members. Does this definition apply to you?
                            </Typography>
                        </Grid2>
                        <Grid2 size={3}>
                            <FormControl
                                fullWidth
                                size='small'
                                variant='outlined'
                                error={formik.touched.pepDefinition && !!formik.errors.pepDefinition}>
                                <Select
                                    name='pepDefinition'
                                    size='small'
                                    fullWidth
                                    disabled={submitting}
                                    value={formik.values.pepDefinition}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}>
                                    {PEP_DEFINITION_OPTIONS.map((value) => (
                                        <MenuItem key={value} value={value}>
                                            {value}
                                        </MenuItem>
                                    ))}
                                </Select>
                                {formik.touched.pepDefinition && !!formik.errors.pepDefinition && (
                                    <FormHelperText error={true}>
                                        {formik.errors.pepDefinition}
                                    </FormHelperText>
                                )}
                            </FormControl>
                        </Grid2>
                        <Grid2 size={12}>
                            <Typography variant='subtitle1'>
                                2. Are you, your family member, or your close associate, a PEP?
                            </Typography>
                        </Grid2>
                        <Grid2 size={12}>
                            <FormControl
                                fullWidth
                                size='small'
                                variant='outlined'
                                error={formik.touched.pepStatus && !!formik.errors.pepStatus}>
                                <Select
                                    name='pepStatus'
                                    size='small'
                                    fullWidth
                                    disabled={submitting}
                                    value={formik.values.pepStatus}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}>
                                    {PEP_STATUS_OPTIONS.map((value) => (
                                        <MenuItem key={value} value={value}>
                                            {value}
                                        </MenuItem>
                                    ))}
                                </Select>
                                {formik.touched.pepStatus && !!formik.errors.pepStatus && (
                                    <FormHelperText error={true}>{formik.errors.pepStatus}</FormHelperText>
                                )}
                            </FormControl>
                        </Grid2>
                        <Grid2 size={12}>
                            <Typography variant='subtitle1'>
                                3. Please tell us specifically the title, dates, and location of the position
                                held.
                            </Typography>
                        </Grid2>
                        <Grid2 size={12}>
                            <TextField
                                fullWidth
                                multiline
                                size='small'
                                name='position'
                                disabled={submitting}
                                value={formik.values.position}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                variant='outlined'
                            />
                        </Grid2>
                        <Grid2 size={12}>
                            <Typography variant='subtitle1'>Team Lead or VP Ops Only:</Typography>
                        </Grid2>
                        <Grid2 size={12}>
                            <TextField
                                fullWidth
                                multiline
                                size='small'
                                name='approval'
                                disabled={submitting}
                                value={formik.values.approval}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                variant='outlined'
                            />
                        </Grid2>
                        <Grid2 size={12}>
                            <KasModalFooter
                                disabled={!formik.isValid || submitting}
                                loading={submitting}
                                onCancel={() => setActiveModal(null)}
                            />
                        </Grid2>
                    </Grid2>
                </form>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
