import {defaultInfoColumn} from '@/utils/TableUtils';
import {createColumnHelper} from '@tanstack/react-table';
import React from 'react';
import {KasUnderwritingSharedLink} from '@/components';
import {VerificationQueueReviewMatchesModel} from '@/interfaces';

const columnHelper = createColumnHelper<VerificationQueueReviewMatchesModel>();

const _defaultInfoColumn = defaultInfoColumn<VerificationQueueReviewMatchesModel>;

export const LinkedEmployeesTableColumns = [
    _defaultInfoColumn('ip_address', 'IP Address'),
    _defaultInfoColumn('last_seen', 'Last Used (User)'),
    columnHelper.accessor('match_id', {
        id: 'match_id',
        header: 'Employee',
        cell: (props) => {
            const {match_id} = props.row.original;

            return <KasUnderwritingSharedLink id={match_id} />;
        },
    }),
    _defaultInfoColumn('match_last_seen', 'Last Used (Flagged Employee)'),
    _defaultInfoColumn('link_comment', 'Linked Flag Comment'),
];
