import React, {useMemo} from 'react';
import {Tooltip} from '@mui/material';
import {KasCancellableButton} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';

export const ApproveButton = () => {
    const {approveQueue, visitedSections, visibleSections} = useVerificationProfile();
    const unvisitedVisibleSections = visibleSections.filter((section) => !visitedSections.includes(section));

    const tooltipTitle = useMemo(() => {
        return unvisitedVisibleSections.length
            ? `You need to click 'Next' in the following sections before approval: ${unvisitedVisibleSections.join(', ')}`
            : null;
    }, [unvisitedVisibleSections]);

    return (
        <Tooltip title={tooltipTitle} placement='top'>
            <div>
                <KasCancellableButton
                    variant='contained'
                    color='success'
                    disabled={!!unvisitedVisibleSections.length}
                    delayText='APPROVING...'
                    onClick={approveQueue}>
                    APPROVE
                </KasCancellableButton>
            </div>
        </Tooltip>
    );
};
