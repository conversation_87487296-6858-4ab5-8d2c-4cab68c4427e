import React, {ChangeEvent, useState} from 'react';
import {Chip, Stack, TextField} from '@mui/material';
import {KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {toCurrency} from '@/utils/FormatUtils';
import {VerificationCardWrap} from './../index';
import IconButton from '@mui/material/IconButton';
import {Delete, Add} from '@mui/icons-material';

type ChipColorType = 'default' | 'error' | 'success';

export const IncomeInformationCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    const [chipColor, setChipColor] = useState<ChipColorType>('default');
    const [fields, setFields] = useState<string[]>(['']);

    const recalculateIncome = (updatedFields: string[]) => {
        const areAllFieldsEmpty = updatedFields.every((val) => val === '' || val === undefined);

        if (areAllFieldsEmpty) {
            setChipColor('default');
            return;
        }

        const totalIncome = updatedFields.reduce((acc, val) => acc + (Number(val) || 0), 0);
        const cycleCount = data.cycle_count || 0;
        const calculatedValue = cycleCount * totalIncome;

        if (isNaN(data.income_self) || isNaN(calculatedValue) || calculatedValue === 0) {
            setChipColor('error');
            return;
        }

        if (calculatedValue < 0.9 * data.income_self) {
            setChipColor('error');
            return;
        }

        setChipColor('success');
    };

    const onChangeIncomes = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {
        const inputValue = event.target.value;
        const updatedFields = [...fields];

        updatedFields[index] = inputValue;
        setFields(updatedFields);

        recalculateIncome(updatedFields);
    };

    const onAddField = () => {
        setFields([...fields, '']);
    };

    const onRemoveField = (index: number) => {
        const updatedFields = fields.filter((_, i) => i !== index);
        setFields(updatedFields);

        recalculateIncome(updatedFields);
    };

    return (
        <VerificationCardWrap title='Income Information' sx={{height: '100%'}}>
            <Stack rowGap={2}>
                <KasInfo label='Self-Reported Annual Income'>
                    <Chip label={toCurrency(data.income_self)} size='small' color={chipColor} />
                </KasInfo>
                <KasInfo label='Online Annual Income'>
                    {data.income_evs ? toCurrency(data.income_evs) : data.income_evs}
                </KasInfo>
                <KasInfo
                    label={
                        <>
                            Documented <b>{data.cycle_frequency}</b> Pay
                        </>
                    }>
                    <Stack rowGap={1}>
                        {fields.map((value, index) => (
                            <Stack key={index} direction='row' spacing={1} alignItems='center'>
                                <TextField
                                    type='number'
                                    variant='outlined'
                                    size='small'
                                    fullWidth
                                    value={value || ''}
                                    onChange={(e) => onChangeIncomes(e, index)}
                                    slotProps={{
                                        htmlInput: {
                                            step: '0.01',
                                        },
                                    }}
                                />
                                {index > 0 ? (
                                    <IconButton onClick={() => onRemoveField(index)} title='Delete Field'>
                                        <Delete fontSize='small' color='error' />
                                    </IconButton>
                                ) : (
                                    <IconButton onClick={onAddField} title='Add Field'>
                                        <Add fontSize='small' color='action' />
                                    </IconButton>
                                )}
                            </Stack>
                        ))}
                    </Stack>
                </KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
