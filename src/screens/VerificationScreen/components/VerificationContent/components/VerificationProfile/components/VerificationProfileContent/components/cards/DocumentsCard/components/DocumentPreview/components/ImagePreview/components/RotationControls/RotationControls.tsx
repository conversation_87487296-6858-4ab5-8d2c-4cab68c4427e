import React, {useEffect, useState} from 'react';
import {ActionCell} from '@/components/table/cells';
import {Clear, Plagiarism, RotateLeft, RotateRight} from '@mui/icons-material';
import {Stack} from '@mui/material';

interface RotationControlsProps {
    resetTransform: (animationTime?: number) => void;
    onChangeRotation: (value: number) => void;
    onCustomAction?: () => void;
}

export const RotationControls = ({
    resetTransform,
    onChangeRotation,
    onCustomAction,
}: RotationControlsProps) => {
    const [rotation, setRotation] = useState(0);

    const handleRotateLeft = () => {
        setRotation((prev) => (prev - 90) % 360);
    };

    const handleRotateRight = () => {
        setRotation((prev) => (prev + 90) % 360);
    };

    const handleReset = () => {
        setRotation(0);
        resetTransform(0);
    };

    useEffect(() => {
        onChangeRotation(rotation);
    }, [rotation]);

    return (
        <Stack flexDirection='row' justifyContent='center' py={1} columnGap={1}>
            <ActionCell
                Icon={<RotateLeft color='primary' titleAccess='Rotate Left' />}
                onClick={handleRotateLeft}
            />
            <ActionCell Icon={<Clear color='primary' titleAccess='Reset' />} onClick={handleReset} />
            {onCustomAction && (
                <ActionCell
                    Icon={<Plagiarism color='primary' titleAccess='Reset' />}
                    onClick={onCustomAction}
                />
            )}
            <ActionCell
                Icon={<RotateRight color='primary' titleAccess='Rotate Right' />}
                onClick={handleRotateRight}
            />
        </Stack>
    );
};
