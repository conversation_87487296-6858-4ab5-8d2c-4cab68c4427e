import React from 'react';
import {Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {toCurrency} from '@/utils/FormatUtils';
import {VerificationCardWrap} from './../index';
import {HiredDateInfo} from './../../../components';
import {RefundAmount} from './../components';

interface ApplicationInformationCardProps {
    data: VerificationQueueDetailsModel;
    showHiredDate?: boolean;
}

export const ApplicationInformationCard = ({
    data,
    showHiredDate = false,
}: ApplicationInformationCardProps) => {
    return (
        <VerificationCardWrap title='Application Information' sx={{height: '100%'}}>
            <Stack rowGap={2}>
                <KasInfo label='Loan Amount'>
                    {data.loan_amount ? toCurrency(data.loan_amount) : null}
                </KasInfo>
                <KasInfo label='Installment Amount'>
                    {data.amount_rounded && data.amount_rounded !== data.amount ? (
                        <>
                            {data.amount_rounded ? toCurrency(data.amount_rounded) : null} (
                            {data.amount ? toCurrency(data.amount) : null})
                        </>
                    ) : (
                        <>{data.amount ? toCurrency(data.amount) : null}</>
                    )}
                </KasInfo>
                <KasInfo label='Start Date'>{data.installment_date}</KasInfo>
                <KasInfo label='Refund Amount'>
                    {data.refund_amount ? <RefundAmount amount={data.refund_amount} /> : null}
                </KasInfo>
                {showHiredDate && <HiredDateInfo hiredDate={data.hired_date} gid={data.employee_id} />}
            </Stack>
        </VerificationCardWrap>
    );
};
