import React from 'react';
import {Stack} from '@mui/material';
import {VerificationCardWrap} from './../index';
import {KasInfo, KasLink} from '@/components';

const OFAC_URL = 'https://sanctionssearch.ofac.treas.gov/';

export const OFACCard = () => {
    const handleClick = () => {
        window.open(OFAC_URL, '_blank', 'width=1000,height=800,top=0');
    };

    return (
        <VerificationCardWrap title='OFAC Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='Message'>
                    <KasLink onClick={handleClick}>Search OFAC</KasLink>
                </KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
