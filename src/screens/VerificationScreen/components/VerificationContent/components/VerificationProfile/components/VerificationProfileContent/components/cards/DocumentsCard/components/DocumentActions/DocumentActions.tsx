import React, {useState} from 'react';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {Button, CircularProgress, Stack} from '@mui/material';
import {Archive, Comment, DeleteOutlined, Download, InfoOutlined} from '@mui/icons-material';
import {useVerificationDocuments} from './../../index';
import {VerificationDocumentModalType} from './../../interfaces';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';
import {ButtonOwnProps} from '@mui/material/Button/Button';
import {KasCircularProgress} from '@/components';

export const DocumentActions = ({document}: {document: DocumentQueueModel}) => {
    const buttonBaseProps: Partial<ButtonOwnProps> = {
        variant: 'outlined',
        size: 'small',
        sx: {paddingX: '6px'},
    };
    const {setActiveModal, documentsState} = useVerificationDocuments();
    const {getBlob} = useDownloadBlob();
    const [loadingFile, setLoadingFile] = useState(false);
    const [loadingAllFile, setLoadingAllFile] = useState(false);
    const [downloadProgress, setDownloadProgress] = useState(0);

    const onDownload = async () => {
        const params = JSON.stringify({
            path: `/secured/common/download/document/${document.gid}`,
        });

        setLoadingFile(true);
        await getBlob(params);
        setLoadingFile(false);
    };

    const onDownloadAll = async () => {
        setDownloadProgress(0);
        setLoadingAllFile(true);

        if (documentsState.data) {
            const totalFiles = documentsState.data.length;
            let completedFiles = 0;

            const promises = documentsState.data.map(async (doc) => {
                const params = JSON.stringify({
                    path: `/secured/common/download/document/${doc.gid}`,
                });

                const result = await getBlob(params);

                completedFiles += 1;
                setDownloadProgress(Math.round((completedFiles / totalFiles) * 100));

                return result;
            });

            await Promise.all(promises);
        }

        setLoadingAllFile(false);
    };

    return (
        <Stack direction='row' justifyContent='flex-end' spacing={1}>
            {document.metadata && (
                <Button
                    {...buttonBaseProps}
                    title='See Metadata'
                    onClick={() =>
                        setActiveModal({
                            type: VerificationDocumentModalType.Document_Metadata,
                            props: {documentId: document.gid, metadata: document.metadata},
                        })
                    }>
                    <InfoOutlined color='primary' />
                </Button>
            )}
            <Button
                {...buttonBaseProps}
                disabled={loadingAllFile}
                title='Download All Documents'
                onClick={onDownloadAll}>
                {loadingAllFile ? (
                    <KasCircularProgress value={downloadProgress} size={28} />
                ) : (
                    <Archive color='primary' />
                )}
            </Button>
            <Button
                {...buttonBaseProps}
                disabled={loadingFile}
                title={`Download ${document.key}`}
                onClick={onDownload}>
                {loadingFile ? <CircularProgress size={20} /> : <Download color='primary' />}
            </Button>
            <Button
                {...buttonBaseProps}
                title='Comment'
                onClick={() =>
                    setActiveModal({
                        type: VerificationDocumentModalType.Add_Comment,
                        props: {documentId: document.gid},
                    })
                }>
                <Comment color='primary' />
            </Button>
            <Button
                {...buttonBaseProps}
                title={`Delete ${document.key}`}
                color='error'
                onClick={() =>
                    setActiveModal({
                        type: VerificationDocumentModalType.Delete,
                        props: {documentId: document.gid},
                    })
                }>
                <DeleteOutlined color='error' />
            </Button>
        </Stack>
    );
};
