import React, {useMemo} from 'react';
import {Divider, Grid2, Paper, Skeleton, Stack, Typography} from '@mui/material';
import {
    KasInfo,
    KasLoadingError,
    KasLoadingStatusIcon,
    KasSwitch,
    KasSwitchWhen,
    KasUnderwritingSharedLink,
} from '@/components';
import {useVerificationProfile} from './../../useVerificationProfile';
import {VerificationActiveQueue, VerificationProfileActions} from './components';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationLookingUser} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {Refresh} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';

export const VerificationProfileHead = () => {
    const {lookingUsers, activeQueue} = useVerification();
    const {detailsState, profileId, enableVerificationActions, loadDetails} = useVerificationProfile();

    const lookingProfileUsers = useMemo(() => {
        return lookingUsers.filter((item) => {
            return item.queue === activeQueue && item.profileId === profileId;
        });
    }, [lookingUsers]);

    return (
        <Paper elevation={0}>
            <Grid2 container alignItems='center' spacing={1} py={2} px={3}>
                <Grid2 size={{xs: 12, xl: enableVerificationActions ? 5 : 12}}>
                    <Stack flexDirection='row' alignItems='center' columnGap={2}>
                        <VerificationActiveQueue />
                        <VerificationLookingUser users={lookingProfileUsers} />
                        <Divider orientation='vertical' flexItem />
                        <KasSwitch>
                            <KasSwitchWhen condition={!detailsState.data && detailsState.loading}>
                                <Skeleton variant='rounded' animation='wave' width={240} height={18} />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!detailsState.error}>
                                <KasLoadingError error={detailsState.error} />
                            </KasSwitchWhen>
                            <KasSwitchWhen condition={!!detailsState.data}>
                                <div>
                                    <Typography variant='h3'>
                                        {detailsState.data?.employee_name} [
                                        <KasUnderwritingSharedLink id={detailsState.data?.employee_id} />]
                                    </Typography>
                                    <KasInfo label='Employer:' isInline>
                                        {detailsState.data?.employer_name}
                                    </KasInfo>
                                </div>
                                {detailsState.loading ? (
                                    <KasLoadingStatusIcon loading={detailsState.loading} />
                                ) : (
                                    <IconButton title={'Refresh'} onClick={loadDetails}>
                                        <Refresh />
                                    </IconButton>
                                )}
                            </KasSwitchWhen>
                        </KasSwitch>
                    </Stack>
                </Grid2>
                {enableVerificationActions && (
                    <Grid2 size={{xs: 12, xl: 7}} justifyItems={{xs: 'flex-start', xl: 'flex-end'}}>
                        <VerificationProfileActions />
                    </Grid2>
                )}
            </Grid2>
        </Paper>
    );
};
