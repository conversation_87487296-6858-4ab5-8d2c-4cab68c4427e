import React from 'react';
import {Stack} from '@mui/material';
import {KasCopyText, KasInfo, KasMaskedSSN, KasUnderwritingSharedLink} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';

export const EmployeeDetailsCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Employee Details'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='ID'>
                    {data.employee_id ? <KasUnderwritingSharedLink id={data.employee_id} /> : null}
                </KasInfo>
                <KasInfo label='Name'>
                    <KasCopyText>{data.employee_name}</KasCopyText>
                </KasInfo>
                <KasInfo label='SSN'>{data.employee_ssn && <KasMaskedSSN ssn={data.employee_ssn} />}</KasInfo>
                <KasInfo label='DOB'>{data.employee_dob}</KasInfo>
                <KasInfo label='Hire Date'>{data.hired_date || 'N/A'}</KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
