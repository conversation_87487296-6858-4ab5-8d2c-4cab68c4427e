import React, {useMemo} from 'react';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {useTheme} from '@mui/material';
import {NO_RESULTS_SYMBOL} from '@/constants';

export const DocumentLastComment = ({document}: {document: DocumentQueueModel}) => {
    const {palette} = useTheme();

    const renderComment = useMemo(() => {
        if (document.metadata && document.metadata.flagged) {
            return <span style={{color: palette.error.main}}>System: potentially altered photo</span>;
        }

        if (document.comments?.length) {
            const {timestamp, user_name, text} = document.comments[0];

            return (
                <>
                    [{timestamp}][<span style={{color: palette.primary.main}}>{user_name}</span>] - {text}
                </>
            );
        }

        return NO_RESULTS_SYMBOL;
    }, [document]);

    return renderComment;
};
