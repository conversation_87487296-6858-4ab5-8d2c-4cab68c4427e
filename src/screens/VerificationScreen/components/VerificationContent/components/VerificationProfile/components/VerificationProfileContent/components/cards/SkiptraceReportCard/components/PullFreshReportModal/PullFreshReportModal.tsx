import React, {useState} from 'react';
import {Ka<PERSON><PERSON><PERSON><PERSON>, KasModalFooter} from '@/components';
import {Alert, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';

interface PullFreshReportModalProps {
    employeeId: number;
    onClose: () => void;
    onSuccess: () => void;
}

export const PullFreshReportModal = ({employeeId, onClose, onSuccess}: PullFreshReportModalProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        setSubmitting(true);

        const response = await apiRequest(`/api/secured/underwriting/reports/${employeeId}/skiptrace`, {
            method: 'POST',
        });

        if (response.value) {
            onSuccess();
            onClose();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <KasModal title='Pull Skiptrace Report' open={true} onClose={onClose}>
            <form onSubmit={formik.handleSubmit}>
                <Grid2 container spacing={2} rowSpacing={2}>
                    <Grid2 size={12}>
                        <Alert severity='warning'>Are you sure you want to pull a fresh report?</Alert>
                    </Grid2>
                    <Grid2 size={12}>
                        <KasModalFooter submitText='Yes' loading={submitting} onCancel={onClose} />
                    </Grid2>
                </Grid2>
            </form>
        </KasModal>
    );
};
