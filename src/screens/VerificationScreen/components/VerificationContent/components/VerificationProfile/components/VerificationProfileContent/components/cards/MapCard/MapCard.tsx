import React from 'react';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {MapView} from '@/views';
import {VerificationCardWrap} from './../index';

export const MapCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Map'>
            <MapView
                style={{height: '100%', minHeight: '350px'}}
                geocode={data.google_geocode}
                markers={
                    data.google_geocode && data.google_geocode?.lng && data.google_geocode?.lat
                        ? [data.google_geocode]
                        : []
                }
            />
        </VerificationCardWrap>
    );
};
