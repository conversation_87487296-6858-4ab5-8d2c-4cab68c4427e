import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography} from '@mui/material';
import {KasExpandIcon} from '@/components';
import {BankruptcyReportView} from '@/views';
import {BankruptcyReportModel} from '@/interfaces';

interface BankruptcyCardItemProps {
    bankruptcy: BankruptcyReportModel;
    expanded?: boolean;
}
export const BankruptcyCardItem = ({bankruptcy, expanded = false}: BankruptcyCardItemProps) => {
    const [isAccordionOpen, setIsAccordionOpen] = useState(expanded);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                onDoubleClick={handleAccordionChange}>
                <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                    <Typography variant='subtitle1'>Bankruptcy Report</Typography>
                    <Typography variant='body1'>({bankruptcy.report_date})</Typography>
                </Stack>
            </AccordionSummary>
            <AccordionDetails>
                <Box px={3} pb={2}>
                    {!!bankruptcy.report?.records?.length && (
                        <BankruptcyReportView records={bankruptcy.report.records} />
                    )}
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
