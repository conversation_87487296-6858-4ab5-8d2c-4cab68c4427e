import React from 'react';
import {Grid2, Skeleton, Stack} from '@mui/material';
import {KasLoading} from '@/components';

export const DocumentsLoading = () => {
    return (
        <Grid2 container spacing={2}>
            <Grid2 size={12}>
                <Stack height={400} justifyContent='center'>
                    <KasLoading />
                </Stack>
            </Grid2>
            <Grid2 size={6}>
                <Skeleton height={20} width='70%' />
                <Skeleton height={20} width='50%' />
            </Grid2>
            <Grid2 size={6}>
                <Stack direction='row' justifyContent='flex-end' spacing={1}>
                    <Skeleton variant='rounded' animation='wave' height={28} width={28} />
                    <Skeleton variant='rounded' animation='wave' height={28} width={28} />
                    <Skeleton variant='rounded' animation='wave' height={28} width={28} />
                    <Skeleton variant='rounded' animation='wave' height={28} width={28} />
                </Stack>
            </Grid2>
        </Grid2>
    );
};
