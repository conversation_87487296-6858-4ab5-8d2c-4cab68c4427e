import './styles.scss';

import React, {useEffect, useState} from 'react';
import {getDefaultState, getLoadingState} from '@/utils/DataStateUtils';
import {<PERSON><PERSON><PERSON>oa<PERSON>, <PERSON><PERSON><PERSON>oading<PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON><PERSON>, KasSwitch, Kas<PERSON><PERSON><PERSON><PERSON>} from '@/components';
import {Stack} from '@mui/material';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {ImagePreview} from './../../components';

interface PDFPreviewProps {
    src: string;
    document: DocumentQueueModel;
}

export const PDFPreview = ({src, document}: PDFPreviewProps) => {
    const [isOpen, setIsOpen] = useState(false);

    const openModal = () => setIsOpen(true);
    const closeModal = () => setIsOpen(false);

    const [imageState, setImageState] = useState(getDefaultState<string>());

    const loadData = async () => {
        setImageState(getLoadingState(imageState));
        const url = JSON.stringify({
            path: `common/download/document/${document.gid}`,
        });
        const response = await fetch(`/api/secured/download-blob?params=${url}`);

        if (response.ok) {
            const headers: Record<string, string> = {};

            response.headers.forEach((value: string, key: string) => {
                headers[key] = value;
            });

            const blob = await response.blob();

            const fileUrl = window.URL.createObjectURL(blob);

            setImageState({
                ...imageState,
                loading: false,
                data: fileUrl,
            });
        } else {
            setImageState({
                ...imageState,
                loading: false,
                error: DEFAULT_ERROR_MSG,
            });
        }
    };

    useEffect(() => {
        loadData().then();
        return () => {
            if (imageState.data) {
                URL.revokeObjectURL(imageState.data);
            }
        };
    }, []);

    return (
        <div className='kas-verification-pdf-preview'>
            <div className='kas-verification-pdf-preview__image'>
                <ImagePreview src={src} alt={document.key} onClick={openModal} />
            </div>
            <KasModal title={document.key} open={isOpen} size='large' onClose={closeModal}>
                <KasSwitch>
                    <KasSwitchWhen condition={imageState.loading}>
                        <Stack height={400} justifyContent='center'>
                            <KasLoading />
                        </Stack>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!imageState.error}>
                        <Stack height={400} justifyContent='center'>
                            <KasLoadingError error={imageState.error} onTryAgain={loadData} />
                        </Stack>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!imageState.data}>
                        {imageState.data && (
                            <iframe
                                src={imageState.data}
                                style={{width: '100%', height: 'calc(100vh - 250px)', minHeight: '600px'}}
                            />
                        )}
                    </KasSwitchWhen>
                </KasSwitch>
            </KasModal>
        </div>
    );
};
