import React, {ReactNode} from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {KasNoResults} from '@/components';

interface EmptyCardProps {
    title: string;
    text: string;
    actions?: ReactNode;
}
export const EmptyCard = ({title, text, actions}: EmptyCardProps) => {
    return (
        <Paper elevation={0}>
            <Stack px={3} py={2.5} rowGap={2}>
                <Stack flexDirection='row' alignItems='center' justifyContent='space-between'>
                    <Typography variant='subtitle1'>{title}</Typography>
                    {actions}
                </Stack>
                <KasNoResults text={text} p={2} bgcolor='var(--color-grey)' />
            </Stack>
        </Paper>
    );
};
