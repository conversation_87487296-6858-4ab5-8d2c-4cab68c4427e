import React, {useState} from 'react';
import {useFormik} from 'formik';
import {EditHiredDateFormValues, validationSchema} from './schema';
import {Grid2} from '@mui/material';
import {KasDatePicker<PERSON><PERSON><PERSON><PERSON>, Kas<PERSON>odalFooter} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import dayjs from 'dayjs';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {VerificationProfileEditHiredDateActionProps} from './../../../../interfaces';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';

export const EditHiredDateForm = ({lastHireDate, gid}: VerificationProfileEditHiredDateActionProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const {setOpenVerificationProfileModal, loadDetails} = useVerificationProfile();

    const onSubmit = async (values: EditHiredDateFormValues) => {
        const url = '/api/secured/underwriting/edit/employee';
        const body = JSON.stringify({
            gid,
            last_hire_date: dayjs(values.lastHireDate).format('YYYYMMDD'),
        });

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'put', body});

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenVerificationProfileModal(null);
            await loadDetails();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            lastHireDate: lastHireDate || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <KasDatePickerFormField
                        disableFuture={true}
                        formik={formik}
                        name='lastHireDate'
                        label='Hired Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenVerificationProfileModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
