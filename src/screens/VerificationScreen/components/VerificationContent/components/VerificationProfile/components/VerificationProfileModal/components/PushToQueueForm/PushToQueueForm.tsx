import './styles.scss';

import React, {ChangeEvent, useEffect, useState} from 'react';
import {useFormik} from 'formik';
import {validationSchema, DeclineFormValues} from './schema';
import {Checkbox, FormControlLabel, Grid2, <PERSON>Field, Typography} from '@mui/material';
import {Kas<PERSON><PERSON>ding<PERSON><PERSON><PERSON>, KasModalFooter, KasSwitch, KasSwitchWhen} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {VerificationProfilePushToQueueActionProps} from './../../../../interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {VerificationQueueApplicationModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {QueueApplicationHistory, QueueApplicationLoading} from './components';
import {getQueueTitle} from '@/utils/VerificationUtils';

export const PushToQueueForm = ({applicationId, source}: VerificationProfilePushToQueueActionProps) => {
    const {showMessage} = useSnackbar();
    const [dataState, setDataState] = useState(getDefaultState<VerificationQueueApplicationModel[]>());
    const [historyState, setHistoryState] = useState(getDefaultState<VerificationQueueApplicationModel[]>());
    const [submitting, setSubmitting] = useState(false);
    const {pushToQueue, setOpenVerificationProfileModal} = useVerificationProfile();
    const [selectedCodes, setSelectedCodes] = useState<string[]>([]);

    const handleChangeCodes = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedCodes.includes(value)) {
            setSelectedCodes(selectedCodes.filter((item) => item !== value));
        } else {
            setSelectedCodes([...selectedCodes, value]);
        }
    };

    const onSubmit = async (values: DeclineFormValues) => {
        const body = JSON.stringify({
            application_id: applicationId,
            comment: values.comment,
            source_gid: source,
            summaries: dataState.data
                ?.filter(({code}) => selectedCodes.includes(code))
                .map(({gid, code}) => ({gid, code})),
        });
        setSubmitting(true);
        await pushToQueue(applicationId, body);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    const loadData = async () => {
        setDataState(getLoadingState(dataState));
        const url = `/api/secured/verification/queue/application/${applicationId}`;
        const response = await apiRequest(url);

        setDataState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            setOpenVerificationProfileModal(null);
        }
    };

    const loadHistoryData = async () => {
        setHistoryState(getLoadingState(historyState));
        const url = `/api/secured/verification/queue/application/${applicationId}?history=true`;
        const response = await apiRequest(url);

        let loaded = getLoadedState<VerificationQueueApplicationModel[]>(response);
        if (loaded.data) {
            const filtered = loaded.data
                .filter((qvh) => qvh.application_id < applicationId)
                .filter((qvh) => qvh.verified);

            const grouped = Object.groupBy(filtered, (qvh) => qvh.code);

            const mostRecentItems = Object.values(grouped).map((items) => {
                // find the item with the greatest verify_time
                return items!.sort((a, b) => ((a.verify_time || '') > (b.verify_time || '') ? 1 : -1))[0];
            });

            loaded.data = mostRecentItems;
        }

        setHistoryState(loaded);
    };

    useEffect(() => {
        loadData().then();
        loadHistoryData().then();
    }, []);

    return (
        <form className='kas-verification-decline-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={3} spacing={2}>
                <Grid2 size={12}>
                    <KasSwitch>
                        <KasSwitchWhen condition={dataState.loading}>
                            <QueueApplicationLoading />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!dataState.error}>
                            <KasLoadingError error={dataState.error} onTryAgain={loadData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!dataState.data}>
                            {!!dataState.data && (
                                <Grid2 container>
                                    {dataState.data?.map((item, index) => (
                                        <Grid2 size={3} key={`code-${item.gid}-${index}`}>
                                            <FormControlLabel
                                                label={getQueueTitle(item.code)}
                                                control={
                                                    <Checkbox
                                                        size='small'
                                                        value={item.code}
                                                        checked={
                                                            (item.queued && !item.verified) ||
                                                            selectedCodes.includes(item.code)
                                                        }
                                                        disabled={item.queued && !item.verified}
                                                        onChange={handleChangeCodes}
                                                    />
                                                }
                                            />
                                        </Grid2>
                                    ))}
                                    <Grid2 size={12} pt={2}>
                                        <Typography variant='subtitle1' mb={1}>
                                            Recently Passed
                                        </Typography>
                                        <QueueApplicationHistory data={dataState.data} isActive={true} />
                                    </Grid2>
                                </Grid2>
                            )}
                        </KasSwitchWhen>
                    </KasSwitch>
                </Grid2>
                <Grid2 size={12}>
                    <KasSwitch>
                        <KasSwitchWhen condition={historyState.loading}>
                            <QueueApplicationLoading />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!historyState.error}>
                            <KasLoadingError error={historyState.error} onTryAgain={loadHistoryData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!historyState.data && !!historyState.data.length}>
                            <Typography variant='subtitle1' mb={1}>
                                Previously Passed
                            </Typography>
                            {!!historyState.data && <QueueApplicationHistory data={historyState.data} />}
                        </KasSwitchWhen>
                    </KasSwitch>
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={submitting || dataState.loading}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Message'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || submitting || selectedCodes.length === 0}
                        submitText='Push'
                        onCancel={() => setOpenVerificationProfileModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
