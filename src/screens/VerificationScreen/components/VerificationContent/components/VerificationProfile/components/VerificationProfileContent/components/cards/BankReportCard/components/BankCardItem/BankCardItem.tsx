import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography} from '@mui/material';
import {BankReportView} from '@/views';
import {KasExpandIcon} from '@/components';
import {BankReportModel} from '@/interfaces';

interface BankCardItemProps {
    bank: BankReportModel;
    title: string;
    expanded?: boolean;
}
export const BankCardItem = ({bank, title, expanded = false}: BankCardItemProps) => {
    const [isAccordionOpen, setIsAccordionOpen] = useState(expanded);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                onDoubleClick={handleAccordionChange}>
                <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                    <Typography variant='subtitle1'>{title}</Typography>
                    <Typography variant='body1'>({bank.report_date})</Typography>
                </Stack>
            </AccordionSummary>
            <AccordionDetails>
                <Box px={3} pb={2}>
                    <BankReportView
                        gid={bank.gid}
                        employeeId={bank.employee_id}
                        applications={bank.applications}
                    />
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
