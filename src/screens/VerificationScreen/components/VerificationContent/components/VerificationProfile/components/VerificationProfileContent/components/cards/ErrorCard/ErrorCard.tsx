import React from 'react';
import {Paper, Stack, Typography} from '@mui/material';
import {KasLoadingError} from '@/components';

interface ErrorCardProps {
    title: string;
    error: string;
    onTryAgain: () => void;
}
export const ErrorCard = ({title, error, onTryAgain}: ErrorCardProps) => {
    return (
        <Paper elevation={0}>
            <Stack px={3} pt={2.5} pb={3} rowGap={2}>
                <Typography variant='subtitle1'>{title}</Typography>
                <KasLoadingError error={error} view='contained' onTryAgain={onTryAgain} />
            </Stack>
        </Paper>
    );
};
