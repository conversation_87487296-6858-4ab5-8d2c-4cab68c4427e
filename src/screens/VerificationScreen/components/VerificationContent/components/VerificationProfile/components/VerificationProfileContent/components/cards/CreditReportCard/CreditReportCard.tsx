import React, {useEffect, useState} from 'react';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {EmptyCard, ErrorCard, LoadingCard} from './../index';
import {CreditCardItem} from './components';
import {CreditReportModel} from '@/interfaces';

interface CreditReportCardProps {
    data: VerificationQueueDetailsModel;
}

const CARD_TITLE = 'Credit Report';

export const CreditReportCard = ({data}: CreditReportCardProps) => {
    const [creditState, setCreditState] = useState(getDefaultState<CreditReportModel[]>());

    const loadData = async () => {
        setCreditState(getLoadingState(creditState));
        const response = await apiRequest(`/api/secured/underwriting/reports/${data.employee_id}/credit`);
        setCreditState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={creditState.loading}>
                <LoadingCard title={CARD_TITLE} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!creditState.error}>
                <ErrorCard title={CARD_TITLE} error={creditState.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!creditState.data}>
                {!!creditState.data?.length ? (
                    <Grid2 container rowGap={2}>
                        {creditState.data.map((credit, index) => (
                            <Grid2 key={credit.gid} size={12}>
                                <CreditCardItem credit={credit} title={CARD_TITLE} expanded={index === 0} />
                            </Grid2>
                        ))}
                    </Grid2>
                ) : (
                    <EmptyCard
                        title={CARD_TITLE}
                        text={`No Credit Reports Found for Employee ID#: ${data.employee_id}`}
                    />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
