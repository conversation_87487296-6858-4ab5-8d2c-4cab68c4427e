import React from 'react';
import {ReactZoomPanPinchContext} from 'react-zoom-pan-pinch';
import {Stack} from '@mui/material';
import Slider from '@mui/material/Slider';

interface ZoomControlsProps {
    value: number;
    instance: ReactZoomPanPinchContext;
    zoomIn: (step?: number, animationTime?: number) => void;
    zoomOut: (step?: number, animationTime?: number) => void;
}

export const ZoomControls = ({value, instance, zoomIn, zoomOut}: ZoomControlsProps) => {
    const handleSliderChange = (newValue: number) => {
        const {scale} = instance.transformState;
        const factor = Math.log(newValue / scale);

        if (newValue > scale) {
            zoomIn(factor, 0);
        } else {
            zoomOut(-factor, 0);
        }
    };

    return (
        <Stack
            flexDirection='row'
            alignItems='center'
            py={1}
            columnGap={2}
            sx={{
                width: '300px',
                position: 'absolute',
                bottom: 0,
                left: '50%',
                marginLeft: '-150px',
                zIndex: 10,
            }}>
            <Slider
                value={value}
                step={0.1}
                min={1}
                max={5}
                onChange={(_event, newValue) => handleSliderChange(newValue as number)}
            />
        </Stack>
    );
};
