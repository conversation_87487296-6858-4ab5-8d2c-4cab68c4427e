import React, {useEffect, useRef, useState} from 'react';
import {TransformComponent, TransformWrapper} from 'react-zoom-pan-pinch';
import {Paper} from '@mui/material';
import {KasLoadingBackDrop} from '@/components';
import {RotationControls, ZoomControls} from './components';

interface ImagePreviewProps {
    src: string;
    alt: string;
    onClick?: () => void;
}

const INITIAL_SCALE = 1;

export const ImagePreview = ({src, alt, onClick}: ImagePreviewProps) => {
    const [curScale, setCurScale] = useState(INITIAL_SCALE);
    const [rotation, setRotation] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const imageRef = useRef<HTMLImageElement | null>(null);

    const drawImage = () => {
        if (!canvasRef.current || !imageRef.current) return;

        const image = imageRef.current;
        const imageWidth = image.width;
        const imageHeight = image.height;
        const canvas = canvasRef.current;
        const canvasSize = Math.max(imageWidth, imageHeight);
        const ctx = canvas.getContext('2d');

        if (!ctx) return;

        canvas.width = canvasSize;
        canvas.height = canvasSize;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        ctx.save();
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((rotation * Math.PI) / 180);
        ctx.drawImage(image, -imageWidth / 2, -imageHeight / 2);
        ctx.restore();

        setIsLoading(false);
    };

    useEffect(drawImage, [rotation]);

    return (
        <TransformWrapper
            initialScale={INITIAL_SCALE}
            centerOnInit
            limitToBounds={true}
            disablePadding
            onTransformed={(_ref, {scale}) => {
                setCurScale(scale);
            }}
            wheel={{disabled: true}}
            doubleClick={{disabled: false}}>
            {({resetTransform, instance, zoomIn, zoomOut}) => (
                <>
                    <Paper sx={{position: 'relative'}}>
                        <TransformComponent
                            wrapperStyle={{
                                width: '100%',
                                height: '400px',
                                cursor: 'move',
                                overflow: 'hidden',
                            }}>
                            {isLoading && <KasLoadingBackDrop />}
                            <canvas
                                ref={canvasRef}
                                style={{
                                    width: '100%',
                                    maxHeight: '400px',
                                    height: 'auto',
                                    objectFit: 'contain',
                                }}
                            />
                            <img
                                ref={imageRef}
                                src={src}
                                alt={alt}
                                style={{display: 'none'}}
                                onLoad={drawImage}
                            />
                        </TransformComponent>
                        <ZoomControls
                            instance={instance}
                            value={curScale}
                            zoomIn={zoomIn}
                            zoomOut={zoomOut}
                        />
                    </Paper>
                    <RotationControls
                        resetTransform={resetTransform}
                        onChangeRotation={setRotation}
                        onCustomAction={onClick}
                    />
                </>
            )}
        </TransformWrapper>
    );
};
