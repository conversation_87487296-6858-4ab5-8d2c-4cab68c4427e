import {MetadataModel} from '@/interfaces';

export enum VerificationDocumentModalType {
    Add_Comment = 'Add Comment',
    Delete = 'Delete',
    Document_Metadata = 'Document Metadata',
    Upload_Documents = 'Upload Documents',
    PEP_Form = 'PEP Form',
}

export type VerificationDocumentModalProps =
    | {
          type: VerificationDocumentModalType.Add_Comment;
          props: {documentId: number};
      }
    | {
          type: VerificationDocumentModalType.Delete;
          props: {documentId: number};
      }
    | {
          type: VerificationDocumentModalType.Document_Metadata;
          props: {documentId: number; metadata: MetadataModel};
      }
    | {
          type: VerificationDocumentModalType.Upload_Documents;
      }
    | {
          type: VerificationDocumentModalType.PEP_Form;
      };
