import React, {useEffect, useState} from 'react';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {BankCardItem} from './components';
import {EmptyCard, ErrorCard, LoadingCard} from './../index';
import {BankReportModel} from '@/interfaces';

interface BankReportCardProps {
    data: VerificationQueueDetailsModel;
}

const CARD_TITLE = 'Bank Report';

export const BankReportCard = ({data}: BankReportCardProps) => {
    const [bankState, setBankState] = useState(getDefaultState<BankReportModel[]>());

    const loadData = async () => {
        setBankState(getLoadingState(bankState));
        const response = await apiRequest(
            `/api/secured/underwriting/reports/${data.employee_id}/bank/verification`,
        );
        setBankState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={bankState.loading}>
                <LoadingCard title={CARD_TITLE} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!bankState.error}>
                <ErrorCard title={CARD_TITLE} error={bankState.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!bankState.data}>
                {!!bankState.data?.length ? (
                    <Grid2 container rowGap={2}>
                        {bankState.data.map((bank, index) => (
                            <Grid2 key={bank.gid} size={12}>
                                <BankCardItem bank={bank} title={CARD_TITLE} expanded={index === 0} />
                            </Grid2>
                        ))}
                    </Grid2>
                ) : (
                    <EmptyCard
                        title={CARD_TITLE}
                        text={`No Bank Reports Found for Employee ID#: ${data.employee_id}`}
                    />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
