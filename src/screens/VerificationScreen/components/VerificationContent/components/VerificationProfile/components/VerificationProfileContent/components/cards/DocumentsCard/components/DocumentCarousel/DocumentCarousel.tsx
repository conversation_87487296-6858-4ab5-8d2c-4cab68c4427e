import './styles.scss';

import React, {useEffect, useRef} from 'react';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {ChevronLeft, ChevronRight} from '@mui/icons-material';
import Box from '@mui/material/Box';
import {DocumentPreview} from './../../components';
import Slider, {CustomArrowProps, Settings} from 'react-slick';
import {Typography} from '@mui/material';

interface DocumentCarouselProps {
    documents: DocumentQueueModel[];
    activeIndex: number;
    onChangeDocument: (index: number) => void;
}

const CustomPrevArrow = (props: CustomArrowProps) => {
    const {className, style, onClick} = props;
    return (
        <div className={className} style={{...style}} onClick={onClick}>
            <ChevronLeft fontSize='large' color='inherit' />
        </div>
    );
};

const CustomNextArrow = (props: CustomArrowProps) => {
    const {className, style, onClick} = props;
    return (
        <div className={className} style={{...style}} onClick={onClick}>
            <ChevronRight fontSize='large' color='inherit' />
        </div>
    );
};

export const DocumentCarousel = ({documents, activeIndex, onChangeDocument}: DocumentCarouselProps) => {
    const showNumericDots = documents.length > 14;
    const sliderRef = useRef<Slider | null>(null);

    const settings: Settings = {
        dots: !showNumericDots,
        infinite: false,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        swipe: false,
        lazyLoad: 'ondemand',
        nextArrow: <CustomNextArrow />,
        prevArrow: <CustomPrevArrow />,
    };

    useEffect(() => {
        if (sliderRef.current) {
            sliderRef.current.slickGoTo(activeIndex);
        }
    }, [activeIndex]);

    return (
        <Box key={documents.length} className='kas-document-carousel' px={3} pb={1}>
            <Slider
                ref={sliderRef}
                {...settings}
                initialSlide={activeIndex}
                onInit={() => {
                    onChangeDocument(0);
                }}
                beforeChange={(_, nextSlide) => {
                    onChangeDocument(nextSlide);
                }}>
                {documents.map((document, index) => (
                    <DocumentPreview key={index} document={document} />
                ))}
            </Slider>
            {showNumericDots && (
                <Typography
                    className='kas-document-carousel__info'
                    variant='body1'
                    color='textDisabled'
                    textAlign='center'>
                    {activeIndex + 1}st document of {documents.length}
                </Typography>
            )}
        </Box>
    );
};
