import {createColumnHelper} from '@tanstack/react-table';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {DocumentLastComment, DocumentOpenLink} from './../components';

export const _columnHelper = createColumnHelper<DocumentQueueModel>();

export const _defaultInfoColumn = defaultInfoColumn<DocumentQueueModel>;

export const DocumentsCardTableColumns = [
    _columnHelper.accessor('gid', {
        id: 'expander',
        header: '',
        cell: (props) => {
            return !!props.row.original.comments?.length ? expandCell(props) : null;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    }),
    _columnHelper.accessor('key', {
        id: 'key',
        header: 'Name',
        cell: (props) => <DocumentOpenLink document={props.row.original} />,
    }),
    _defaultInfoColumn('date', 'Date'),
    _defaultInfoColumn('last_update_user', 'Uploaded By'),
    _columnHelper.accessor('comments', {
        id: 'comments',
        header: 'Last Comment',
        cell: (props) => <DocumentLastComment document={props.row.original} />,
    }),
];
