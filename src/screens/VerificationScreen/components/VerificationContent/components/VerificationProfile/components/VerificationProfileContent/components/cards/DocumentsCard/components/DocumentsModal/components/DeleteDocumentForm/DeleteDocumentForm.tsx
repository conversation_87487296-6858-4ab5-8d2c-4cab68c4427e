import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {Alert, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {useVerificationDocuments} from './../../../../index';

export const DeleteDocumentForm = ({documentId}: {documentId: number}) => {
    const {deleteDocument, setActiveModal} = useVerificationDocuments();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        setSubmitting(true);
        await deleteDocument(documentId);
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>Are you sure you would like to delete this file?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='OK'
                        loading={submitting}
                        onCancel={() => setActiveModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
