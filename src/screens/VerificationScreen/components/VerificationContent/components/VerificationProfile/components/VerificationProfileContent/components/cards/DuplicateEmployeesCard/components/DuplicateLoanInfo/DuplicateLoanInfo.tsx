import React from 'react';
import {VerificationQueueDuplicatesModel} from '@/interfaces';
import {Grid2, Typography} from '@mui/material';
import {KasInfo} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';

export const DuplicateLoanInfo = ({data}: {data: VerificationQueueDuplicatesModel}) => {
    return (
        <Grid2 container p={1} pl={5.5}>
            <Grid2 size={4}>
                <Typography variant='subtitle1'>Loan</Typography>
                <KasInfo label='ID:' isInline>
                    {data.loan_id}
                </KasInfo>
                <KasInfo label='Status:' isInline>
                    {data.loan_status}
                </KasInfo>
                <KasInfo label='Balance:' isInline>
                    {toCurrency(data.loan_balance)}
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <Typography variant='subtitle1'>Delinquency</Typography>
                <KasInfo label='Current:' isInline>
                    {data.current_dlq_days ? `${data.current_dlq_days} Days` : null}
                </KasInfo>
                <KasInfo label='Max:' isInline>
                    {data.max_dlq_days ? `${data.max_dlq_days} Days` : null}
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <Typography variant='subtitle1'>Payments</Typography>
                <KasInfo label='Expected:' isInline>
                    {data.expected_count
                        ? `${data.expected_count} Pymts (${toCurrency(data.expected_amount)})`
                        : null}
                </KasInfo>
                <KasInfo label='Actual:' isInline>
                    {data.repayment_count
                        ? `${data.repayment_count} Pymts (${toCurrency(data.repayment_amount)})`
                        : null}
                </KasInfo>
            </Grid2>
        </Grid2>
    );
};
