import React, {ChangeEvent, useEffect, useState} from 'react';
import {<PERSON>ton, Checkbox, FormControlLabel, Grid2, <PERSON>ack, Typography} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    <PERSON>s<PERSON><PERSON><PERSON>,
    KasLoadingBackDrop,
    KasNoResults,
    KasSearchAutocompleteField,
    KasSwitch,
    KasSwitchWhen,
} from '@/components';
import {EmailPreview, ErrorView} from '@/views';
import {VerificationEmailModel} from '@/interfaces';
import {VerificationProfileSendEmailActionProps} from './../../../../interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {ActionConfirmationModal, ActionsCheckbox, ActionsCheckboxType} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {usePrint} from '@/hooks/usePrint';

export const SendEmailForm = ({id}: VerificationProfileSendEmailActionProps) => {
    const {
        setOpenVerificationProfileModal,
        sendEmail,
        declineQueue,
        addCommentQueue,
        markBankVerificationReportInactive,
    } = useVerificationProfile();
    const url = `/api/secured/verification/${id}/email`;
    const [sendingEmail, setSendingEmail] = useState(false);
    const [showActionConfirmationModal, setShowActionConfirmationModal] = useState(false);
    const [emailsState, setEmailsState] = useState(getDefaultState<VerificationEmailModel[]>());
    const [selectedItem, setSelectedItem] = useState<VerificationEmailModel>();
    const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
    const [selectedAction, setSelectedAction] = useState<ActionsCheckboxType>(null);
    const [contentData, setContentData] = useState<string>('');
    const {handlePrint, handleEmailPrintContent} = usePrint();

    const handleChange = (type: string | null) => {
        const item = emailsState.data?.find(({email_type}) => email_type === type);

        if (item) {
            setSelectedItem(item);
            setSelectedEmails(item?.recipients.map((item) => item.email || '') || []);
        }
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedEmails.includes(value)) {
            setSelectedEmails(selectedEmails.filter((email) => email !== value));
        } else {
            setSelectedEmails([...selectedEmails, value]);
        }
    };

    useEffect(() => {
        handleEmailPrintContent(
            contentData,
            selectedItem?.recipients.filter((item) => !!item.email && selectedEmails.includes(item.email)),
            selectedItem?.email_type,
        );
    }, [selectedEmails, contentData]);

    const sendHandler = async () => {
        if (selectedAction) {
            setShowActionConfirmationModal(true);
        } else {
            await onSend();
        }
    };

    const onSend = async () => {
        setShowActionConfirmationModal(false);
        setSendingEmail(true);
        const recipients = selectedItem?.recipients.filter(
            (item) => !!item.email && selectedEmails.includes(item.email),
        );
        const body = JSON.stringify({...selectedItem, recipients});
        const callbacks: Array<() => Promise<void>> = [];

        if (selectedAction === 'decline') {
            callbacks.push(declineQueue);
        }

        if (selectedAction === 'inactive') {
            callbacks.push(markBankVerificationReportInactive);
        }

        callbacks.push(async () => {
            await addCommentQueue(`Email '${selectedItem?.email_type}' sent`, true);
        });

        await sendEmail(`${url}?preview=false`, body, callbacks);
        setSendingEmail(false);
        setOpenVerificationProfileModal(null);
    };

    const loadData = async () => {
        setEmailsState(getLoadingState(emailsState));
        const response = await apiRequest(url);

        setEmailsState(getLoadedState(response));

        if (response.value) {
            const values: VerificationEmailModel[] = response.value;

            if (values.length) {
                setSelectedItem(values[0]);
                setSelectedEmails(values[0].recipients.map((item) => item.email || ''));
            }
        }
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={emailsState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!emailsState.error}>
                <ErrorView error={emailsState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!emailsState.data}>
                {!emailsState.data || emailsState.data?.length < 1 ? (
                    <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
                ) : (
                    <div className='kas-send-email-form'>
                        {sendingEmail && <KasLoadingBackDrop />}
                        {showActionConfirmationModal && (
                            <ActionConfirmationModal
                                text={
                                    selectedAction === 'decline'
                                        ? 'Are you sure you would like to decline this application?'
                                        : 'Are you sure you would like to mark the bank verification report for this application inactive?'
                                }
                                onConfirm={onSend}
                                onClose={() => setShowActionConfirmationModal(false)}
                            />
                        )}
                        <Grid2 container spacing={1}>
                            <Grid2 size={12}>
                                <KasSearchAutocompleteField
                                    value={selectedItem?.email_type ? selectedItem?.email_type : ''}
                                    label='Type'
                                    disabled={sendingEmail}
                                    autoFocus={true}
                                    placeholder='Enter Type'
                                    options={emailsState.data.map(({email_type}) => email_type)}
                                    onSelect={handleChange}
                                />
                            </Grid2>
                            {selectedItem && (
                                <>
                                    <Grid2 size={6}>
                                        <Typography variant='subtitle1'>Recipients</Typography>
                                        {selectedItem.recipients.map((item, index) => (
                                            <div key={index}>
                                                <FormControlLabel
                                                    label={item.email}
                                                    control={
                                                        <Checkbox
                                                            size='small'
                                                            value={item.email}
                                                            disabled={sendingEmail}
                                                            checked={
                                                                !!item.email &&
                                                                selectedEmails.includes(item.email)
                                                            }
                                                            onChange={handleChangeRecipients}
                                                            inputProps={{'aria-label': 'controlled'}}
                                                        />
                                                    }
                                                />
                                            </div>
                                        ))}
                                    </Grid2>
                                    <Grid2 size={6}>
                                        <ActionsCheckbox onChange={setSelectedAction} />
                                    </Grid2>

                                    <Grid2 size={12}>
                                        <Typography variant='subtitle1' mb={1}>
                                            Preview
                                        </Typography>
                                        <EmailPreview
                                            url={url + '?preview=true'}
                                            payload={JSON.stringify(selectedItem)}
                                            onContent={(content) => setContentData(content)}
                                        />
                                    </Grid2>
                                </>
                            )}
                        </Grid2>
                        <Stack pt={3} direction='row' justifyContent='flex-end' spacing={2}>
                            <Button onClick={() => setOpenVerificationProfileModal(null)}>CLOSE</Button>
                            <Button variant='outlined' onClick={handlePrint}>
                                PRINT
                            </Button>
                            <Button
                                variant='contained'
                                disabled={!selectedEmails.length || sendingEmail}
                                onClick={sendHandler}>
                                SEND EMAIL
                            </Button>
                        </Stack>
                    </div>
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
