import React from 'react';
import IconButton from '@mui/material/IconButton';
import {Edit} from '@mui/icons-material';
import {useVerificationProfile} from './../../../../useVerificationProfile';
import {VerificationProfileModalType} from './../../../../interfaces';
import {KasInfo} from '@/components';

interface EVSInformationCardProps {
    hiredDate: string | null;
    gid: number;
}

export const HiredDateInfo = ({hiredDate, gid}: EVSInformationCardProps) => {
    const {setOpenVerificationProfileModal} = useVerificationProfile();

    return (
        <KasInfo label='Hired Date'>
            {hiredDate || 'N/A'}{' '}
            <IconButton
                onClick={() =>
                    setOpenVerificationProfileModal({
                        type: VerificationProfileModalType.EDIT_HIRED_DATE,
                        props: {lastHireDate: hiredDate, gid},
                    })
                }>
                <Edit sx={{height: 16}} />
            </IconButton>
        </KasInfo>
    );
};
