import React from 'react';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {VerificationProfileSectionType} from './../../../../../../interfaces';
import {ContactInformationCard} from './../../../cards';

export const ContactInformationSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection} = useVerificationProfile();
    return (
        <div hidden={activeSection !== VerificationProfileSectionType.Phone_Number}>
            <ContactInformationCard data={data} />
        </div>
    );
};
