import React, {useEffect, useState} from 'react';
import {Checkbox, FormControlLabel, Typography} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';

export type ActionsCheckboxType = 'decline' | 'inactive' | null;

interface ActionsCheckboxProps {
    onChange: (value: ActionsCheckboxType) => void;
}

export const ActionsCheckbox = ({onChange}: ActionsCheckboxProps) => {
    const {activeQueue} = useVerification();
    const [selectedCheckbox, setSelectedCheckbox] = useState<ActionsCheckboxType>(null);

    useEffect(() => {
        onChange(selectedCheckbox);
    }, [selectedCheckbox]);

    return (
        <>
            <Typography variant='subtitle1'>Actions</Typography>
            <FormControlLabel
                label='Decline Application'
                control={
                    <Checkbox
                        size='small'
                        value='decline'
                        checked={selectedCheckbox === 'decline'}
                        onChange={(_event, checked) => {
                            setSelectedCheckbox(checked ? 'decline' : null);
                        }}
                    />
                }
            />
            {(activeQueue === VerificationQueueItemType.Bank ||
                activeQueue === VerificationQueueItemType.Retiree_Bank) && (
                <FormControlLabel
                    label='Mark Bank Verification Report Inactive'
                    control={
                        <Checkbox
                            size='small'
                            value='inactive'
                            checked={selectedCheckbox === 'inactive'}
                            onChange={(_event, checked) => {
                                setSelectedCheckbox(checked ? 'inactive' : null);
                            }}
                        />
                    }
                />
            )}
        </>
    );
};
