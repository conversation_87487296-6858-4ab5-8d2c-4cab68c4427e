import React, {useEffect} from 'react';
import {<PERSON><PERSON>, Chip, Stack} from '@mui/material';
import {East} from '@mui/icons-material';
import {useVerificationProfile} from './../../../../components';
import {SectionCheckMark} from './components';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {VerificationProfileSectionType} from './../../interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';

export const VerificationProfileSections = () => {
    const {activeQueue} = useVerification();
    const {
        activeSection,
        changeActiveSection,
        visitedSections,
        setActiveSection,
        visibleSections,
        enableVerificationActions,
        detailsState,
    } = useVerificationProfile();

    useEffect(() => {
        if (!activeSection && visibleSections.length > 0) {
            changeActiveSection(visibleSections[0]);
        }
    }, [activeSection, visibleSections]);

    useEffect(() => {
        const documentCount = detailsState.data?.document_count || 0;
        const isBankWithDocs = activeQueue === VerificationQueueItemType.Bank && documentCount > 0;

        if (isBankWithDocs && visibleSections.includes(VerificationProfileSectionType.Documents)) {
            changeActiveSection(VerificationProfileSectionType.Documents);
        }
    }, []);

    return (
        <Stack flexDirection='row' justifyContent='space-between' columnGap={1}>
            <Stack flexDirection='row' columnGap={1}>
                {visibleSections.map((item) => (
                    <Chip
                        key={item}
                        label={item}
                        variant='outlined'
                        sx={{pointerEvents: item === activeSection ? 'none' : 'fill'}}
                        color={item === activeSection ? 'secondary' : undefined}
                        icon={<SectionCheckMark item={item} />}
                        onClick={() => setActiveSection(item)}
                    />
                ))}
            </Stack>
            {enableVerificationActions && (
                <Button
                    disabled={visibleSections.length === visitedSections.length}
                    variant='outlined'
                    sx={{width: '58px', height: '58px', marginY: '-9px'}}
                    title='Next'
                    color='info'
                    onClick={() => {
                        if (activeSection) {
                            const activeSectionIndex = visibleSections.indexOf(activeSection);

                            changeActiveSection(visibleSections[activeSectionIndex + 1]);
                        }
                    }}>
                    <East color='primary' />
                </Button>
            )}
        </Stack>
    );
};
