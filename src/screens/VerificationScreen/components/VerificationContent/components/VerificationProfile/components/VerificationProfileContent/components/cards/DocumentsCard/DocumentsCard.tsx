import React, {useEffect, useMemo} from 'react';
import {VerificationCardWrap, useVerificationDocuments} from './../index';
import {KasLoading<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {But<PERSON>, Chip, Grid2, Stack} from '@mui/material';
import {
    DocumentActions,
    DocumentCarousel,
    DocumentComments,
    DocumentInfo,
    DocumentsLoading,
    DocumentsModal,
} from './components';
import {NoResultsView, TableView} from '@/views';
import {VerificationDocumentModalType} from './interfaces';
import {ColumnDef, Row} from '@tanstack/react-table';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {DocumentsCardTableColumns} from './tables';

export const DocumentsCard = ({showPEP = false}: {showPEP?: boolean}) => {
    const {
        queue,
        documentsState,
        loadDocuments,
        activeModal,
        setActiveModal,
        activeDocumentIndex,
        setActiveDocumentIndex,
    } = useVerificationDocuments();

    const activeDocument = useMemo(() => {
        if (!documentsState.data?.length) {
            return undefined;
        }

        return documentsState.data[activeDocumentIndex];
    }, [activeDocumentIndex, documentsState.data]);

    const documentsLength = useMemo(
        () => (!!documentsState.data?.length ? documentsState.data.length : null),
        [documentsState.data],
    );

    useEffect(() => {
        if (!documentsState.data && !documentsState.loading) {
            loadDocuments().then();
        }
    }, []);

    return (
        <VerificationCardWrap
            title='Documents'
            Actions={
                <Stack
                    flexDirection='row'
                    flexGrow={1}
                    justifyContent='space-between'
                    alignItems='center'
                    columnGap={2}>
                    {!!documentsLength && (
                        <Chip
                            label={`${activeDocumentIndex + 1}/${documentsLength}`}
                            size='small'
                            variant='outlined'
                        />
                    )}
                    <Stack my={-1} flexDirection='row' flexGrow={1} justifyContent='flex-end' columnGap={2}>
                        {showPEP && (
                            <Button
                                size='small'
                                variant='outlined'
                                title='Upload Documents'
                                color='primary'
                                onClick={() =>
                                    setActiveModal({
                                        type: VerificationDocumentModalType.PEP_Form,
                                    })
                                }>
                                Fill out the PEP Form
                            </Button>
                        )}
                        <Button
                            size='small'
                            variant='contained'
                            title='Upload Documents'
                            color='primary'
                            onClick={() =>
                                setActiveModal({
                                    type: VerificationDocumentModalType.Upload_Documents,
                                })
                            }>
                            Upload Documents
                        </Button>
                    </Stack>
                </Stack>
            }>
            <KasSwitch>
                <KasSwitchWhen condition={!documentsState.data && documentsState.loading}>
                    <DocumentsLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!documentsState.error}>
                    <KasLoadingError
                        error={documentsState.error}
                        view='contained'
                        onTryAgain={loadDocuments}
                    />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!documentsState.data}>
                    {!!documentsState.data?.length ? (
                        <Grid2 container spacing={2}>
                            <Grid2 size={12}>
                                <DocumentCarousel
                                    documents={documentsState.data}
                                    activeIndex={activeDocumentIndex}
                                    onChangeDocument={setActiveDocumentIndex}
                                />
                            </Grid2>
                            {activeDocument && (
                                <>
                                    <Grid2 size={6}>
                                        <DocumentInfo document={activeDocument} />
                                    </Grid2>
                                    <Grid2 size={6}>
                                        <DocumentActions document={activeDocument} />
                                    </Grid2>
                                </>
                            )}
                            <Grid2 size={12}>
                                <TableView<DocumentQueueModel>
                                    data={documentsState.data}
                                    columns={
                                        DocumentsCardTableColumns as ColumnDef<DocumentQueueModel, unknown>[]
                                    }
                                    renderExpand={(row: Row<DocumentQueueModel>) => (
                                        <DocumentComments data={row.original} />
                                    )}
                                />
                            </Grid2>
                        </Grid2>
                    ) : (
                        <NoResultsView text={`No Documents Found for Employee ID#: ${queue.queue_id}`} />
                    )}
                </KasSwitchWhen>
            </KasSwitch>
            {activeModal && <DocumentsModal />}
        </VerificationCardWrap>
    );
};
