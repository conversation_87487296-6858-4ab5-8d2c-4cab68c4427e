import React, {createContext, useContext, useEffect, useRef, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DataStateInterface} from '@/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DocumentQueueModel} from '@/screens/UnderwritingScreen/interfaces';
import {VerificationDocumentModalProps} from './interfaces';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';

interface VerificationDocumentsContextModel {
    queue: VerificationQueueDetailsModel;
    documentsState: DataStateInterface<DocumentQueueModel[]>;
    loadDocuments: () => Promise<void>;
    addDocumentComment: (id: number, text: string) => Promise<void>;
    deleteDocument: (id: number) => Promise<void>;
    activeModal: VerificationDocumentModalProps | null;
    setActiveModal: (value: VerificationDocumentModalProps | null) => void;
    uploadDocument: (body: FormData) => Promise<void>;
    activeDocumentIndex: number;
    setActiveDocumentIndex: (value: number) => void;
}

const VerificationDocumentsContext = createContext<VerificationDocumentsContextModel | undefined>(undefined);

interface VerificationDocumentsProviderProps {
    children: React.ReactNode;
    queue: VerificationQueueDetailsModel;
}

export const VerificationDocumentsProvider = ({children, queue}: VerificationDocumentsProviderProps) => {
    const {showMessage} = useSnackbar();
    const {loadDetails} = useVerificationProfile();
    const abortControllerRef = useRef<AbortController | null>(null);
    const [activeModal, setActiveModal] = useState<VerificationDocumentModalProps | null>(null);
    const [documentsState, setDocumentsState] = useState(getDefaultState<DocumentQueueModel[]>());
    const [activeDocumentIndex, setActiveDocumentIndex] = useState<number>(0);

    const loadDocuments = async () => {
        const url = `/api/secured/verification/${queue.queue_id}/documents?queue=loan`;

        setDocumentsState(getLoadingState(documentsState));
        const response = await apiRequest(url);
        setDocumentsState(getLoadedState(response));
    };

    const onSubmitAction = async (url: string, init?: RequestInit) => {
        const response = await apiRequest(url, init);

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setActiveModal(null);
            await loadDocuments();
            await loadDetails();
        }
    };

    const addDocumentComment = async (id: number, text: string) => {
        const url = `/api/secured/verification/document/${id}/comment`;
        const body = JSON.stringify({
            entity_class: 'Document',
            entity_id: id,
            text,
        });

        await onSubmitAction(url, {
            method: 'post',
            body,
        });
    };

    const deleteDocument = async (id: number) => {
        const url = `/api/secured/verification/document/${id}`;

        await onSubmitAction(url, {
            method: 'delete',
        });
    };

    const uploadDocument = async (body: FormData) => {
        abortControllerRef.current = new AbortController();
        const signal = abortControllerRef.current.signal;
        const url = `/api/secured/verification/document`;

        await onSubmitAction(url, {method: 'post', body, signal});
    };

    useEffect(() => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
    }, [activeModal]);

    const value: VerificationDocumentsContextModel = {
        queue,
        documentsState,
        loadDocuments,
        addDocumentComment,
        deleteDocument,
        activeModal,
        setActiveModal,
        uploadDocument,
        activeDocumentIndex,
        setActiveDocumentIndex,
    };

    return (
        <VerificationDocumentsContext.Provider value={value}>
            {children}
        </VerificationDocumentsContext.Provider>
    );
};

export function useVerificationDocuments() {
    const context = useContext(VerificationDocumentsContext);
    if (!context) {
        throw new Error('useVerificationDocuments must be used within VerificationDocumentsProvider');
    }
    return context;
}
