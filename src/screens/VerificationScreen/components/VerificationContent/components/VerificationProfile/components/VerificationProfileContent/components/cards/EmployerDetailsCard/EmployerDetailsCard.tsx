import React from 'react';
import {Stack} from '@mui/material';
import {KasFlaggedIcon, KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';

export const EmployerDetailsCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Employer Details'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='Employer'>{data.employer_name}</KasInfo>
                {data.primary_employer_id !== data.employer_id && (
                    <KasInfo label='Primary Employer'>{data.primary_employer_name}</KasInfo>
                )}
                <KasInfo label='Loan Guarantee'>
                    <KasFlaggedIcon flagged={data.loan_guarantee} />
                </KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
