import React from 'react';
import {useTheme} from '@mui/material';
import {KasInfo} from '@/components';
import {VerificationQueueSystemMessageModel} from '@/interfaces';

export const SystemMessages = ({messages}: {messages: VerificationQueueSystemMessageModel[]}) => {
    const {palette} = useTheme();

    return (
        <KasInfo label='Underwriting Messages'>
            {messages.map((item, i) => (
                <div key={i}>
                    <span
                        key={i}
                        style={{
                            color: item.critical ? palette.error.main : 'inherit',
                        }}>
                        {item.rule}: {item.message}
                    </span>
                    <br />
                </div>
            ))}
        </KasInfo>
    );
};
