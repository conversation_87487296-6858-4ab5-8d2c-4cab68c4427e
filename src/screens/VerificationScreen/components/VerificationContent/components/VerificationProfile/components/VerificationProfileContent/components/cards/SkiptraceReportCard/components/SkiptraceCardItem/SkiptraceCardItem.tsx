import React, {useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Box, Button, Stack, Typography} from '@mui/material';
import {SkiptraceReportView} from '@/views';
import {SkiptraceReportDetailsTabType, SkiptraceReportModel} from '@/interfaces';
import {KasExpandIcon} from '@/components';
import {useSkiptraceReportCard} from './../../useSkiptraceReportCard';

interface SkiptraceCardItemProps {
    skiptrace: SkiptraceReportModel;
    title: string;
    employeeId: number;
    defaultTab?: SkiptraceReportDetailsTabType;
    expanded?: boolean;
    onPullReport: () => void;
}
export const SkiptraceCardItem = ({
    skiptrace,
    title,
    employeeId,
    defaultTab,
    expanded = false,
    onPullReport,
}: SkiptraceCardItemProps) => {
    const {handleLoadedSkiptraceReport} = useSkiptraceReportCard();
    const [isAccordionOpen, setIsAccordionOpen] = useState(expanded);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
            <AccordionSummary
                component='div'
                sx={{paddingLeft: '8px'}}
                expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                onDoubleClick={handleAccordionChange}>
                <Stack flexDirection='row' alignItems='center' py={1} px={2} columnGap={2}>
                    <Typography variant='subtitle1'>{title}</Typography>
                    <Typography variant='body1'>({skiptrace.report_date})</Typography>
                    <Box my={-1}>
                        <Button onClick={onPullReport} variant='outlined' size='small'>
                            Pull Report
                        </Button>
                    </Box>
                </Stack>
            </AccordionSummary>
            <AccordionDetails>
                <Box px={3} pb={2}>
                    <SkiptraceReportView
                        skiptrace={skiptrace}
                        employeeId={employeeId}
                        defaultTab={defaultTab}
                        onSkiptraceReportLoaded={handleLoadedSkiptraceReport}
                    />
                </Box>
            </AccordionDetails>
        </Accordion>
    );
};
