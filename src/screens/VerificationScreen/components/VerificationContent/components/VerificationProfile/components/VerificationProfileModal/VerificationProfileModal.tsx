import './styles.scss';

import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {VerificationProfileModalType} from './../../interfaces';
import {AddCommentForm, DeclineForm, EditHiredDateForm, PushToQueueForm, SendEmailForm} from './components';

export const VerificationProfileModal = () => {
    const {openVerificationProfileModal, setOpenVerificationProfileModal} = useVerificationProfile();

    const size = useMemo(() => {
        switch (openVerificationProfileModal?.type) {
            case VerificationProfileModalType.EDIT_HIRED_DATE:
                return 'small';
            default:
                return 'medium';
        }
    }, [openVerificationProfileModal?.type]);

    const renderModalContent = useMemo(() => {
        switch (openVerificationProfileModal?.type) {
            case VerificationProfileModalType.ADD_COMMENT:
                return <AddCommentForm />;
            case VerificationProfileModalType.DECLINE:
                return <DeclineForm />;
            case VerificationProfileModalType.PUSH_TO_QUEUE:
                return <PushToQueueForm {...openVerificationProfileModal.props} />;
            case VerificationProfileModalType.SEND_EMAIL:
                return <SendEmailForm {...openVerificationProfileModal.props} />;
            case VerificationProfileModalType.EDIT_HIRED_DATE:
                return <EditHiredDateForm {...openVerificationProfileModal.props} />;
            default:
                return null;
        }
    }, [openVerificationProfileModal?.type]);

    return (
        <KasModal
            title={openVerificationProfileModal?.type || 'Unknown Type'}
            size={size}
            open={!!openVerificationProfileModal}
            onClose={() => setOpenVerificationProfileModal(null)}>
            <div className='kas-verification-profile-modal'>{renderModalContent}</div>
        </KasModal>
    );
};
