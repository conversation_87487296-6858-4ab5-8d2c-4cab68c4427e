import React from 'react';
import {Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';

export const BankInformationCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Bank Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <KasInfo label='Bank'>{data.bank_name}</KasInfo>
                <KasInfo label='Routing Number'>{data.bank_routing_number}</KasInfo>
                <KasInfo label='Account Number'>{data.bank_account_number}</KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
