import React, {PropsWithChildren} from 'react';
import {Tooltip} from '@mui/material';
import {KasCopyText, KasInfo} from '@/components';

interface VerificationInfoProps extends PropsWithChildren {
    label: string;
    tooltip: string;
}

export const VerificationInfo = ({label, tooltip, children}: VerificationInfoProps) => {
    return (
        <KasInfo label={label}>
            <KasCopyText textToCopy={children as string}>
                <Tooltip title={tooltip}>
                    <span>{children}</span>
                </Tooltip>
            </KasCopyText>
        </KasInfo>
    );
};
