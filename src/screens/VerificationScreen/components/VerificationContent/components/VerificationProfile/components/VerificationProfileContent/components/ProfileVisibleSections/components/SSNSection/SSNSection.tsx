import React from 'react';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {DefaultInfoCard} from './../../../cards';
import {VerificationProfileSectionType} from '../../../../../../interfaces';
import {useVerificationProfile} from '../../../../../../../index';

export const SSNSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection} = useVerificationProfile();

    return (
        <div hidden={activeSection !== VerificationProfileSectionType.SSN}>
            <DefaultInfoCard data={data} />
        </div>
    );
};
