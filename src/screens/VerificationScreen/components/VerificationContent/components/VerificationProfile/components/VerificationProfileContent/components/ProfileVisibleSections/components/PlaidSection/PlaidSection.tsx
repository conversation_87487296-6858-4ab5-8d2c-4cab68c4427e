import React from 'react';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {DefaultInfoCard, IncomeInformationCard} from './../../../cards';
import {VerificationProfileSectionType} from '../../../../../../interfaces';
import {useVerificationProfile} from '../../../../../../../index';
import {Grid2} from '@mui/material';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {KasSwitch, KasSwitchWhen} from '@/components';

export const PlaidSection = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeQueue} = useVerification();
    const {activeSection, isEVSReportExist} = useVerificationProfile();
    const hiddenCondition =
        activeSection !== VerificationProfileSectionType.Plaid ||
        (activeQueue === VerificationQueueItemType.Bank && isEVSReportExist);

    return (
        <div hidden={hiddenCondition}>
            <Grid2 container spacing={1}>
                <KasSwitch>
                    <KasSwitchWhen condition={activeQueue === VerificationQueueItemType.Bank}>
                        <Grid2 size={12}>
                            <DefaultInfoCard data={data} />
                        </Grid2>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={activeQueue === VerificationQueueItemType.Retiree_Bank}>
                        {!isEVSReportExist && (
                            <Grid2 size={6}>
                                <DefaultInfoCard data={data} />
                            </Grid2>
                        )}
                        <Grid2 size={isEVSReportExist ? 12 : 6}>
                            <IncomeInformationCard data={data} />
                        </Grid2>
                    </KasSwitchWhen>
                </KasSwitch>
            </Grid2>
        </div>
    );
};
