import React from 'react';
import {Stack} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationCardWrap} from './../index';
import {VerificationInfo} from './../../../components';
import {KasInfo} from '@/components';

export const ContactInformationCard = ({data}: {data: VerificationQueueDetailsModel}) => {
    return (
        <VerificationCardWrap title='Contact Information'>
            <Stack direction='row' flexWrap='wrap' useFlexGap spacing={2}>
                <VerificationInfo label='Phone' tooltip={`Added: ${data.employee_phone_added}`}>
                    {data.employee_phone}
                </VerificationInfo>
                <VerificationInfo label='Email' tooltip={`Added: ${data.employee_email_added}`}>
                    {data.employee_email}
                </VerificationInfo>
                <KasInfo label='Last Originated'>{data.last_originated_date}</KasInfo>
            </Stack>
        </VerificationCardWrap>
    );
};
