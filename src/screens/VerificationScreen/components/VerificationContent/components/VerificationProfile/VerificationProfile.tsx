import './styles.scss';

import React, {useEffect, useRef} from 'react';
import {
    VerificationProfileContent,
    VerificationProfileHead,
    VerificationProfileInfo,
    VerificationProfileModal,
    VerificationProfileSections,
} from './components';
import {Grid2} from '@mui/material';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {KasLoading, KasLoadingBackDrop, KasSwitch, KasSwitchWhen} from '@/components';
import Box from '@mui/material/Box';
import {ErrorView} from '@/views';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {useWindowEventObserver} from '@/hooks/useWindowEventObserver';

export const VerificationProfile = () => {
    const {selectedProfileId} = useVerification();
    const {loadDetails, detailsState, visibleSections, submittingAction, setProfileHeaderHeight} =
        useVerificationProfile();
    const profileHeadContainerRef = useRef<HTMLDivElement | null>(null);

    const updateProfileHeadHeight = () => {
        if (profileHeadContainerRef.current) {
            setProfileHeaderHeight(profileHeadContainerRef.current.offsetHeight);
        }
    };

    useWindowEventObserver('resize', profileHeadContainerRef, updateProfileHeadHeight);

    useEffect(() => {
        loadDetails().then();
    }, [selectedProfileId]);

    return (
        <div className='kas-verification-profile'>
            {submittingAction && <KasLoadingBackDrop />}
            <div className='kas-verification-profile__head' ref={profileHeadContainerRef}>
                <VerificationProfileHead />
            </div>
            <Box mb={2}>{detailsState.data && <VerificationProfileInfo data={detailsState.data} />}</Box>
            <KasSwitch>
                <KasSwitchWhen condition={detailsState.loading && !detailsState.data}>
                    <div className='kas-verification-profile__content'>
                        <KasLoading size={100} />
                    </div>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!detailsState.error}>
                    <ErrorView error={detailsState.error} onTryAgain={loadDetails} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!detailsState.data}>
                    {detailsState.loading && <KasLoadingBackDrop />}
                    <Grid2 container rowSpacing={2}>
                        {visibleSections.length > 0 && (
                            <Grid2 size={12}>
                                <VerificationProfileSections />
                            </Grid2>
                        )}
                        <Grid2 size={12}>
                            <VerificationProfileContent />
                        </Grid2>
                    </Grid2>
                </KasSwitchWhen>
            </KasSwitch>
            <VerificationProfileModal />
        </div>
    );
};
