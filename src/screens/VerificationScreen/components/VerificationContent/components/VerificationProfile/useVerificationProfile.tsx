import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {
    VerificationQueueDetailsModel,
    VerificationQueueDuplicatesModel,
    VerificationQueueModel,
} from '@/interfaces';
import {
    VerificationProfileContextModel,
    VerificationProfileModalProps,
    VerificationProfileSectionType,
    VerificationUserAction,
} from './interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {DEFAULT_SUCCESS_MSG} from '@/constants';
import {
    VerificationQueueItemType,
    VerificationQueueLookingUserModel,
    VerificationQueueSuccessActionModel,
} from '@/screens/VerificationScreen/interfaces';
import {Completable} from '@/interfaces';
import {useAppSelector} from '@/lib/hooks';
import {selectUser} from '@/lib/slices/userSlice';
import {generateUniqueId} from '@/utils/UniqueIdUtils';
import dayjs from 'dayjs';
import {AlertProps} from '@mui/material';
import {useActiveUser} from '@/hooks/useActiveUser';
import {useVerificationProfileTracker} from './hooks/useVerificationProfileTracker';

const VerificationProfileContext = createContext<VerificationProfileContextModel | undefined>(undefined);

const ACTIVE_USER_MS = 5000;
const NON_ENTERPRISE_WORKFLOWS = ['FEDGOV', 'SELF_SERVICE', 'PINWHEEL', 'ATOMIC', 'USPS'];

interface VerificationProfileProviderProps {
    children: React.ReactNode;
    profileId: number;
}

export const VerificationProfileProvider = ({children, profileId}: VerificationProfileProviderProps) => {
    const {showMessage} = useSnackbar();
    const isUserActive = useActiveUser();
    const user = useAppSelector(selectUser);
    const {
        activeQueue,
        changeActiveQueue,
        changeSelectedProfileId,
        loadQueue,
        addLookingUsers,
        handleSuccessAction,
        setProfileLoading,
    } = useVerification();
    const [submittingAction, setSubmittingAction] = useState(false);
    const [detailsState, setDetailsState] = useState(getDefaultState<VerificationQueueDetailsModel>());
    const [openVerificationProfileModal, setOpenVerificationProfileModal] =
        useState<VerificationProfileModalProps | null>(null);
    const [visitedSections, setVisitedSections] = useState<VerificationProfileSectionType[]>([]);
    const [activeSection, setActiveSection] = useState<VerificationProfileSectionType | undefined>();
    const [employeeDuplicateState, setEmployeeDuplicateState] =
        useState(getDefaultState<VerificationQueueDuplicatesModel[]>());
    const [profileHeaderHeight, setProfileHeaderHeight] = useState(0);
    const {trackUserAction} = useVerificationProfileTracker(activeQueue, profileId);

    const isEVSReportExist = useMemo(() => {
        const data = detailsState.data;

        return data ? !!(data.evs_name_match && data.evs_employer_match) : false;
    }, [detailsState.data]);

    const enableVerificationActions = useMemo(() => {
        const condition = !(detailsState.data?.verify_date || detailsState.data?.decline_date);

        return !!detailsState.data && condition;
    }, [detailsState.data]);

    const visibleSections = useMemo(() => {
        if (!detailsState.data) {
            return [];
        }

        switch (activeQueue) {
            case VerificationQueueItemType.Bank:
            case VerificationQueueItemType.Retiree_Bank:
            case VerificationQueueItemType.Primary_Bank:
                const bankSections = [
                    VerificationProfileSectionType.Plaid,
                    VerificationProfileSectionType.Documents,
                ];

                setVisitedSections(bankSections);

                return bankSections;
            case VerificationQueueItemType.Deposit:
                const isNonEnterprise = NON_ENTERPRISE_WORKFLOWS.includes(detailsState.data.workflow);

                return [
                    VerificationProfileSectionType.Documents,
                    VerificationProfileSectionType.SSN,
                    ...(isNonEnterprise ? [VerificationProfileSectionType.Employment] : []),
                    VerificationProfileSectionType.Phone_Number,
                    VerificationProfileSectionType.Address,
                ];
            case VerificationQueueItemType.Address:
            case VerificationQueueItemType.Income:
            case VerificationQueueItemType.Identity:
            case VerificationQueueItemType.Challenge:
            case VerificationQueueItemType.PEP:
            case VerificationQueueItemType.Bankruptcy:
            case VerificationQueueItemType.OFAC:
            case VerificationQueueItemType.Fraud:
            case VerificationQueueItemType.Spouse:
            case VerificationQueueItemType.Retiree_Income:
            default:
                return [];
        }
    }, [activeQueue, detailsState.data]);

    useEffect(() => {
        if (detailsState.data) {
            changeActiveQueue(detailsState.data.queue_type as VerificationQueueItemType, false);
        }
    }, [detailsState.data]);

    const handleAutoVerifiedSection = (sections: VerificationProfileSectionType[]) => {
        const updatedVisitedSections = [
            ...visitedSections,
            ...sections.filter((section) => !visitedSections.includes(section)),
        ];

        setVisitedSections(updatedVisitedSections);
    };

    const loadDetails = async () => {
        const url = `/api/secured/verification/${profileId}`;

        setDetailsState(getLoadingState(detailsState));
        const response = await apiRequest(url);
        setDetailsState(getLoadedState(response));
    };

    const loadEmployeeDuplicates = async (queueId: number) => {
        setEmployeeDuplicateState(getLoadingState(employeeDuplicateState));
        const response = await apiRequest(`/api/secured/verification/${queueId}/duplicates`);

        setEmployeeDuplicateState(getLoadedState(response));
    };

    const changeActiveSection = (value: VerificationProfileSectionType) => {
        if (activeSection) {
            const newVisitedSections = [...visitedSections, activeSection];
            const notVisitedSections = visibleSections.filter(
                (section) => !newVisitedSections.includes(section),
            );

            if (notVisitedSections.length) {
                setActiveSection(notVisitedSections[0]);
            }
            setVisitedSections(newVisitedSections);
        } else {
            setActiveSection(value);
        }
    };

    const handleCompletedAction = async () => {
        const queue: Completable<VerificationQueueModel[]> = await loadQueue();

        if (queue.value && queue.value.length > 0) {
            const profileId = queue.value[0].gid;

            setVisitedSections([]);
            changeSelectedProfileId(profileId);
        } else {
            changeSelectedProfileId(null);
        }
    };

    const pushNotification = (severity: AlertProps['severity'], action: VerificationUserAction) => {
        if (user.value) {
            const getActionMessage = () => {
                switch (action) {
                    case 'approve':
                        return 'approved';
                    case 'push-to-queue':
                        return 'pushed to queue';
                    case 'send-email':
                        return 'sent email';
                    case 'add-comment':
                        return 'added comment';
                    case 'decline':
                        return 'declined';
                    default:
                        return '';
                }
            };
            const data: VerificationQueueSuccessActionModel = {
                id: generateUniqueId(),
                userId: user.value.gid!,
                message: `${user.value.first_name} ${user.value.last_name} has ${getActionMessage()} ${detailsState.data?.employee_name}[${detailsState.data?.employee_id}] from ${activeQueue} queue!`,
                severity,
                queue: activeQueue,
            };

            trackUserAction(action);
            handleSuccessAction(data);
        }
    };

    const approveQueue = async () => {
        setSubmittingAction(true);
        const response = await apiRequest(`/api/secured/verification/${profileId}/approve`, {
            method: 'put',
            body: JSON.stringify('N/A'),
        });

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            pushNotification('success', 'approve');
            await handleCompletedAction();
        }
        setSubmittingAction(false);
    };

    const addCommentQueue = async (
        comment: string,
        snooze: boolean,
        callbacks: Array<() => Promise<void>> = [],
    ) => {
        const body = JSON.stringify({
            comment,
        });
        const response = await apiRequest(
            `/api/secured/verification/${profileId}/add-comment?snooze=${snooze}`,
            {
                method: 'post',
                body,
            },
        );

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            pushNotification('info', 'add-comment');

            if (callbacks.length > 0) {
                try {
                    await Promise.all(callbacks.map((callback) => callback()));
                } catch (err) {
                    showMessage(DEFAULT_SUCCESS_MSG, 'error');
                }
            }
        }
        setSubmittingAction(false);
    };

    const declineQueue = async (comment?: string, updateQueue = false) => {
        setSubmittingAction(true);
        const body = JSON.stringify(comment || 'N/A');
        const response = await apiRequest(`/api/secured/verification/${profileId}/decline`, {
            method: 'delete',
            body,
        });

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            pushNotification('error', 'decline');

            if (updateQueue) {
                setOpenVerificationProfileModal(null);
                await handleCompletedAction();
            }
        }
        setSubmittingAction(false);
    };

    const markBankVerificationReportInactive = async () => {
        const response = await apiRequest(`/api/secured/verification/${profileId}/mark-inactive`, {
            method: 'put',
        });

        if (response.error) {
            showMessage(response.error, 'error');
        }
    };

    const pushToQueue = async (applicationId: number, body: string) => {
        const url = `/api/secured/verification/queue/application/${applicationId}`;
        const response = await apiRequest(url, {method: 'post', body});

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenVerificationProfileModal(null);
            pushNotification('info', 'push-to-queue');
            await loadDetails();
        }
    };

    const sendEmail = async (url: string, body: string, callbacks: Array<() => Promise<void>> = []) => {
        setSubmittingAction(true);
        showMessage('Sending email...', 'info');
        const response = await apiRequest(`${url}&preview=false`, {
            method: 'POST',
            body,
        });

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage('Email sent successfully', 'success');
            setOpenVerificationProfileModal(null);
            pushNotification('info', 'send-email');

            if (callbacks.length > 0) {
                try {
                    await Promise.all(callbacks.map((callback) => callback()));
                } catch (err) {
                    showMessage(DEFAULT_SUCCESS_MSG, 'error');
                }
            }

            await handleCompletedAction();
        }
        setSubmittingAction(false);
    };

    useEffect(() => {
        setActiveSection(visibleSections.length ? visibleSections[0] : undefined);
    }, [profileId]);

    useEffect(() => {
        setProfileLoading(detailsState.loading);
    }, [detailsState.loading]);

    useEffect(() => {
        if (user.value) {
            const data: VerificationQueueLookingUserModel = {
                id: generateUniqueId(),
                userId: user.value.gid!,
                userName: `${user.value.first_name} ${user.value.last_name}`,
                queue: activeQueue,
                profileId,
                entryTime: dayjs(),
                updatedTime: dayjs(),
            };
            const interval = setInterval(() => {
                isUserActive && addLookingUsers({...data, updatedTime: dayjs()}).then();
            }, ACTIVE_USER_MS);

            return () => {
                clearInterval(interval);
            };
        }
    }, [user, isUserActive]);

    const value: VerificationProfileContextModel = {
        profileId,
        activeSection,
        setActiveSection,
        changeActiveSection,
        detailsState,
        loadDetails,
        approveQueue,
        addCommentQueue,
        declineQueue,
        pushToQueue,
        markBankVerificationReportInactive,
        sendEmail,
        openVerificationProfileModal,
        setOpenVerificationProfileModal,
        visitedSections,
        visibleSections,
        submittingAction,
        employeeDuplicateState,
        loadEmployeeDuplicates,
        enableVerificationActions,
        handleAutoVerifiedSection,
        isEVSReportExist,
        profileHeaderHeight,
        setProfileHeaderHeight,
    };

    return (
        <VerificationProfileContext.Provider value={value}>{children}</VerificationProfileContext.Provider>
    );
};

export function useVerificationProfile() {
    const context = useContext(VerificationProfileContext);
    if (!context) {
        throw new Error('useVerificationProfile must be used within VerificationProfileProvider');
    }
    return context;
}
