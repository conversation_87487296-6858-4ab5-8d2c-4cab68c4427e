import {
    DataStateInterface,
    VerificationQueueDetailsModel,
    VerificationQueueDuplicatesModel,
} from '@/interfaces';

export enum VerificationProfileSectionType {
    SSN = 'SSN',
    Employment = 'Employment',
    Phone_Number = 'Phone Number',
    Address = 'Address',
    Plaid = 'Plaid',
    Documents = 'Documents',
}

export interface VerificationProfileSendEmailActionProps {
    id: number;
}

export interface VerificationProfilePushToQueueActionProps {
    applicationId: number;
    source: number;
}

export interface VerificationProfileEditHiredDateActionProps {
    lastHireDate: string | null;
    gid: number;
}

export type VerificationProfileModalProps =
    | {
          type: VerificationProfileModalType.ADD_COMMENT;
      }
    | {
          type: VerificationProfileModalType.DECLINE;
      }
    | {
          type: VerificationProfileModalType.EDIT_HIRED_DATE;
          props: VerificationProfileEditHiredDateActionProps;
      }
    | {
          type: VerificationProfileModalType.SEND_EMAIL;
          props: VerificationProfileSendEmailActionProps;
      }
    | {
          type: VerificationProfileModalType.PUSH_TO_QUEUE;
          props: VerificationProfilePushToQueueActionProps;
      };

export enum VerificationProfileModalType {
    ADD_COMMENT = 'Add Comment',
    DECLINE = 'Enter Comment',
    SEND_EMAIL = 'Email Preview',
    PUSH_TO_QUEUE = 'Push User into Queue',
    EDIT_HIRED_DATE = 'Edit Hired Date',
}

export type VerificationUserAction = 'approve' | 'decline' | 'push-to-queue' | 'send-email' | 'add-comment';

export interface VerificationProfileContextModel {
    profileId: number;
    activeSection: VerificationProfileSectionType | undefined;
    setActiveSection: (value: VerificationProfileSectionType) => void;
    changeActiveSection: (value: VerificationProfileSectionType) => void;
    detailsState: DataStateInterface<VerificationQueueDetailsModel>;
    loadDetails: () => Promise<void>;
    approveQueue: () => Promise<void>;
    addCommentQueue: (
        comment: string,
        snooze: boolean,
        callbacks?: Array<() => Promise<void>>,
    ) => Promise<void>;
    declineQueue: (comment?: string, updateQueue?: boolean) => Promise<void>;
    pushToQueue: (applicationId: number, body: string) => Promise<void>;
    markBankVerificationReportInactive: () => Promise<void>;
    sendEmail: (url: string, body: string, callbacks?: Array<() => Promise<void>>) => Promise<void>;
    openVerificationProfileModal: VerificationProfileModalProps | null;
    setOpenVerificationProfileModal: (value: VerificationProfileModalProps | null) => void;
    visitedSections: VerificationProfileSectionType[];
    visibleSections: VerificationProfileSectionType[];
    submittingAction: boolean;
    employeeDuplicateState: DataStateInterface<VerificationQueueDuplicatesModel[]>;
    loadEmployeeDuplicates: (queueId: number) => Promise<void>;
    enableVerificationActions: boolean;
    handleAutoVerifiedSection: (data: VerificationProfileSectionType[]) => void;
    isEVSReportExist: boolean;
    profileHeaderHeight: number;
    setProfileHeaderHeight: (value: number) => void;
}
