import React, {useMemo} from 'react';
import {Grid2, Paper, Stack, Typography} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {VerificationFilterForm, VerificationOutdated} from './components';
import {VerificationLookingUser} from './../../components';
import {uniqElements} from '@/utils/ArrayUtils';
import {getQueueTitle} from '@/utils/VerificationUtils';

export const VerificationFilter = () => {
    const {activeQueue, lookingUsers} = useVerification();

    const lookingProfileUsers = useMemo(() => {
        const activeQueueLookingUsers = lookingUsers.filter((item) => {
            return item.queue === activeQueue;
        });
        const uniqueKeys = uniqElements(activeQueueLookingUsers, ({userId}) => userId);

        return uniqueKeys.map((key) => {
            return activeQueueLookingUsers.find(({userId}) => userId === key)!;
        });
    }, [lookingUsers]);

    return (
        <Paper elevation={0}>
            <Grid2 container spacing={2} rowSpacing={2} p={3} pt={2}>
                <Grid2 size={12}>
                    <Stack flexDirection='row' alignItems='center' columnGap={2}>
                        <Typography variant='h3'>{getQueueTitle(activeQueue)} Verification</Typography>
                        <VerificationOutdated />
                        <VerificationLookingUser users={lookingProfileUsers} />
                    </Stack>
                </Grid2>
                <Grid2 size={12}>
                    <Typography variant='h6'>Filters</Typography>
                </Grid2>
                <Grid2 size={12}>
                    {Object.entries(VerificationQueueItemType).map(([key, value]) => (
                        <div key={key} hidden={activeQueue !== value}>
                            <VerificationFilterForm queue={value} />
                        </div>
                    ))}
                </Grid2>
            </Grid2>
        </Paper>
    );
};
