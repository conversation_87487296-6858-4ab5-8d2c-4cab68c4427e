import React from 'react';
import {Stack, Tooltip, Typography} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {KasLoadingStatusIcon} from '@/components';
import IconButton from '@mui/material/IconButton';
import {Refresh} from '@mui/icons-material';

export const VerificationOutdated = () => {
    const {activeQueue, queueOutdatedState, loadQueue, queueState} = useVerification();

    if (!queueOutdatedState[activeQueue].length) {
        return null;
    }

    return (
        <Stack flexDirection='row' alignItems='center' columnGap={1} my={-1}>
            <Tooltip title={queueOutdatedState[activeQueue].join(', ')}>
                <Typography variant='body1' sx={{cursor: 'help'}}>
                    The data is outdated!
                </Typography>
            </Tooltip>
            {queueState[activeQueue].loading ? (
                <KasLoadingStatusIcon loading={queueState[activeQueue].loading} />
            ) : (
                <IconButton title='Refresh' onClick={() => loadQueue()}>
                    <Refresh />
                </IconButton>
            )}
        </Stack>
    );
};
