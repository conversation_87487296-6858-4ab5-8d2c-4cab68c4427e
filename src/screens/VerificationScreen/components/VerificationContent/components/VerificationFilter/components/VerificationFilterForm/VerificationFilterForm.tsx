import React, {useEffect, useMemo} from 'react';
import {Button, Checkbox, FormControlLabel, Grid2, Stack} from '@mui/material';
import {KasSelect} from '@/components';
import MenuItem from '@mui/material/MenuItem';
import {VERIFICATION_PERIOD_LIST} from '@/screens/VerificationScreen/data';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {
    VerificationQueueItemType,
    VerificationQueueParamsModel,
} from '@/screens/VerificationScreen/interfaces';
import {useSecured} from '@/hooks/useSecured';

interface VerificationFilterFormProps {
    queue: VerificationQueueItemType;
}

export const VerificationFilterForm = ({queue}: VerificationFilterFormProps) => {
    const {hasAnyDesignation} = useSecured();
    const {loadQueue, activeQueue, queueState, queueParams} = useVerification();

    const loading = useMemo(() => activeQueue === queue && queueState[queue].loading, [queueState]);

    const onSubmit = async (values: VerificationQueueParamsModel) => {
        await loadQueue(queue, values);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: queueParams[queue],
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (queue === activeQueue && !queueState[activeQueue].loading && !queueState[activeQueue].error) {
            formik.submitForm().then();
        }
    }, [activeQueue]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={3}>
                    <KasSelect
                        disabled={loading}
                        value={formik.values.lookup}
                        label='Period'
                        name='lookup'
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}>
                        {VERIFICATION_PERIOD_LIST.map(({id, value, label}) => (
                            <MenuItem key={id} value={value}>
                                {label}
                            </MenuItem>
                        ))}
                    </KasSelect>
                </Grid2>
                <Grid2 size={9}>
                    <Stack flexDirection='row' alignItems='center' columnGap={2} pl={2}>
                        <Stack flexDirection='row' useFlexGap flexWrap='wrap' columnGap={2} my={-1}>
                            <FormControlLabel
                                disabled={loading}
                                control={
                                    <Checkbox
                                        name='verified'
                                        size='small'
                                        sx={{height: 28}}
                                        checked={formik.values.verified}
                                        onChange={formik.handleChange}
                                    />
                                }
                                label='Include Verified'
                            />
                            <FormControlLabel
                                disabled={loading}
                                control={
                                    <Checkbox
                                        name='denied'
                                        size='small'
                                        sx={{height: 28}}
                                        checked={formik.values.denied}
                                        onChange={formik.handleChange}
                                    />
                                }
                                label='Include Declined'
                            />
                            <FormControlLabel
                                disabled={loading}
                                control={
                                    <Checkbox
                                        name='followup'
                                        size='small'
                                        sx={{height: 28}}
                                        checked={formik.values.followup}
                                        onChange={formik.handleChange}
                                    />
                                }
                                label='Include Support Follow-up'
                            />
                            {activeQueue !== VerificationQueueItemType.PEP && (
                                <FormControlLabel
                                    disabled={loading}
                                    control={
                                        <Checkbox
                                            name='docs'
                                            size='small'
                                            sx={{height: 28}}
                                            checked={formik.values.docs}
                                            onChange={formik.handleChange}
                                        />
                                    }
                                    label='Documents Uploaded'
                                />
                            )}
                            {activeQueue === VerificationQueueItemType.Bank &&
                                hasAnyDesignation(['MANAGER']) && (
                                    <FormControlLabel
                                        disabled={loading}
                                        control={
                                            <Checkbox
                                                name='strict'
                                                size='small'
                                                sx={{height: 28}}
                                                checked={formik.values.strict}
                                                onChange={formik.handleChange}
                                            />
                                        }
                                        label='Primary Bank'
                                    />
                                )}
                        </Stack>
                        <Button type='submit' variant='outlined' disabled={loading}>
                            APPLY
                        </Button>
                    </Stack>
                </Grid2>
            </Grid2>
        </form>
    );
};
