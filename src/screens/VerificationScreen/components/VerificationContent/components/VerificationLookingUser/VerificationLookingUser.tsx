import React from 'react';
import {Badge, Tooltip} from '@mui/material';
import {VerificationQueueLookingUserModel} from '@/screens/VerificationScreen/interfaces';
import {PersonSearch} from '@mui/icons-material';
import {getLookingProfileUsersDescription} from '@/utils/VerificationUtils';

export const VerificationLookingUser = ({users}: {users: VerificationQueueLookingUserModel[]}) => {
    if (!users.length) {
        return null;
    }

    return (
        <Tooltip title={getLookingProfileUsersDescription(users)}>
            <Badge badgeContent={users.length} color='info'>
                <PersonSearch color='action' />
            </Badge>
        </Tooltip>
    );
};
