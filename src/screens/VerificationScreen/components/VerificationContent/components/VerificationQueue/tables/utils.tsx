import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {AmountCell} from '@/components/table/cells';
import React from 'react';
import {KasStrike} from '@/components';
import {VerificationQueueExpandedModel} from '@/interfaces';

export const _columnHelper = createColumnHelper<VerificationQueueExpandedModel>();

export const _defaultInfoColumn = defaultInfoColumn<VerificationQueueExpandedModel>;

export const _strikeContent = (props: CellContext<VerificationQueueExpandedModel, string>) => (
    <KasStrike
        key={`${props.column.id}-${props.row.original.gid}`}
        isStrike={props.row.original.archived || !!props.row.original.verify_time}>
        {props.getValue()}
    </KasStrike>
);

export const _strikeAmountContent = (props: CellContext<VerificationQueueExpandedModel, string>) => (
    <KasStrike
        key={`${props.column.id}-${props.row.original.gid}`}
        isStrike={props.row.original.archived || !!props.row.original.verify_time}>
        <AmountCell data={Number(props.getValue() || 0)} />
    </KasStrike>
);
