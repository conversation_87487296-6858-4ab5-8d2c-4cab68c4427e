import {CellContext} from '@tanstack/react-table';
import React from 'react';
import {KasUnderwritingSharedLink} from '@/components';
import {LookingUsersCell, OpenProfileActionCell} from './../components';
import {VerificationQueueExpandedModel} from '@/interfaces';
import {_defaultInfoColumn, _strikeAmountContent, _strikeContent, _columnHelper} from './utils';

export const VerificationAmountTableColumns = [
    _columnHelper.accessor('employeeTableName', {
        id: 'employeeTableName',
        header: 'Employee',
        cell: (props) => {
            const {employee_id, employee_name} = props.row.original;

            return (
                <>
                    {employee_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
    }),
    _defaultInfoColumn('employer', 'Employer', undefined, _strikeContent),
    _defaultInfoColumn('loan_amount', 'Amount', undefined, _strikeAmountContent),
    _defaultInfoColumn('state', 'State', undefined, _strikeContent),
    _defaultInfoColumn('last_updated_time', 'Updated', undefined, _strikeContent),
    _defaultInfoColumn('verify_time', 'Verified', undefined, _strikeContent),
    _columnHelper.accessor('lookingUsers', {
        id: 'lookingUsers',
        header: 'Agents',
        cell: (props) => <LookingUsersCell data={props.getValue()} />,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<VerificationQueueExpandedModel, string>) => (
            <OpenProfileActionCell profileId={props.row.original.gid} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
