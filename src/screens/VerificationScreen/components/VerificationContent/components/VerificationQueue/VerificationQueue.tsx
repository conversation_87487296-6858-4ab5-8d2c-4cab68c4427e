import './styles.scss';

import React, {useMemo} from 'react';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {KasDesignedTable, KasLoading, KasLoadingBackDrop, KasSwitch, KasSwitchWhen} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {
    VerificationTableColumns,
    VerificationAmountTableColumns,
    VerificationBankTableColumns,
} from './tables';
import {VerificationQueueExpandedModel, VerificationQueueModel} from '@/interfaces';
import {ErrorView} from '@/views';
import Box from '@mui/material/Box';
import {useSecured} from '@/hooks/useSecured';

export const VerificationQueue = () => {
    const {hasAnyDesignation} = useSecured();
    const {activeQueue, queueState, lookingUsers, changeQueueTableState} = useVerification();

    const columns = useMemo(() => {
        switch (activeQueue) {
            case VerificationQueueItemType.Income:
            case VerificationQueueItemType.Deposit:
                return VerificationAmountTableColumns;
            case VerificationQueueItemType.Bank:
                if (hasAnyDesignation(['MANAGER'])) {
                    return VerificationBankTableColumns;
                }
                return VerificationTableColumns;
            default:
                return VerificationTableColumns;
        }
    }, [activeQueue]);

    const data: VerificationQueueExpandedModel[] = useMemo(() => {
        const curData = queueState[activeQueue].data || [];

        return curData.map((item) => {
            const curLookingUsers = lookingUsers.filter((user) => {
                return user.queue === activeQueue && user.profileId === item.gid;
            });
            return {
                ...item,
                lookingUsers: curLookingUsers,
                employeeTableName: `${item.employee_name} [${item.employee_id}]`,
            };
        });
    }, [lookingUsers, queueState[activeQueue].data, activeQueue]);

    return (
        <div className='kas-verification-queue'>
            <KasSwitch>
                <KasSwitchWhen condition={!queueState[activeQueue].data && queueState[activeQueue].loading}>
                    <div className='kas-verification-queue__loading'>
                        <KasLoading size={100} />
                    </div>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!queueState[activeQueue].error}>
                    <Box py={2}>
                        <ErrorView error={queueState[activeQueue].error} />
                    </Box>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!queueState[activeQueue].data}>
                    {queueState[activeQueue].loading && <KasLoadingBackDrop />}
                    <KasDesignedTable
                        withSearch={true}
                        columns={columns as ColumnDef<VerificationQueueModel, unknown>[]}
                        data={data}
                        sortingColumns={[{id: 'last_updated_time', desc: true}]}
                        onTableSortChange={(value) => {
                            changeQueueTableState(value);
                        }}
                    />
                </KasSwitchWhen>
            </KasSwitch>
        </div>
    );
};
