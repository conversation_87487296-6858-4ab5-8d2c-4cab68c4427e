import React from 'react';
import {Chip} from '@mui/material';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {VerificationQueueExpandedModel} from '@/interfaces';
import {capitalizeWords} from '@/utils/TextUtils';

export const CodeCell = ({data}: {data: VerificationQueueExpandedModel}) => {
    const label = capitalizeWords(data.code.replace('_', ' '));
    const color = data.code === VerificationQueueItemType.Primary_Bank ? 'primary' : 'default';

    return <Chip label={label} size='small' color={color} variant='outlined' />;
};
