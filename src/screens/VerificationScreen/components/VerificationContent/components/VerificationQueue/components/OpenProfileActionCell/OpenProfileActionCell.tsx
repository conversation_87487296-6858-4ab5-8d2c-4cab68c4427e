import React from 'react';
import {CallMade} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {useVerification} from '@/screens/VerificationScreen/useVerification';

interface OpenProfileActionCellProps {
    profileId: number;
}

export const OpenProfileActionCell = ({profileId}: OpenProfileActionCellProps) => {
    const {changeSelectedProfileId} = useVerification();

    return (
        <ActionCell
            Icon={<CallMade color='primary' titleAccess='Show Details' />}
            onClick={() => changeSelectedProfileId(profileId)}
        />
    );
};
