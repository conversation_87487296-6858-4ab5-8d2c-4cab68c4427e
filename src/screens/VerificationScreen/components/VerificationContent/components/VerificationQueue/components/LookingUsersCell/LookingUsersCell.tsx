import React from 'react';
import {Visibility} from '@mui/icons-material';
import {Chip, Tooltip} from '@mui/material';
import {VerificationQueueLookingUserModel} from '@/screens/VerificationScreen/interfaces';

export const LookingUsersCell = ({data}: {data: VerificationQueueLookingUserModel[]}) => {
    const title = data
        .map((item) => {
            return item.userName.trim() ? item.userName : item.userId;
        })
        .join(', ');

    return data.length ? (
        <Tooltip title={title}>
            <Chip icon={<Visibility fontSize='small' />} label={data.length} size='small' color='primary' />
        </Tooltip>
    ) : null;
};
