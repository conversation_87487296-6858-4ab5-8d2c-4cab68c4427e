import React from 'react';
import {Button, Stack, Typography} from '@mui/material';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {SkipNext, SkipPrevious} from '@mui/icons-material';
import {useSecured} from '@/hooks/useSecured';

export const VerificationHead = () => {
    const {hasAnyRole} = useSecured();
    const {changeSelectedProfileId, prevUser, nextUser, selectedProfileId, profileLoading} =
        useVerification();
    const showNavigation = hasAnyRole(['KASH_POWERUSER']) && selectedProfileId;

    const goToPrevUser = () => {
        if (prevUser) {
            changeSelectedProfileId(prevUser.gid);
        }
    };

    const goToNextUser = () => {
        if (nextUser) {
            changeSelectedProfileId(nextUser.gid);
        }
    };

    return (
        <Stack direction='row' alignItems='center' justifyContent='space-between' spacing={2}>
            <Typography variant='h3' py={2.5}>
                Verification Dashboard
            </Typography>
            {showNavigation && (
                <Stack direction='row' spacing={1}>
                    <Button
                        variant='outlined'
                        size='small'
                        title={prevUser?.employee_name || ''}
                        disabled={!prevUser || profileLoading}
                        sx={{minHeight: 20}}
                        onClick={goToPrevUser}>
                        <SkipPrevious fontSize='small' />
                    </Button>
                    <Button
                        variant='outlined'
                        size='small'
                        title={nextUser?.employee_name || ''}
                        disabled={!nextUser || profileLoading}
                        sx={{minHeight: 20}}
                        onClick={goToNextUser}>
                        <SkipNext fontSize='small' />
                    </Button>
                </Stack>
            )}
        </Stack>
    );
};
