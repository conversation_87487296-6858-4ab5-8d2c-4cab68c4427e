import React, {useEffect, useRef} from 'react';
import {VerificationQueueSuccessActionModel} from './../../interfaces';
import {Alert, Snackbar, Stack} from '@mui/material';
import {useThemeMode} from '@/hooks/useThemeMode';

interface VerificationSnackbarProps {
    data: VerificationQueueSuccessActionModel[];
    handleClose: (id: string) => void;
}

export const VerificationSnackbar = ({data, handleClose}: VerificationSnackbarProps) => {
    return (
        <Snackbar open={!!data.length} anchorOrigin={{vertical: 'top', horizontal: 'right'}}>
            <Stack flexDirection='column' rowGap={1}>
                {data.map((snackbar) => (
                    <VerificationSnackbarAlert
                        key={snackbar.id}
                        snackbar={snackbar}
                        onClose={() => handleClose(snackbar.id)}
                    />
                ))}
            </Stack>
        </Snackbar>
    );
};

interface VerificationSnackbarAlertProps {
    snackbar: VerificationQueueSuccessActionModel;
    onClose: () => void;
}

export const VerificationSnackbarAlert = ({snackbar, onClose}: VerificationSnackbarAlertProps) => {
    const {snackbarHideDuration} = useThemeMode();
    const startTime = useRef<number>(Date.now());
    const delayTime = useRef<number>(snackbarHideDuration);
    const alertTimer = useRef<NodeJS.Timeout>(setTimeout(onClose, delayTime.current));

    const onStopTimer = () => {
        clearTimeout(alertTimer.current);

        const elapsedTime = Date.now() - startTime.current;

        delayTime.current = delayTime.current - elapsedTime;
    };

    const onResumeTimer = () => {
        clearTimeout(alertTimer.current);
        startTime.current = Date.now();
        alertTimer.current = setTimeout(onClose, delayTime.current > 0 ? delayTime.current : 0);
    };

    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.hidden) {
                onStopTimer();
            } else {
                onResumeTimer();
            }
        };

        handleVisibilityChange();

        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            clearTimeout(alertTimer.current);
        };
    }, []);

    return (
        <Alert onClose={onClose} severity={snackbar.severity} variant='filled' sx={{width: '100%'}}>
            {snackbar.message}
        </Alert>
    );
};
