import './styles.scss';

import {
    KasTablePagination,
    KasTableHeadActions,
    KasTableColumnManagement,
    KasTableContent,
    KasTablePullReports,
} from '@/components/table';
import React, {PropsWithChildren} from 'react';
import {useTable} from '@/hooks/useTable';
import {KasTableProps} from '@/components/KasTable/index';
import {Button, ButtonGroup, Stack} from '@mui/material';
import {PaginationState} from '@tanstack/react-table';
import Box from '@mui/material/Box';

export enum KasComparisonTableView {
    Totals = 'Totals',
    Columns = 'Columns',
    Rows = 'Rows',
    Merged = 'Merged',
}

const TABLE_VIEW_BUTTONS = [
    KasComparisonTableView.Totals,
    KasComparisonTableView.Columns,
    KasComparisonTableView.Rows,
    KasComparisonTableView.Merged,
];

interface KasComparisonTableProps<T> extends KasTableProps<T>, PropsWithChildren {
    activeView: KasComparisonTableView;
    onChangeView: (value: KasComparisonTableView) => void;
}

export const KasComparisonTable = <T,>({
    columns,
    data,
    renderExpand,
    activeView,
    onChangeView,
    children,
}: KasComparisonTableProps<T>) => {
    const {table, setPagination} = useTable<T>(data || [], columns);

    const handleButtonClick = (value: KasComparisonTableView) => {
        onChangeView(value);
    };

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    return (
        <div className='kas-table'>
            <KasTableHeadActions<T> table={table}>
                <Stack direction='row' spacing={1}>
                    <KasTablePullReports table={table} tableName='Comparison' />
                    <Box px={2}>
                        <ButtonGroup>
                            {TABLE_VIEW_BUTTONS.map((item) => (
                                <Button
                                    key={item}
                                    variant={activeView === item ? 'contained' : 'text'}
                                    onClick={() => handleButtonClick(item)}>
                                    {item}
                                </Button>
                            ))}
                        </ButtonGroup>
                    </Box>
                    {children}
                </Stack>
            </KasTableHeadActions>
            {activeView === KasComparisonTableView.Columns && <KasTableColumnManagement<T> table={table} />}
            <KasTableContent table={table} renderExpand={renderExpand} />
            <KasTablePagination table={table} onChange={handleChange} />
        </div>
    );
};
