import './styles.scss';

import {KasTableHeadActions} from '@/components/table/KasTableHeadActions/KasTableHeadActions';
import {KasTableContent} from '@/components/table/KasTableContent/KasTableContent';
import {KasTablePagination, KasTablePullReports} from '@/components/table';
import React from 'react';
import {useTable} from '@/hooks/useTable';
import {KasTableProps} from '@/components/KasTable/index';
import {Stack} from '@mui/material';
import {PaginationState} from '@tanstack/react-table';

interface KasDefaultTableProps<T> extends KasTableProps<T> {
    children?: React.ReactNode;
}

export const KasDefaultTable = <T,>({
    columns,
    data,
    renderExpand,
    sortingColumns = [],
    tableName = 'Table',
    excelColumnFormats,
    children,
}: KasDefaultTableProps<T>) => {
    const {table, setPagination} = useTable<T>(data || [], columns, sortingColumns);

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    return (
        <div className='kas-table'>
            <KasTableHeadActions<T> table={table}>
                <Stack direction='row' spacing={1}>
                    <KasTablePullReports
                        table={table}
                        tableName={tableName}
                        excelColumnFormats={excelColumnFormats}
                    />
                    {children && children}
                </Stack>
            </KasTableHeadActions>
            <KasTableContent table={table} renderExpand={renderExpand} />
            <KasTablePagination table={table} onChange={handleChange} />
        </div>
    );
};
