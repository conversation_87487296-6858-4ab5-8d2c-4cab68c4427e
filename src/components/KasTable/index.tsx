import {ColumnDef, Row, SortingState, Table} from '@tanstack/react-table';
import React from 'react';
import {ExcelColumnFormatModel} from '@/interfaces';

export interface KasTableProps<T> {
    columns: ColumnDef<T>[];
    data: T[] | null;
    renderExpand?: (row: Row<T>) => React.ReactNode;
    sortingColumns?: SortingState;
    onTableStateChange?: (rows: Row<T>[]) => void;
    onTableInit?: (table: Table<T>) => void;
    onTableSortChange?: (rows: Array<T>) => void;
    tableName?: string;
    excelColumnFormats?: ExcelColumnFormatModel[];
}

export {KasDefaultTable} from './KasDefaultTable';
export {KasPureTable} from './KasPureTable';
export {KasComparisonTable} from './KasComparisonTable';
export {KasDesignedTable} from './KasDesignedTable';
