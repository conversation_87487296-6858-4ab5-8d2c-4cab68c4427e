import './styles.scss';

import {KasTableHeadActions} from '@/components/table/KasTableHeadActions/KasTableHeadActions';
import {KasTableContent} from '@/components/table/KasTableContent/KasTableContent';
import {KasTablePagination} from '@/components/table';
import React, {ReactNode, useEffect} from 'react';
import {useTable} from '@/hooks/useTable';
import {KasTableProps} from '@/components/KasTable/index';
import {PaginationState} from '@tanstack/react-table';

interface KasDesignedTableProps<T> extends KasTableProps<T> {
    withSearch?: boolean;
    children?: ReactNode;
}

export const KasDesignedTable = <T,>({
    columns,
    data,
    renderExpand,
    sortingColumns = [],
    onTableSortChange,
    withSearch = false,
    children,
}: KasDesignedTableProps<T>) => {
    const {table, sortedData, setPagination} = useTable<T>(data || [], columns, sortingColumns);

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    useEffect(() => {
        if (onTableSortChange) {
            onTableSortChange(sortedData);
        }
    }, [sortedData]);

    return (
        <div className='kas-designed-table'>
            {withSearch && <KasTableHeadActions<T> table={table}>{children}</KasTableHeadActions>}
            <KasTableContent table={table} renderExpand={renderExpand} />
            <KasTablePagination table={table} onChange={handleChange} />
        </div>
    );
};
