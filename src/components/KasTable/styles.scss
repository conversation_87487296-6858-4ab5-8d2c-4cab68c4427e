$cell-padding-base: var(--table-cell-spacing);
$cell-padding: $cell-padding-base calc(2 * $cell-padding-base);

@mixin default-cell {
  height: 32px;
  border-bottom: 1px solid var(--color-divider);
  border-left: 1px solid var(--color-divider);
  padding: $cell-padding;

  &:first-child {
    border-left: none;
  }
}

@mixin default-head-footer-cell {
  &:empty {
    border-left-color: transparent;
  }

  &:not(:empty) + th:empty {
    border-left: 1px solid var(--color-divider);
  }
}

.kas-table {

  .kas-table-column-management {
    margin-bottom: 16px;
  }

  .kas-table-content {
    table {
      border-spacing: 0;
      width: 100%;
      border: 1px solid var(--color-divider);
      border-bottom: none;
      font-size: var(--table-text-size);
      background: var(--background-paper);
      border-radius: var(--border-radius);
      text-align: left;

      tbody {
        tr {
          &.odd {
            background-color: var(--color-grey);
          }
        }

        td {
          @include default-cell();
          word-wrap: break-word;
          word-break: break-word;

          &.expander {
            padding: 0;
            width: 40px;
            text-align: center;
          }

          a,
          a:visited {
            font-size: inherit;
          }

          .action-cell {
            margin-top: -4px;
            margin-bottom: -4px;
          }
        }
      }

      thead {
        th {
          @include default-cell();
          @include default-head-footer-cell();

          &[colspan='1'] {
            text-align: inherit;

            > div {
              justify-content: flex-start;
            }
          }

          > div {
            display: flex;
            align-items: center;
          }
        }
      }

      tfoot {

        th {
          @include default-cell();
          @include default-head-footer-cell();
        }
      }
    }
  }
}

@mixin designed-cell {
  padding: 12px 16px;
  border-top: 1px solid var(--color-divider);
  border-bottom: 1px solid var(--color-divider);

  &:first-child {
    border-radius: 4px 0 0 4px;
    border-left: 1px solid var(--color-divider);
  }

  &:last-child {
    border-radius: 0 4px 4px 0;
    border-right: 1px solid var(--color-divider);
  }

}

@mixin designed-head-footer-cell {
  padding: 8px 16px;
  white-space: nowrap;
  color: var(--color-disabled);
}

.kas-designed-table {

  &.clickable {
    .kas-table-content {
      table {
        tbody {
          tr {
            cursor: pointer;

            &:hover {
              td {
                background-color: rgba(29, 158, 213, 0.1);
              }
            }
          }
        }
      }
    }
  }

  .kas-table-content {
    table {
      border-spacing: 0 8px;
      width: 100%;
      font-size: var(--table-text-size);
      text-align: left;

      tbody {
        tr {
          background-color: var(--background-paper);

          &.active-row {

            td {
              border-color: var(--color-primary);
              background-color: rgba(29, 158, 213, 0.1);
            }
          }
        }

        td {
          @include designed-cell();
          word-wrap: break-word;
          word-break: break-word;

          a,
          a:visited {
            font-size: inherit;
          }
        }
      }

      thead {
        th {
          @include designed-head-footer-cell();

          &[colspan='1'] {
            text-align: inherit;

            > div {
              justify-content: flex-start;
            }
          }

          > div {
            display: flex;
            align-items: center;
          }
        }
      }

      tfoot {

        th {
          @include designed-head-footer-cell();
        }
      }
    }
  }
}

