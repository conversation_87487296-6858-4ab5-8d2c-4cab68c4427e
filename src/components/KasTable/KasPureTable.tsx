import './styles.scss';

import {KasTableContent} from '@/components/table/KasTableContent/KasTableContent';
import {KasTablePagination} from '@/components/table';
import React, {useEffect} from 'react';
import {useTable} from '@/hooks/useTable';
import {KasTableProps} from '@/components/KasTable/index';
import {PaginationState} from '@tanstack/react-table';

export const KasPureTable = <T,>({
    columns,
    data,
    renderExpand,
    sortingColumns = [],
    onTableStateChange,
    onTableInit,
}: KasTableProps<T>) => {
    const {table, setPagination} = useTable<T>(data || [], columns, sortingColumns);
    const state = table.getState().pagination;

    useEffect(() => {
        if (onTableInit) {
            onTableInit(table);
        }
    }, [onTableInit, table]);

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    useEffect(() => {
        if (state && onTableStateChange) {
            onTableStateChange(table.getRowModel().rows);
        }
    }, [onTableStateChange, state]);

    return (
        <div className='kas-table'>
            <KasTableContent table={table} renderExpand={renderExpand} />
            <KasTablePagination table={table} onChange={handleChange} />
        </div>
    );
};
