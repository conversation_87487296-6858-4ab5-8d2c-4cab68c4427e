import React, {PropsWithChildren} from 'react';
import {Paper, Typography} from '@mui/material';
import Box from '@mui/material/Box';
import {KasLink} from '@/components';
import {DropzoneOptions, useDropzone} from 'react-dropzone';

interface SelectFileDropzoneProps extends PropsWithChildren {
    options?: DropzoneOptions;
}

export const KasFileDropzone = ({options, children}: SelectFileDropzoneProps) => {
    const {open, getRootProps, getInputProps, isDragActive} = useDropzone({
        noClick: true,
        ...options,
    });

    return (
        <Paper elevation={0}>
            <Box
                p={4}
                {...getRootProps()}
                sx={{
                    textAlign: 'center',
                    backgroundColor: isDragActive ? '#f0f0f0' : 'var(--background-paper)',
                }}>
                <input {...getInputProps()} />
                {children}
                <Box pt={3}>
                    <Typography variant='body1'>
                        <KasLink onClick={open}>Upload from computer</KasLink> of Drag and Drop file here
                    </Typography>
                </Box>
            </Box>
        </Paper>
    );
};
