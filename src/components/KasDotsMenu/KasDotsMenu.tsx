import './styles.scss';

import React, {ReactNode, useCallback, useState} from 'react';
import {MoreVert} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import MenuItem from '@mui/material/MenuItem';
import Menu from '@mui/material/Menu';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';
import {Typography} from '@mui/material';
import {TypographyOwnProps} from '@mui/material/Typography/Typography';

export interface KasDotsMenuItemProps {
    ContentComponent: ReactNode;
    disabled?: boolean;
    onClick: () => void;
}

interface KasDotsMenuProps extends TestableProps {
    menuItems: KasDotsMenuItemProps[];
    disabled?: boolean;
}

export const KasDotsMenu = ({menuItems, disabled = false, testid}: KasDotsMenuProps) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const color: 'disabled' | 'action' = disabled || !menuItems.length ? 'disabled' : 'action';

    const renderMenuItem = useCallback(
        ({ContentComponent, disabled = false, onClick}: KasDotsMenuItemProps, index: number) => (
            <MenuItem
                key={index}
                disabled={disabled}
                onClick={() => {
                    onClick();
                    setAnchorEl(null);
                }}>
                {ContentComponent}
            </MenuItem>
        ),
        [menuItems],
    );

    return (
        <div className='kas-dots-menu' data-testid={`${testid}-dotsmenu`}>
            <IconButton
                aria-controls='menu-appbar'
                aria-haspopup='true'
                aria-expanded={open ? 'true' : undefined}
                disabled={disabled || !menuItems.length}
                onClick={(event) => {
                    event.stopPropagation();
                    setAnchorEl(event.currentTarget);
                }}>
                <MoreVert color={color} />
            </IconButton>
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={() => setAnchorEl(null)}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}>
                {menuItems.map(renderMenuItem)}
            </Menu>
        </div>
    );
};

interface KasDotsMenuSingleProps extends TestableProps {
    title: string;
    disabled?: boolean;
    color?: TypographyOwnProps['color'];
    onClick: () => void;
}

export const KasDotsMenuSingle = ({title, color, onClick, ...rest}: KasDotsMenuSingleProps) => {
    const menuItems = [
        {
            ContentComponent: (
                <Typography variant='body1' color={color}>
                    {title}
                </Typography>
            ),
            onClick: onClick,
        },
    ];

    return <KasDotsMenu {...rest} menuItems={menuItems} />;
};
