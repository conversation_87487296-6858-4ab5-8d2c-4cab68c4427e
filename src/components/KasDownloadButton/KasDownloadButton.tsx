import React from 'react';
import {Button, CircularProgress} from '@mui/material';
import ArrowCircleDownIcon from '@mui/icons-material/ArrowCircleDown';

interface KasDownloadButtonProps {
    submitting: boolean;
    disabled?: boolean;
}

export const KasDownloadButton = ({submitting, disabled = false}: KasDownloadButtonProps) => {
    return (
        <Button fullWidth variant='contained' type='submit' disabled={disabled}>
            {submitting ? <CircularProgress size={20} /> : <ArrowCircleDownIcon fontSize='small' />}
            &nbsp;&nbsp;Download
        </Button>
    );
};
