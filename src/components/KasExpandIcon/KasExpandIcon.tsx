import './styles.scss';

import React from 'react';
import {ArrowDropDown} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import { TestableProps } from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasExpandIconProps extends TestableProps {
    expanded: boolean;
    onClick: () => void;
}

export const KasExpandIcon = ({expanded, onClick, testid}: KasExpandIconProps) => (
    <div className='kas-expand-icon' data-testid={`${testid}-expand-icon`}>
        {expanded ? (
            <IconButton onClick={onClick}>
                <ArrowDropDown />
            </IconButton>
        ) : (
            <IconButton disabled>
                <ArrowDropDown />
            </IconButton>
        )}
    </div>
);
