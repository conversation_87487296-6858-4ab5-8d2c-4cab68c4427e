import React from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {AddressDTO} from '@/models';
import {useGoogleMapsScript} from '@/hooks/useGoogleMapsScript';
import {usePlacesAutocompleteWithDetails} from '@/hooks/usePlaceAutocompleteDetails';
import {FormikValues} from 'formik';

interface KasGooglePlacesAutocompleteProps {
    name: string;
    formik: FormikValues;
    label?: string;
    onSelect: (result: AddressDTO) => void;
}

export const KasGooglePlacesAutocomplete = (props: KasGooglePlacesAutocompleteProps) => {
    const {isLoaded, loadError} = useGoogleMapsScript();

    if (loadError) {
        return <div>Error loading maps</div>;
    }

    if (!isLoaded) {
        return <div>Loading...</div>;
    }

    return <GooglePlacesAutocompleteField {...props} />;
};

const GooglePlacesAutocompleteField = ({
    name,
    formik,
    label = 'Search places',
    onSelect,
}: KasGooglePlacesAutocompleteProps) => {
    const {ready, suggestionsStatus, suggestionsData, setValue, handleOptionSelect} =
        usePlacesAutocompleteWithDetails(onSelect);

    const handleInputChange = (event: React.ChangeEvent<{}>, newInputValue: string) => {
        setValue(newInputValue);
        formik.setFieldValue(name, newInputValue);
    };

    return (
        <>
            <Autocomplete
                freeSolo
                value={formik.values[name]}
                options={suggestionsStatus === 'OK' ? suggestionsData : []}
                getOptionLabel={(option) =>
                    typeof option === 'string' ? option : option.structured_formatting.main_text
                }
                isOptionEqualToValue={(option, value) =>
                    typeof option !== 'string' && option.place_id === value.place_id
                }
                filterOptions={(x) => x}
                onInputChange={handleInputChange}
                onChange={handleOptionSelect}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        size='small'
                        label={label}
                        variant='outlined'
                        fullWidth
                        disabled={!ready}
                        onBlur={() => {
                            formik.setFieldTouched(name);
                        }}
                        error={!!formik.errors[name] && formik.touched[name]}
                        helperText={formik.touched[name] && formik.errors[name]}
                    />
                )}
                renderOption={(props, option) => (
                    <li {...props} key={typeof option === 'string' ? option : option.place_id}>
                        {typeof option === 'string' ? option : option.description}
                    </li>
                )}
            />
        </>
    );
};
