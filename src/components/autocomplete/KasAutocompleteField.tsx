import React, {useEffect, useRef} from 'react';
import {Autocomplete, AutocompleteProps, TextField} from '@mui/material';
import {FormikValues} from 'formik';
import {SelectModel} from '@/interfaces';

interface KasAutocompleteFieldProps<T>
    extends Partial<AutocompleteProps<SelectModel<T>, false, false, false>> {
    name: string;
    formik: FormikValues;
    options: SelectModel<T>[];
    label?: string;
    showValidation?: boolean;
}

export const KasAutocompleteField = <T,>({
    name,
    formik,
    options,
    label = 'Select',
    showValidation = true,
    ...rest
}: KasAutocompleteFieldProps<T>) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const selectedValue = formik.values[name];

    const mergedOptions = React.useMemo(() => {
        if (selectedValue && !options.some((option) => option.value === selectedValue)) {
            return [
                ...options,
                {value: selectedValue, label: String(selectedValue), id: String(selectedValue)},
            ];
        }
        return options;
    }, [options, selectedValue]);

    useEffect(() => {
        if (inputRef.current && selectedValue) {
            inputRef.current.focus();
        }
    }, [selectedValue]);

    return (
        <Autocomplete
            {...rest}
            size='small'
            fullWidth
            options={mergedOptions}
            getOptionLabel={(option) => option.label}
            getOptionKey={(option) => option.id}
            value={mergedOptions.find((option) => option.value === selectedValue) || null}
            isOptionEqualToValue={(option, value) => option.value === value.value}
            onChange={(_, newValue) => {
                formik.setFieldValue(name, newValue?.value || '');
            }}
            onBlur={() => {
                formik.setFieldTouched(name);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    inputRef={inputRef}
                    variant='outlined'
                    label={label}
                    error={showValidation && !!formik.errors[name] && formik.touched[name]}
                    helperText={showValidation && formik.touched[name] && formik.errors[name]}
                />
            )}
        />
    );
};
