import React, {useEffect, useRef, useState} from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {SelectModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {FormikValues} from 'formik';

interface KasAutocompleteSelectProps<T> {
    name: string;
    formik: FormikValues;
    label: string;
    searchUrl: string;
    disabled?: boolean;
}

export const KasAutocompleteSelect = <T = string,>({
    name,
    formik,
    label,
    searchUrl,
    disabled = false,
}: KasAutocompleteSelectProps<T>) => {
    const abortControllerRef = useRef<AbortController | null>(null);
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<SelectModel<T>[]>([]);
    const [inputValue, setInputValue] = useState('');

    const getOptions = async (text: string) => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        const controller = new AbortController();
        abortControllerRef.current = controller;

        setLoading(true);
        try {
            const response = await apiRequest(`${searchUrl}${text}`, {signal: controller.signal});

            if (response.value) {
                setOptions(response.value);
            }
        } catch (error) {
            setOptions([]);
        } finally {
            setLoading(false);
            abortControllerRef.current = null;
        }
    };

    useEffect(() => {
        if (inputValue.trim() === '') {
            setOptions([]);
            return;
        }

        const debounceTimeout = setTimeout(() => {
            getOptions(inputValue).then();
        }, 300);

        return () => {
            clearTimeout(debounceTimeout);
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, [inputValue]);

    return (
        <Autocomplete
            loading={loading}
            options={options}
            size={'small'}
            disabled={disabled}
            value={
                formik.values[name] ? {id: 1, label: formik.values[name], value: formik.values[name]} : null
            }
            isOptionEqualToValue={(option, value) => option.value === value.value}
            onChange={(_, newValue) => {
                formik.setFieldValue(name, newValue?.value || '');
            }}
            onBlur={() => {
                formik.setFieldTouched(name);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label={label}
                    onChange={(e) => {
                        setInputValue(e.target.value);
                    }}
                    error={!!formik.errors[name] && formik.touched[name]}
                    helperText={formik.touched[name] && formik.errors[name]}
                />
            )}
        />
    );
};
