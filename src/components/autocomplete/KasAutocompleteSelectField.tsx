import React, {useEffect, useRef, useState} from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {FormikValues} from 'formik';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {LookupDTO} from '@/models';

interface KasAutocompleteSelectFieldProps {
    name: string;
    formik: FormikValues;
    label: string;
    url: string;
    disabled?: boolean;
    showValidation?: boolean;
}

export const KasAutocompleteSelectField = ({
    name,
    formik,
    label,
    url,
    disabled = false,
    showValidation = true,
}: KasAutocompleteSelectFieldProps) => {
    const [state, setState] = useState(getDefaultState<LookupDTO[]>);
    const inputRef = useRef<HTMLInputElement>(null);

    const loadTransactionTypes = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(url);
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadTransactionTypes().then();
    }, []);

    useEffect(() => {
        if (inputRef.current && formik.values[name]) {
            inputRef.current.focus();
        }
    }, [formik.values[name]]);

    return (
        <Autocomplete
            size='small'
            fullWidth
            disabled={disabled}
            loading={state.loading}
            options={state.data || []}
            getOptionLabel={(option) => option.text}
            getOptionKey={(option) => option.id}
            value={formik.values[name] || null}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            onChange={(_, newValue) => {
                formik.setFieldValue(name, newValue || null);
            }}
            onBlur={() => {
                formik.setFieldTouched(name);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    inputRef={inputRef}
                    variant='outlined'
                    label={label}
                    error={showValidation && !!formik.errors[name] && formik.touched[name]}
                    helperText={showValidation && formik.touched[name] && formik.errors[name]}
                />
            )}
        />
    );
};
