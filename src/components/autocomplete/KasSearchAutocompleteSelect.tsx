import React, {useEffect, useRef} from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {SelectModel} from '@/interfaces';

interface KasSearchAutocompleteSelectProps<T> {
    value: SelectModel<T> | null;
    label: string;
    placeholder: string;
    options: SelectModel<T>[];
    disabled?: boolean;
    autoFocus?: boolean;
    loading?: boolean;
    onSelect: (value: SelectModel<T> | null) => void;
}

export const KasSearchAutocompleteSelect = <T,>({
    value,
    label,
    placeholder,
    options,
    disabled = false,
    autoFocus = false,
    loading = false,
    onSelect,
}: KasSearchAutocompleteSelectProps<T>) => {
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (inputRef.current && autoFocus) {
            inputRef.current.focus();
        }
    }, [value]);

    return (
        <Autocomplete
            loading={loading}
            value={value}
            disabled={disabled}
            clearOnBlur
            options={options}
            isOptionEqualToValue={(option, value) => option.value === value.value}
            getOptionLabel={(option) => option.label}
            size='small'
            onChange={(_event, val) => {
                onSelect(val as SelectModel<T> | null);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    inputRef={inputRef}
                    label={label}
                    placeholder={placeholder}
                    variant='outlined'
                />
            )}
        />
    );
};
