import React, {ChangeEvent, useEffect, useRef, useState} from 'react';
import {Autocomplete, TextField} from '@mui/material';

interface KasAutocompleteFieldProps {
    value: string | null;
    label: string;
    placeholder: string;
    options: string[];
    disabled?: boolean;
    autoFocus?: boolean;
    onSelect: (value: string | null) => void;
    onBlur?: () => void;
    onHighlight?: (value: string | null) => void;
    onClose?: () => void;
    onKeyDown?: (event: React.KeyboardEvent) => void;
}

export const KasSearchAutocompleteField = ({
    value,
    label,
    placeholder,
    options,
    disabled = false,
    autoFocus = false,
    onSelect,
    onBlur,
    onHighlight,
    onClose,
    onKeyDown,
}: KasAutocompleteFieldProps) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [filteredOptions, setFilteredOptions] = useState<string[]>(options);
    const [hasFocus, setHasFocus] = useState(false);

    const onChange = (e: ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        onSelect(inputValue);

        setFilteredOptions(
            options.filter((option) => option.toLowerCase().includes(inputValue.toLowerCase())),
        );
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Tab' && filteredOptions.length > 0) {
            event.preventDefault();
            onSelect(filteredOptions[0]);
        }
        if (onKeyDown) {
            onKeyDown(event);
        }
    };

    const handleInputFocus = () => {
        if (inputRef.current) {
            inputRef.current.select();
            setHasFocus(true);
        }
    };

    useEffect(() => {
        if (inputRef.current && autoFocus) {
            inputRef.current.focus();
        }
    }, [autoFocus]);

    useEffect(() => {
        if (hasFocus && inputRef.current) {
            inputRef.current.select();
        }
    }, [hasFocus]);

    return (
        <Autocomplete
            freeSolo
            value={value}
            disabled={disabled}
            clearOnBlur
            options={options}
            getOptionLabel={(option) => option}
            size='small'
            onChange={(_event, val) => {
                onSelect(val);
            }}
            onHighlightChange={(event, option) => {
                if (event?.type === 'keydown' && onHighlight) {
                    onHighlight(option);
                }
            }}
            onInputChange={(event, newInputValue, reason) => {
                if (reason === 'input' && onHighlight) {
                    onHighlight(null);
                }
            }}
            onClose={onClose}
            renderInput={(params) => (
                <TextField
                    {...params}
                    inputRef={inputRef}
                    label={label}
                    value={value}
                    placeholder={placeholder}
                    variant='outlined'
                    onChange={onChange}
                    onKeyDown={handleKeyDown}
                    onBlur={() => {
                        if (onBlur) {
                            onBlur();
                        }
                    }}
                    onFocus={handleInputFocus}
                />
            )}
        />
    );
};
