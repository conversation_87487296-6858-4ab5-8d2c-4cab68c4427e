import React from 'react';
import {Close, Done} from '@mui/icons-material';
import {SvgIconOwnProps} from '@mui/material/SvgIcon/SvgIcon';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';
import {NO_RESULTS_SYMBOL} from '@/constants';

interface KasFlaggedIconProps extends SvgIconOwnProps, TestableProps {
    flagged: boolean | null;
    reverseColors?: boolean;
}

export const KasFlaggedIcon = ({flagged, reverseColors = false, testid, ...rest}: KasFlaggedIconProps) => {
    if (flagged === null) {
        return <span>{NO_RESULTS_SYMBOL}</span>;
    }

    return (
        <>
            {flagged ? (
                <Done
                    color={reverseColors ? 'error' : 'success'}
                    fontSize='small'
                    {...rest}
                    data-testid={`${testid}-flagged-icon`}
                />
            ) : (
                <Close
                    color={reverseColors ? 'success' : 'error'}
                    fontSize='small'
                    {...rest}
                    data-testid={`${testid}-flagged-icon`}
                />
            )}
        </>
    );
};
