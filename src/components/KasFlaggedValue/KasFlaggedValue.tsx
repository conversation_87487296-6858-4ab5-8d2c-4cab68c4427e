import React from 'react';
import {useTheme} from '@mui/material';
import { TestableProps } from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasFlaggedValueProps extends TestableProps {
    flagged: boolean;
}

export const KasFlaggedValue = ({flagged, testid}: KasFlaggedValueProps) => {
    const {palette} = useTheme();

    return (
        <span style={{color: flagged ? palette.error.main : palette.success.main}} data-testid={testid + '-flagged-value'}>
            {flagged ? 'Yes' : 'No'}
        </span>
    );
};
