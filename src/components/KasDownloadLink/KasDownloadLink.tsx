import './styles.scss';

import React, {AnchorHTMLAttributes, useState} from 'react';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';
import {Link} from '@mui/material';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasDownloadLinkProps extends AnchorHTMLAttributes<HTMLAnchorElement>, TestableProps {
    params: string;
    url?: string;
}

export const KasDownloadLink = ({children, params, url, testid, ...rest}: KasDownloadLinkProps) => {
    const [loadingFile, setLoadingFile] = useState(false);
    const {getBlob} = useDownloadBlob();

    const onClickHandler = async () => {
        setLoadingFile(true);
        await getBlob(params, url);
        setLoadingFile(false);
    };

    return (
        <Link
            {...rest}
            data-testid={testid}
            className={`kas-download-link ${loadingFile ? 'loading' : ''}`}
            underline='hover'
            style={{cursor: 'pointer'}}
            onClick={onClickHandler}>
            {children}
        </Link>
    );
};
