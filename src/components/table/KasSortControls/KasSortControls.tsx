import './styles.scss';

import React from 'react';
import {SortDirection} from '@tanstack/react-table';
import {ArrowDropDown, ArrowDropUp} from '@mui/icons-material';
import {useTheme} from '@mui/material';

interface KasSortControlsProps {
    direction: false | SortDirection;
}

export const KasSortControls = ({direction}: KasSortControlsProps) => {
    const {palette} = useTheme();

    return (
        <div className='kas-sort-controls' style={{color: palette.text.primary}}>
            <ArrowDropUp fontSize='small' color={direction === 'asc' ? 'inherit' : 'disabled'} />
            <ArrowDropDown fontSize='small' color={direction === 'desc' ? 'inherit' : 'disabled'} />
        </div>
    );
};
