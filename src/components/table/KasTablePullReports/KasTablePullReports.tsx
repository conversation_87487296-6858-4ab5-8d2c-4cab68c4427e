import React from 'react';
import {Button, Stack} from '@mui/material';
import {useTableActions} from '@/hooks/useTableActions';
import {Table} from '@tanstack/react-table';
import {ExcelColumnFormatModel} from '@/interfaces';

interface KasTablePullReports<T> {
    table: Table<T>;
    tableName: string;
    excelColumnFormats?: ExcelColumnFormatModel[];
}

export const KasTablePullReports = <T,>({
    table,
    tableName,
    excelColumnFormats = [],
}: KasTablePullReports<T>) => {
    const {printTable, saveTableAsExcel, saveTableAsCSV, saveTableAsPDF} = useTableActions<T>();

    return (
        <Stack direction='row' spacing={1}>
            <Button variant='text' onClick={() => printTable(table, tableName)}>
                Print
            </Button>
            <Button
                variant='text'
                onClick={async () => {
                    await saveTableAsExcel(table, tableName, excelColumnFormats);
                }}>
                Excel
            </Button>
            <Button variant='text' onClick={() => saveTableAsCSV(table, tableName)}>
                CSV
            </Button>
            <Button variant='text' onClick={() => saveTableAsPDF(table, tableName)}>
                PDF
            </Button>
        </Stack>
    );
};
