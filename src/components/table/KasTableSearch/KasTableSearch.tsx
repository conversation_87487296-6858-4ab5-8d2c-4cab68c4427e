import React, {useState} from 'react';
import {Table} from '@tanstack/react-table';
import {InputAdornment, TextField} from '@mui/material';
import {Clear, Search} from '@mui/icons-material';

interface KasTableHeadActionsProps<T> {
    table: Table<T>;
}

export const KasTableSearch = <T,>({table}: KasTableHeadActionsProps<T>) => {
    const [searchValue, setSearchValue] = useState('');

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchValue(value);
        table.setGlobalFilter(value);
    };

    const handleClearSearch = () => {
        setSearchValue('');
        table.setGlobalFilter('');
    };

    return (
        <TextField
            fullWidth
            size='small'
            placeholder='Search'
            variant='outlined'
            value={searchValue}
            onChange={handleSearchChange}
            slotProps={{
                input: {
                    startAdornment: (
                        <InputAdornment position='start'>
                            <Search fontSize='small' color='disabled' />
                        </InputAdornment>
                    ),
                    endAdornment: searchValue && (
                        <InputAdornment position='end'>
                            <Clear
                                fontSize='small'
                                color='disabled'
                                style={{cursor: 'pointer'}}
                                onClick={handleClearSearch}
                            />
                        </InputAdornment>
                    ),
                    sx: {
                        backgroundColor: 'var(--background-paper)',
                    },
                },
            }}
        />
    );
};
