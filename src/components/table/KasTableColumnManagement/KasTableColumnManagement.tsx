import './styles.scss';

import React, {useEffect, useState} from 'react';
import {Column, HeaderGroup, Table} from '@tanstack/react-table';
import {Checkbox, FormControlLabel} from '@mui/material';

interface KasTableColumnManagementProps<T> {
    table: Table<T>;
}

export const KasTableColumnManagement = <T,>({table}: KasTableColumnManagementProps<T>) => {
    const [headerGroups, setHeaderGroups] = useState<HeaderGroup<T>[]>([]);

    useEffect(() => {
        setHeaderGroups(table.getHeaderGroups());
    }, []);

    return (
        <div className='kas-table-column-management'>
            <table>
                <thead>
                    {headerGroups.map((headerGroup) => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                const label = (column: Column<T, unknown>): string => {
                                    const result = column.columnDef.header as string;

                                    if (column.id.includes('date_first')) {
                                        return result.replace('1', '');
                                    }

                                    return result;
                                };
                                const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
                                    if (!header.column.columns.length) {
                                        if (header.column.id.includes('date_first')) {
                                            const secondId = header.column.id.replace(
                                                'date_first',
                                                'date_second',
                                            );
                                            const secondHeader = headerGroup.headers.find(
                                                (item) => item.id === secondId,
                                            );

                                            header.column.getToggleVisibilityHandler()(event);

                                            if (secondHeader) {
                                                secondHeader.column.getToggleVisibilityHandler()(event);
                                            }
                                        } else {
                                            header.column.getToggleVisibilityHandler()(event);
                                        }
                                    } else {
                                        header.column.getToggleVisibilityHandler()(event);
                                        header.column.columns.forEach((column) => {
                                            column.getToggleVisibilityHandler()(event);
                                        });
                                    }
                                };

                                if (header.column.id.includes('date_second')) {
                                    return null;
                                }

                                return (
                                    <th
                                        key={header.id}
                                        colSpan={
                                            header.column.id.includes('date_first')
                                                ? header.colSpan + 1
                                                : header.colSpan
                                        }>
                                        {header.isPlaceholder ? null : (
                                            <FormControlLabel
                                                key={header.column.id}
                                                label={label(header.column)}
                                                control={
                                                    <Checkbox
                                                        size='small'
                                                        checked={header.column.getIsVisible()}
                                                        onChange={handleCheckboxChange}
                                                        inputProps={{'aria-label': 'controlled'}}
                                                    />
                                                }
                                            />
                                        )}
                                    </th>
                                );
                            })}
                        </tr>
                    ))}
                </thead>
            </table>
        </div>
    );
};
