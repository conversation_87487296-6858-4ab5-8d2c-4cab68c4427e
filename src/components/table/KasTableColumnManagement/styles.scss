@mixin cell {
    border-bottom: 1px solid var(--color-divider);
    border-left: 1px solid var(--color-divider);
    padding: 8px;

    &:first-child {
        border-left: none;
    }
}

@mixin head-footer-cell {
    &:empty {
        border-left-color: transparent;
    }

    &:not(:empty) + th:empty {
        border-left: 1px solid var(--color-divider);
    }
}

.kas-table-column-management {
    overflow: auto;

    table {
        border-spacing: 0;
        width: 100%;
        border: 1px solid var(--color-divider);
        border-bottom: none;
        font-size: var(--table-text-size);
        background: var(--background-paper);
        border-radius: var(--border-radius);
        text-align: left;

        thead {
            th {
                @include cell();
                @include head-footer-cell();
                white-space: nowrap;

                &[colspan='1'] {
                    text-align: inherit;

                    > div {
                        justify-content: flex-start;
                    }
                }

                > div {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}
