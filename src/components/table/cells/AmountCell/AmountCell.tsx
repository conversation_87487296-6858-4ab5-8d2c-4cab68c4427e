import './styles.scss';

import React, {useMemo} from 'react';
import {toCurrency} from '@/utils/FormatUtils';

interface AmountCellProps {
    data: number;
    isMulticolor?: boolean;
}

export const AmountCell = ({data, isMulticolor = false}: AmountCellProps) => {
    const className = useMemo(() => {
        let result = 'amount-cell';

        if (isMulticolor) {
            if (data === 0) {
                return `${result} grey`;
            }
            if (data > 0) {
                return `${result} green`;
            }
            if (data < 0) {
                return `${result} red`;
            }
        }

        return result;
    }, [data, isMulticolor]);

    return <div className={className}>{isNaN(data) ? '' : toCurrency(data)}</div>;
};
