import React from 'react';
import {AMLFlagType} from '@/screens/ComplianceScreen/interfaces';

const FLAG_LABELS: Record<AMLFlagType, {label: string; description: string}> = {
    [AMLFlagType.LARGE_PAYMENT]: {
        label: 'Large Payment',
        description: 'A payment exceeds the installment amount by 150% or more',
    },
    [AMLFlagType.EARLY_PAYOFF]: {
        label: 'Early Payoff',
        description: 'The loan reaches a $0 balance within 60 days or less of the date loan disbursement',
    },
    [AMLFlagType.OVERPAYMENT]: {
        label: 'Overpayment',
        description: 'A single payment which results in an account balance of -$500 or more.',
    },
    [AMLFlagType.MULTIPLE_PAYMENTS]: {
        label: 'Multiple Payments',
        description: 'Three or more payments in a rolling 7 day period.',
    },
};

const FlagType = ({label, description}: {label: string; description: string}) => (
    <abbr title={description} style={{marginRight: 4}}>
        {label}
    </abbr>
);

export const AMLFlagTypeCell = ({flagTypes}: {flagTypes: AMLFlagType[]}) => {
    const renderFlagType = (type: AMLFlagType) => {
        switch (type) {
            case AMLFlagType.LARGE_PAYMENT:
            case AMLFlagType.EARLY_PAYOFF:
            case AMLFlagType.OVERPAYMENT:
            case AMLFlagType.MULTIPLE_PAYMENTS: {
                const {label, description} = FLAG_LABELS[type];
                return <FlagType key={type} label={label} description={description} />;
            }
            default:
                return null;
        }
    };

    return (
        <>
            {flagTypes.map((type, index) => (
                <React.Fragment key={type}>
                    {renderFlagType(type)}
                    {index < flagTypes.length - 1 && ', '}
                </React.Fragment>
            ))}
        </>
    );
};

export const accessorAMLFlagTypeCellFn = (flag_types: AMLFlagType[]): string => {
    return flag_types
        .map((type: AMLFlagType) => {
            const flagData = FLAG_LABELS[type];
            return flagData ? `${flagData.label} ${flagData.description}` : '';
        })
        .join(' ');
};
