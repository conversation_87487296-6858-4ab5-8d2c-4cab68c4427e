import './styles.scss';

import React, {useState} from 'react';
import ArrowCircleDownIcon from '@mui/icons-material/ArrowCircleDown';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';
import {ActionCell} from '@/components/table/cells';
import {KasLoading} from '@/components';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';

interface StepCellProps extends TestableProps {
    params: string;
    url?: string;
    titleAccess?: string;
    Icon?: React.ReactNode;
}

export const DownloadCell = ({params, url, titleAccess = 'Download', Icon, testid}: StepCellProps) => {
    const [loadingFile, setLoadingFile] = useState(false);
    const {getBlob} = useDownloadBlob();

    const onClickHandler = async () => {
        setLoadingFile(true);
        await getBlob(params, url);
        setLoadingFile(false);
    };

    return (
        <ActionCell
            disabled={loadingFile}
            Icon={
                loadingFile ? (
                    <KasLoading size={18} />
                ) : (
                    Icon || <ArrowCircleDownIcon titleAccess={titleAccess} color='primary' />
                )
            }
            onClick={onClickHandler}
            testid={testid + '-download'}
        />
    );
};
