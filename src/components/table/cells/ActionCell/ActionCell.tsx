import './styles.scss';

import React, {useMemo} from 'react';
import IconButton from '@mui/material/IconButton';
import ArrowCircleDownIcon from '@mui/icons-material/ArrowCircleDown';
import {styled} from '@mui/material';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';
import {KasLoading} from '@/components';

const StyledIconButton = styled(IconButton)(({theme}) => ({
    padding: '4px',
    minHeight: '28px',
    borderRadius: '4px',
    border: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.default,
    '&:hover': {
        backgroundColor: theme.palette.action.hover,
        borderColor: theme.palette.primary.main,
    },
    '&.Mui-disabled': {
        backgroundColor: theme.palette.action.disabledBackground,
        borderColor: theme.palette.action.disabled,
    },
    '.MuiSvgIcon-root': {
        width: '18px',
        height: '18px',
        '&.Mui-disabled': {
            color: theme.palette.action.disabled,
        },
    },
}));

const DEFAULT_ICON = <ArrowCircleDownIcon color='primary' fontSize='inherit' />;

interface ActionCellProps extends TestableProps {
    onClick?: () => void;
    Icon?: React.ReactNode;
    disabled?: boolean;
    href?: string;
    loading?: boolean;
}

export const ActionCell = ({
    onClick,
    Icon = DEFAULT_ICON,
    disabled = false,
    href,
    loading = false,
    testid,
}: ActionCellProps) => {
    const renderIcon = useMemo(() => {
        return loading ? <KasLoading size={18} /> : Icon;
    }, [loading, Icon]);

    return (
        <div className='action-cell' data-testid={`${testid}-action-cell`}>
            {href && (
                <a href={href} target='_blank'>
                    <StyledIconButton disabled={disabled}>{renderIcon}</StyledIconButton>
                </a>
            )}
            {onClick && (
                <StyledIconButton
                    disabled={disabled}
                    onClick={(event) => {
                        event.stopPropagation();
                        onClick && onClick();
                    }}>
                    {renderIcon}
                </StyledIconButton>
            )}
        </div>
    );
};
