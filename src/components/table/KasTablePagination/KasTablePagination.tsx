import './styles.scss';

import React from 'react';
import {PaginationState, Table} from '@tanstack/react-table';
import {MenuItem, Pagination, Select, Stack, Typography} from '@mui/material';

const PAGINATION_LIST: number[] = [12, 24, 48, 120];

interface TablePaginationProps<T> {
    table: Table<T>;
    onChange: (value: Partial<PaginationState>) => void;
}

export const KasTablePagination = <T,>({table, onChange}: TablePaginationProps<T>) => {
    const pageIndex = table.getState().pagination.pageIndex;
    const pageSize = table.getState().pagination.pageSize;
    const disabled = !pageSize;
    const length = table.getPrePaginationRowModel().rows.length;

    return (
        <div className='kas-table-pagination'>
            <Stack direction='row' alignItems='center' className='kas-table-pagination__size' spacing={2}>
                <Typography variant='body1'>Rows per page</Typography>
                <Select
                    disabled={disabled}
                    size='small'
                    variant='outlined'
                    value={pageSize}
                    sx={{minHeight: '32px'}}
                    onChange={(e) => {
                        onChange({pageIndex: 0, pageSize: Number(e.target.value)});
                    }}
                    displayEmpty>
                    {PAGINATION_LIST.map((value) => (
                        <MenuItem key={value} sx={{fontSize: 'var(--table-text-size)'}} value={value}>
                            {value}
                        </MenuItem>
                    ))}
                </Select>
                <Typography variant='body1'>
                    {pageIndex * pageSize + 1}-
                    {(pageIndex + 1) * pageSize > length ? length : (pageIndex + 1) * pageSize} of {length}
                </Typography>
            </Stack>
            <Pagination
                disabled={disabled}
                showFirstButton={true}
                showLastButton={true}
                count={Math.ceil(length / pageSize)}
                page={pageIndex + 1}
                onChange={(event, newPage) => {
                    onChange({pageIndex: newPage - 1});
                }}
                shape='rounded'
            />
        </div>
    );
};
