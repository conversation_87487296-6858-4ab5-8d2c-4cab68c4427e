import React, {ReactNode} from 'react';
import {Table} from '@tanstack/react-table';
import {Grid2} from '@mui/material';
import {KasTableSearch} from '@/components/table';

interface KasTableHeadActionsProps<T> {
    table: Table<T>;
    children?: ReactNode;
}

export const KasTableHeadActions = <T,>({table, children}: KasTableHeadActionsProps<T>) => {
    return (
        <Grid2 container alignItems='center' mb={2}>
            <Grid2 size={10}>{children}</Grid2>
            <Grid2 size={2}>
                <KasTableSearch table={table} />
            </Grid2>
        </Grid2>
    );
};
