.kas-modal {
    $padding-horizontal: 40px;
    $padding-vertical: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 920px;
    max-height: calc(100vh - 60px);
    overflow: hidden;
    border: none;
    outline: none;

    &.small {
        max-width: 600px;
    }

    &.large {
        max-width: 1200px;
    }

    &:after {
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: $padding-vertical;
        background-image: linear-gradient(to bottom, transparent 0%, var(--background-paper) 50%);
        content: '';
    }

    &__close {
        position: absolute !important;
        top: 16px;
        right: 16px;
        z-index: 100;
    }

    &__wrap {
        padding-top: $padding-vertical;
    }

    &__head {
        padding: 0 $padding-horizontal 12px;
    }

    &__content {
        overflow: auto;
        max-height: calc(100vh - 154px - $padding-vertical);
        padding: 12px $padding-horizontal 0;

        &:after {
            display: block;
            height: $padding-vertical;
            content: '';
        }
    }
}
