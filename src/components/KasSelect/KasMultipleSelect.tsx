import React, {SyntheticEvent, useEffect, useMemo, useState} from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import {Checkbox, Tooltip} from '@mui/material';
import {FormikValues} from 'formik';
import {LookupDTO} from '@/models';

interface KasMultipleSelectProps {
    options: LookupDTO[];
    name: string;
    placeholder: string;
    formik: FormikValues;
    disabled?: boolean;
}

const SELECT_ALL_OPTION: LookupDTO = {id: 'ALL', text: 'Select All'};

export const KasMultipleSelect = ({
    options,
    name,
    placeholder,
    formik,
    disabled = false,
}: KasMultipleSelectProps) => {
    const [selectedItems, setSelectedItems] = useState<LookupDTO[]>([]);

    const title = useMemo(() => {
        return selectedItems.map(({text}) => text).join(', ');
    }, [selectedItems]);

    const label = useMemo(() => {
        if (selectedItems.length > 1) {
            return `${selectedItems.length} ${placeholder} selected`;
        } else if (selectedItems.length === 1) {
            return selectedItems[0].text;
        }

        return placeholder;
    }, [selectedItems]);

    const handleStateChange = (_event: SyntheticEvent, value: LookupDTO[]) => {
        const isSelectAllSelected = value.some((option) => option.id === SELECT_ALL_OPTION.id);

        if (isSelectAllSelected) {
            if (formik.values[name].length === options.length) {
                setSelectedItems([]);
            } else {
                setSelectedItems(options);
            }
        } else {
            setSelectedItems(value);
        }
    };

    useEffect(() => {
        formik.setFieldValue(
            name,
            selectedItems.map(({id}) => id),
        );
    }, [selectedItems]);

    useEffect(() => {
        if (selectedItems.length && formik.values[name].length === 0) {
            setSelectedItems([]);
        }
    }, [formik.values[name]]);

    return (
        <Autocomplete
            multiple
            options={[SELECT_ALL_OPTION, ...options]}
            disableCloseOnSelect
            disabled={disabled}
            value={selectedItems}
            getOptionLabel={(option) => option.text}
            filterOptions={(options, state) => {
                const searchText = state.inputValue.toLowerCase();

                return options.filter(
                    (option) =>
                        option.text.toLowerCase().includes(searchText) ||
                        option.id.toLowerCase().includes(searchText),
                );
            }}
            onChange={handleStateChange}
            renderTags={() => null}
            renderOption={(props, option, {selected}) => {
                const {key, ...propsWithoutKey} = props as {key?: string};

                return (
                    <li key={option.id} {...propsWithoutKey}>
                        <Checkbox
                            size='small'
                            sx={{
                                marginY: '-8px',
                                marginLeft: '-8px',
                            }}
                            checked={
                                option.id === SELECT_ALL_OPTION.id
                                    ? selectedItems.length === options.length
                                    : selected
                            }
                        />
                        {option.text}
                    </li>
                );
            }}
            renderInput={(params) => (
                <Tooltip title={title} placement='top'>
                    <TextField
                        {...params}
                        size='small'
                        label={label}
                        placeholder={label}
                        error={formik.touched[name] && Boolean(formik.errors[name])}
                        helperText={formik.touched[name] && formik.errors[name]}
                    />
                </Tooltip>
            )}
        />
    );
};
