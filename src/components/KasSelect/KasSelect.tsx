import React from 'react';
import {FormControl, InputLabel, Select, SelectProps} from '@mui/material';

const KasSelectComponent = <Value = unknown,>(props: SelectProps<Value>) => {
    const {variant, label, labelId, children, ...selectProps} = props;

    return (
        <FormControl fullWidth size='small'>
            {label && <InputLabel id={labelId}>{label}</InputLabel>}
            <Select label={label} labelId={labelId} {...selectProps} variant={variant}>
                {children}
            </Select>
        </FormControl>
    );
};

export const KasSelectOutlined = <Value = unknown,>(props: Omit<SelectProps<Value>, 'variant'>) => (
    <KasSelectComponent {...props} variant='outlined' />
);

export const KasSelectFilled = <Value = unknown,>(props: Omit<SelectProps<Value>, 'variant'>) => (
    <KasSelectComponent {...props} variant='filled' />
);

export const KasSelectStandard = <Value = unknown,>(props: Omit<SelectProps<Value>, 'variant'>) => (
    <KasSelectComponent {...props} variant='standard' />
);

export const KasSelect = <Value = unknown,>(props: SelectProps<Value>) => {
    const {variant, ...rest} = props;

    switch (variant) {
        case 'filled':
            return <KasSelectFilled {...rest} />;
        case 'standard':
            return <KasSelectStandard {...rest} />;
        case 'outlined':
        default:
            return <KasSelectOutlined {...rest} />;
    }
};
