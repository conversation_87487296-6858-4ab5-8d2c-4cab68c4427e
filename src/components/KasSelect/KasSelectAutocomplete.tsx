import React, {useState} from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {SelectModel} from '@/interfaces';

interface KasSelectAutocompleteProps<T = string> {
    label: string;
    options: SelectModel<T>[];
    value: T | null;
    onChange: (value: T | null) => void;
    disabled?: boolean;
    fullWidth?: boolean;
    error?: boolean;
    helperText?: string;
}

export const KasSelectAutocomplete = <T = string,>({
    label,
    options,
    value,
    onChange,
    disabled = false,
    fullWidth = true,
    error,
    helperText,
}: KasSelectAutocompleteProps<T>) => {
    const [inputValue, setInputValue] = useState('');

    const selectedOption = options.find((opt) => opt.value === value) ?? null;

    return (
        <Autocomplete
            size='small'
            fullWidth={fullWidth}
            options={options}
            value={selectedOption}
            inputValue={inputValue}
            onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
            onChange={(_, newValue) => onChange(newValue?.value ?? null)}
            isOptionEqualToValue={(option, val) => option.value === val.value}
            getOptionLabel={(option) => option.label}
            disabled={disabled}
            renderInput={(params) => (
                <TextField {...params} label={label} error={error} helperText={helperText} />
            )}
        />
    );
};
