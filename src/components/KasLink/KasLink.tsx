import './styles.scss';

import React, {AnchorHTMLAttributes, PropsWith<PERSON>hildren, ReactNode} from 'react';
import {UNDERWRITING_EMPL_HASH, UNDERWRITING_LOAN_HASH, UNDERWRITING_USER_HASH} from '@/constants';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasLinkProps extends AnchorHTMLAttributes<HTMLAnchorElement>, TestableProps {
    children: ReactNode;
    disabled?: boolean;
    onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
}

export const KasLink = ({
    href,
    disabled = false,
    target,
    children,
    onClick,
    testid,
    ...rest
}: KasLinkProps) => {
    return (
        <a
            href={href}
            target={target}
            className={`kas-link ${disabled ? 'disabled' : ''}`}
            onClick={onClick}
            data-testid={testid + '-link'}
            {...rest}>
            {children}
        </a>
    );
};

interface KasSharedLinkProps extends PropsWithChildren {
    href: string;
    disabled?: boolean;
}

export const KasSharedLink = ({href, disabled, children}: KasSharedLinkProps) => {
    const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
        if (event.ctrlKey || event.metaKey) {
            window.open(href, '_blank');
        } else if (href.includes(window.location.pathname)) {
            window.location.href = href;
        } else {
            const tab = window.open('', href);

            if (tab) {
                tab.location.href = href;
                tab.focus();
            } else {
                window.open(href, '_blank');
            }
        }
    };

    return (
        <KasLink disabled={disabled} onClick={handleClick}>
            {children}
        </KasLink>
    );
};

interface KasUnderwritingSharedLinkProps extends PropsWithChildren {
    id: string | number | undefined | null;
    disabled?: boolean;
}

export const KasUnderwritingSharedLink = ({id, disabled}: KasUnderwritingSharedLinkProps) => (
    <KasSharedLink href={`/secured/underwriting#${UNDERWRITING_EMPL_HASH}:${id}`} disabled={disabled}>
        {id}
    </KasSharedLink>
);

export const KasUnderwritingSharedUserLink = ({id, disabled}: KasUnderwritingSharedLinkProps) => (
    <KasSharedLink href={`/secured/underwriting#${UNDERWRITING_USER_HASH}:${id}`} disabled={disabled}>
        {id}
    </KasSharedLink>
);

export const KasUnderwritingSharedLoanLink = ({id, disabled}: KasUnderwritingSharedLinkProps) => (
    <KasSharedLink href={`/secured/underwriting#${UNDERWRITING_LOAN_HASH}:${id}`} disabled={disabled}>
        {id}
    </KasSharedLink>
);
