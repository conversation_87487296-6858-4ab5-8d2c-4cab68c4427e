import React from 'react';
import {Payload} from 'recharts/types/component/DefaultLegendContent';
import Box from '@mui/material/Box';

export interface KasCustomLegendProps {
    payload?: Payload[];
    hiddenKeys?: string[];
    onClick?: (key: string) => void;
}

export const KasCustomLegend = ({payload, hiddenKeys = [], onClick}: KasCustomLegendProps) => {
    return (
        <Box display='flex' justifyContent='center' gap={2}>
            {payload?.map((item) => {
                const key = item.dataKey as string;
                const isHidden = hiddenKeys.includes(key);

                return (
                    <Box
                        key={key}
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            color: isHidden ? 'text.disabled' : item.color,
                            ...(!!onClick && {
                                cursor: 'pointer',
                                '&:hover': {
                                    textDecoration: 'underline',
                                },
                            }),
                        }}
                        onClick={() => onClick && onClick(key)}>
                        {item.type === 'line' ? (
                            <div
                                style={{
                                    width: 16,
                                    height: 2,
                                    background: item.color,
                                    borderRadius: 1,
                                }}
                            />
                        ) : (
                            <div style={{width: 8, height: 8, background: item.color}} />
                        )}
                        <span>{item.value}</span>
                    </Box>
                );
            })}
        </Box>
    );
};
