import React, {useState} from 'react';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DatePicker, DateView} from '@mui/x-date-pickers-pro';
import {Dayjs} from 'dayjs';

interface KasDatePickerProps {
    value: Dayjs | null;
    disabled?: boolean;
    label?: string;
    views?: DateView[];
    onChange: (value: Dayjs | null) => void;
}

export const KasDatePicker = ({
    value,
    disabled = false,
    label = 'Date',
    views,
    onChange,
}: KasDatePickerProps) => {
    const [open, setOpen] = useState(false);

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
                open={open}
                value={value}
                disabled={disabled}
                label={label}
                views={views}
                slotProps={{
                    textField: {
                        size: 'small',
                        fullWidth: true,
                        InputProps: {endAdornment: <></>},
                        onClick: () => setOpen(true),
                    },
                }}
                onClose={() => setOpen(false)}
                onChange={onChange}
            />
        </LocalizationProvider>
    );
};
