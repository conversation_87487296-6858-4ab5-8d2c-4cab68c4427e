import './styles.scss';

import React from 'react';
import Image, {StaticImageData} from 'next/image';
import abcsysImage from './sources/abcsys.png';
import alightImage from './sources/alight.png';
import atomicImage from './sources/atomic.png';
import appleImage from './sources/apple.png';
import benefitfocusImage from './sources/benefitfocus.png';
import benefithubImage from './sources/benefithub.png';
import brightsideImage from './sources/brightside.png';
import facebookImage from './sources/facebook.png';
import googleImage from './sources/google.png';
import pinwheelImage from './sources/pinwheel.png';
import transunionImage from './sources/transunion.png';
import {HideImage} from '@mui/icons-material';

const imagesMap: {[key: string]: StaticImageData} = {
    abcsys: abcsysImage,
    alight: alightImage,
    atomic: atomicImage,
    apple: appleImage,
    benefitfocus: benefitfocusImage,
    benefithub: benefithubImage,
    brightside: brightsideImage,
    facebook: facebookImage,
    google: googleImage,
    pinwheel: pinwheelImage,
    transunion: transunionImage,
};

interface KasSocialImageProps {
    type: string;
}

export const KasSocialImage = ({type}: KasSocialImageProps) => {
    return (
        <div className='kas-social-image' data-testid={`kas-social-image-${type}`}>
            {imagesMap[type.toLowerCase()] ? (
                <Image src={imagesMap[type.toLowerCase()]} alt={type} />
            ) : (
                <HideImage fontSize='large' color='action' />
            )}
        </div>
    );
};
