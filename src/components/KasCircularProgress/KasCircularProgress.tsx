import React from 'react';
import {CircularProgress, CircularProgressProps, Typography} from '@mui/material';
import Box from '@mui/material/Box';

interface KasInfoProps extends CircularProgressProps {
    value: number;
    size: number;
}
export const KasCircularProgress = (props: KasInfoProps) => {
    return (
        <Box sx={{position: 'relative', display: 'inline-flex'}}>
            <CircularProgress variant='determinate' {...props} />
            <Box
                sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                <Typography
                    variant='caption'
                    component='div'
                    sx={{
                        color: 'text.secondary',
                        fontSize: `${props.size * 0.3}px`,
                    }}>{`${Math.round(props.value)}%`}</Typography>
            </Box>
        </Box>
    );
};
