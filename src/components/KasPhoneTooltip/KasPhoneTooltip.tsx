import React, {useState} from 'react';
import {CircularProgress, Divider, Stack, Tooltip, useTheme} from '@mui/material';
import {useSnackbar} from '@/hooks/useSnackbar';
import {KasCopyText, KasLink, KasSwitch, KasSwitchWhen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';

interface KasPhoneTooltipProps {
    phone: string;
    addedDate?: string | null;
    caller?: string | null;
    withCopy?: boolean;
    restricted?: string | null;
}

export const KasPhoneTooltip = ({
    phone,
    addedDate,
    caller,
    withCopy = false,
    restricted,
}: KasPhoneTooltipProps) => {
    const {palette} = useTheme();
    const {showMessage} = useSnackbar();
    const [callerState, setCallerState] = useState(getDefaultState<string>());
    const restrictedStyles = {
        textDecoration: 'underline dotted',
        color: palette.action.disabled,
    };

    const onLoadCaller = async () => {
        setCallerState(getLoadingState(callerState));
        const response = await apiRequest(`/api/secured/ui/lookup/phone/cnam?phone=${phone}`);
        setCallerState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    const tooltipContent = (
        <Stack spacing={1} p={0.5} sx={{fontWeight: 400}}>
            {addedDate && (
                <>
                    <div>Added: {addedDate}</div>
                    <Divider />
                </>
            )}

            {!!restricted && (
                <>
                    <div>Contact Restricted: {restricted}</div>
                    <Divider />
                </>
            )}

            {caller ? (
                <div>Caller ID: {caller}</div>
            ) : (
                <KasSwitch>
                    <KasSwitchWhen condition={callerState.loading}>
                        <Stack direction='row' alignItems='center' spacing={1}>
                            <span>Caller ID:</span>
                            <CircularProgress size={14} style={{color: 'var(--text)'}} />
                        </Stack>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!callerState.data}>
                        <KasLink style={{color: 'var(--text)'}} onClick={onLoadCaller}>
                            Caller ID
                        </KasLink>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!callerState.data}>
                        <div>Caller ID: {callerState.data}</div>
                    </KasSwitchWhen>
                </KasSwitch>
            )}
        </Stack>
    );

    return (
        <Stack flexDirection='row' flexWrap='wrap' useFlexGap alignItems='center'>
            <Tooltip title={tooltipContent} arrow placement='top'>
                <span style={!!restricted ? restrictedStyles : {}}>{phone}</span>
            </Tooltip>
            {withCopy && <KasCopyText textToCopy={phone} />}
        </Stack>
    );
};
