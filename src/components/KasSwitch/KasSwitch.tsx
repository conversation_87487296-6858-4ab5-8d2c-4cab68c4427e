import React, {ReactNode} from 'react';
import {KasErrorBoundary} from '@/components';

interface KasSwitchProps {
    children: ReactNode;
}

interface KasSwitchWhenProps {
    condition: boolean;
    children: ReactNode;
}

const KasSwitch: React.FC<KasSwitchProps> = ({children}) => {
    const validChild = React.Children.toArray(children).find((child: ReactNode) => {
        if (React.isValidElement(child)) {
            return child.props.condition;
        }
        return false;
    });

    return <KasErrorBoundary>{validChild}</KasErrorBoundary>;
};

const KasSwitchWhen: React.FC<KasSwitchWhenProps> = ({condition, children}) => {
    return condition ? <>{children}</> : null;
};

export {KasSwitch, KasSwitchWhen};
