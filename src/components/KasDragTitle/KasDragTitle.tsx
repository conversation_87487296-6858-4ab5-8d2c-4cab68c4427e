import React, {PropsWithChildren} from 'react';
import {Stack, Typography} from '@mui/material';
import {DragIndicator} from '@mui/icons-material';
import {DraggableAttributes} from '@dnd-kit/core';
import {SyntheticListenerMap} from '@dnd-kit/core/dist/hooks/utilities';

interface KasDragTitleProps extends PropsWithChildren {
    attributes: DraggableAttributes;
    listeners: SyntheticListenerMap | undefined;
    setActivatorNodeRef: (element: HTMLElement | null) => void;
}

export const KasDragTitle = ({attributes, listeners, setActivatorNodeRef, children}: KasDragTitleProps) => {
    const DragIcon = (
        <div {...attributes} {...listeners} ref={setActivatorNodeRef} style={{cursor: 'grab'}}>
            <DragIndicator color={'disabled'} fontSize={'small'} />
        </div>
    );

    return (
        <Stack direction='row' alignItems='center' spacing={1}>
            {DragIcon}
            <Typography variant='h6'>{children}</Typography>
        </Stack>
    );
};
