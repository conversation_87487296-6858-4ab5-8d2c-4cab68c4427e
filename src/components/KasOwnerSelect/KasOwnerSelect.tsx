import React, {useEffect, useState} from 'react';
import {KasSelect} from '@/components';
import {OWNERS_LIST} from './data';
import {SelectModel} from '@/interfaces';
import MenuItem from '@mui/material/MenuItem';
import {SelectChangeEvent} from '@mui/material';

interface KasOwnerProps {
    labelId: string;
    disabled: boolean;
    onOwnerSelect: (owner: SelectModel<string>) => void;
}

export const KasOwnerSelect = ({onOwnerSelect, labelId, disabled}: KasOwnerProps) => {
    const [activeOwner, setActiveOwner] = useState<SelectModel<string>>(OWNERS_LIST[0]);

    useEffect(() => {
        onOwnerSelect(activeOwner);
    }, [activeOwner]);

    const handleChangeMethod = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newOwner = OWNERS_LIST.find(({value}) => value === newValue);

        if (newOwner) {
            setActiveOwner(newOwner);
        }
    };

    return (
        <KasSelect
            labelId={labelId}
            label='Owner'
            value={activeOwner.value}
            disabled={disabled}
            onChange={handleChangeMethod}>
            {OWNERS_LIST.map(({id, value, label}) => (
                <MenuItem key={id} value={value}>
                    {label}
                </MenuItem>
            ))}
        </KasSelect>
    );
};
