import './styles.scss';

import React, {useState} from 'react';
import {VisibilityOutlined} from '@mui/icons-material';
import {CircularProgress} from '@mui/material';
import {NO_RESULTS_SYMBOL} from '@/constants';
import {Completable} from '@/interfaces';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';

interface EmployeeProfileSSNProps extends TestableProps {
    ssn: string | null;
    onLoanSSN?: () => Promise<Completable<string>>;
}

export const KasMaskedSSN = ({ssn, onLoanSSN, testid}: EmployeeProfileSSNProps) => {
    const maskedSSN = !!ssn && ssn.length > 4 ? `XXXXXXX${ssn.slice(-4)}` : ssn;
    const [unmaskedSSN, setUnmaskedSSN] = useState('');
    const [loading, setLoading] = useState(false);

    const loadSSN = async () => {
        setLoading(true);

        if (onLoanSSN) {
            const response = await onLoanSSN();

            if (response.value) {
                setUnmaskedSSN(response.value);
            }
        } else {
            setUnmaskedSSN(ssn || '');
        }

        setLoading(false);
    };

    if (!ssn) {
        return NO_RESULTS_SYMBOL;
    }

    return (
        <span className='kas-masked-ssn' data-testid={testid + '-masked-ssn'}>
            {unmaskedSSN || maskedSSN}
            {!unmaskedSSN && (
                <span className='kas-masked-ssn__icon'>
                    {loading ? (
                        <CircularProgress size={14} />
                    ) : (
                        <span className='kas-masked-ssn__load' onClick={loadSSN}>
                            <VisibilityOutlined fontSize='small' color='inherit' />
                        </span>
                    )}
                </span>
            )}
        </span>
    );
};
