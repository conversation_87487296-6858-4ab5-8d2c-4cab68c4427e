import React, {PropsWithChildren, useState} from 'react';
import {Button, ButtonProps, Chip} from '@mui/material';
import {ChipPropsColorOverrides} from '@mui/material/Chip/Chip';
import {OverridableStringUnion} from '@mui/types';
import {useThemeMode} from '@/hooks/useThemeMode';

interface KasCancellableButtonProps extends PropsWithChildren, ButtonProps {
    delayText: string;
    delay?: number;
    onClick: () => void;
}

export const KasCancellableButton = ({
    delayText,
    delay = 2500,
    children,
    onClick,
    ...props
}: KasCancellableButtonProps) => {
    const {cancelTimeout} = useThemeMode();
    const [isCancellable, setIsCancellable] = useState(false);
    const cancelButtonTimeout = React.useRef<NodeJS.Timeout | null>(null);

    const handleApproveClick = () => {
        if (cancelTimeout) {
            setIsCancellable(true);

            cancelButtonTimeout.current = setTimeout(() => {
                setIsCancellable(false);
                onClick();
            }, delay);
        } else {
            onClick();
        }
    };

    const handleCancel = () => {
        if (cancelButtonTimeout.current) {
            clearTimeout(cancelButtonTimeout.current);
            cancelButtonTimeout.current = null;
            setIsCancellable(false);
        }
    };

    return (
        <div style={{position: 'relative'}}>
            <Chip
                label={delayText}
                sx={{
                    width: props.fullWidth ? '100%' : 'auto',
                    opacity: isCancellable ? 1 : 0,
                    pointerEvents: isCancellable ? 'fill' : 'none',
                }}
                variant={props.variant === 'contained' ? 'filled' : 'outlined'}
                color={
                    (props.color as
                        | OverridableStringUnion<
                              'default' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning',
                              ChipPropsColorOverrides
                          >
                        | undefined) || 'primary'
                }
                onDelete={handleCancel}
            />
            {!isCancellable && (
                <Button
                    onClick={handleApproveClick}
                    {...props}
                    sx={{position: 'absolute', top: 0, left: 0, width: '100%', height: '100%'}}>
                    {children}
                </Button>
            )}
        </div>
    );
};
