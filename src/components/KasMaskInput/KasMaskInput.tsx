import React, {PropsWithChildren} from 'react';
import InputMask from '@mona-health/react-input-mask';
import {FormikValues} from 'formik';

interface KasMaskInputProps extends PropsWithChildren {
    name: string;
    formik: FormikValues;
    mask: string | Array<string | RegExp>;
    disabled?: boolean;
    maskPlaceholder?: string;
}

export const KasMaskInput = ({
    name,
    formik,
    mask,
    disabled = false,
    maskPlaceholder = '_',
    children,
}: KasMaskInputProps) => (
    <InputMask
        mask={mask}
        maskPlaceholder={maskPlaceholder}
        disabled={disabled}
        value={formik.values[name]}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}>
        {children}
    </InputMask>
);
