import './styles.scss';

import React, {PropsWithChildren} from 'react';
import {NO_RESULTS_SYMBOL} from '@/constants';
import { TestableProps } from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasStrikeProps extends PropsWithChildren, TestableProps {
    isStrike: boolean;
}

export const KasStrike = ({isStrike, children, testid}: KasStrikeProps) => {
    const strikethroughStyle = {textDecoration: 'line-through', color: '#aaa'};

    return (
        <span className='kas-strike' style={isStrike ? strikethroughStyle : {}} data-testid={testid}>
            {children ?? NO_RESULTS_SYMBOL}
        </span>
    );
};
