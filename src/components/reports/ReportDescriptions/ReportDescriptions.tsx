import React from 'react';
import {Grid2} from '@mui/material';
import {KasInfo} from '@/components';

interface CreditReportInfoDescriptionsProps {
    label: string;
    descriptions: string[] | undefined;
}

export const ReportDescriptions = ({label, descriptions}: CreditReportInfoDescriptionsProps) => (
    <Grid2 size={12}>
        <KasInfo label={label}>
            {descriptions?.length ? descriptions.map((desc, index) => <div key={index}>{desc}</div>) : null}
        </KasInfo>
    </Grid2>
);
