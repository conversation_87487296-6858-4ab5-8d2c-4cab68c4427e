import React from 'react';
import {Grid2} from '@mui/material';
import {KasInfo} from '@/components';
import {useReportHeaderSection} from './useReportHeaderSection';

interface ReportHeaderSectionProps {
    root: Element;
    reportingAgency?: string;
}

export const ReportHeaderSection = ({root, reportingAgency}: ReportHeaderSectionProps) => {
    const report = useReportHeaderSection(root);

    return (
        <Grid2 container p={1.5}>
            <Grid2 size={7}>
                <KasInfo label='Report Version:' isInline>
                    {reportingAgency && <>{reportingAgency}, </>}
                    {report.controlFileName}
                </KasInfo>
            </Grid2>
            <Grid2 size={5}>
                <KasInfo label='Tracking #:' isInline>
                    {report.trackingNumber}
                </KasInfo>
            </Grid2>
        </Grid2>
    );
};
