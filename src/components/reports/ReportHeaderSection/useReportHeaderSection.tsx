interface ReportHeaderSectionDataModel {
    controlFileName?: string | null;
    controlFileVersion?: string | null;
    trackingNumber?: string | null;
}

export const useReportHeaderSection = (root: Element): ReportHeaderSectionDataModel => {
    const req = root.querySelector('clear-products-request');
    const controlFileName = req?.querySelector('control-file-name')?.textContent;
    const controlFileVersion = req?.querySelector('control-file-version-number')?.textContent;
    const trackingNumber = root.querySelector('tracking-number')?.textContent;

    return {
        controlFileName,
        controlFileVersion,
        trackingNumber,
    };
};
