interface ReportHistorySectionDataModel {
    history?: Element | null;
    historyAction?: string | null;
    historyCodes?: string | null;
    historyDesc?: string | null;
    onlineLoansInquiries?: string | null;
    onlineLoansOpened?: string | null;
    daysWithOpenLoans90?: string | null;
    daysWithOpenLoansYear?: string | null;
    loansInCollections?: string | null;
    amountLoansInCollections?: string | null;
    lastCollectionDate?: string | null;
    loansChargedOff?: string | null;
    amountLoansChargedOff?: string | null;
    lastChargeOffDate?: string | null;
}

export const useReportHistorySection = (history: Element): ReportHistorySectionDataModel => {
    const historyAction = history?.querySelector('action')?.textContent;
    const historyCodes = history?.querySelector('deny-codes')?.textContent;
    const historyDesc = history?.querySelector('deny-descriptions')?.textContent?.replace(/,/g, '|');

    const summary = history?.querySelector('summary-recent-history');
    const onlineLoansInquiries =
        summary?.querySelector('online-loan-inquiry-in-the-last-thirty-days')?.textContent || '0';
    const onlineLoansOpened =
        summary?.querySelector('online-loan-opened-in-the-last-year')?.textContent || '0';
    const daysWithOpenLoans90 =
        summary?.querySelector('days-with-open-loans-in-the-last-ninety-days')?.textContent || '0';
    const daysWithOpenLoansYear =
        summary?.querySelector('days-with-open-loans-in-the-last-year')?.textContent || '0';
    const loansInCollections = summary?.querySelector('loans-in-collections')?.textContent || '0';
    const amountLoansInCollections =
        summary?.querySelector('amount-loans-in-collections')?.textContent || '0';
    const lastCollectionDate = summary?.querySelector('last-collection')?.textContent;
    const loansChargedOff = summary?.querySelector('loans-in-collections')?.textContent || '0';
    const amountLoansChargedOff = summary?.querySelector('amount-loans-charged-off')?.textContent || '0';
    const lastChargeOffDate = summary?.querySelector('last-charge-off')?.textContent;

    return {
        history,
        historyAction,
        historyCodes,
        historyDesc,
        onlineLoansInquiries,
        onlineLoansOpened,
        daysWithOpenLoans90,
        daysWithOpenLoansYear,
        loansInCollections,
        amountLoansInCollections,
        lastCollectionDate,
        loansChargedOff,
        amountLoansChargedOff,
        lastChargeOffDate,
    };
};
