import React from 'react';
import {Typography, Grid2} from '@mui/material';
import {KasInfo} from '@/components';
import {useReportHistorySection} from './useReportHistorySection';
import {toCurrency} from '@/utils/FormatUtils';
import {ReportDivider} from '@/components/reports';

export const ReportHistorySection = ({history}: {history: Element}) => {
    const report = useReportHistorySection(history);

    return (
        <>
            <ReportDivider />
            <Grid2 size={4}>
                <KasInfo label='History Action:'>{report.historyAction}</KasInfo>
            </Grid2>
            <Grid2 size={3}>
                <KasInfo label='History Codes:'>{report.historyCodes}</KasInfo>
            </Grid2>
            <Grid2 size={5}>
                <KasInfo label='History Desc:'>{report.historyDesc}</KasInfo>
            </Grid2>

            <ReportDivider />
            <Grid2 size={12}>
                <Typography variant='subtitle1'>Additional Attributes:</Typography>
            </Grid2>
            <Grid2 size={4}>
                <Typography variant='subtitle1'>Online Loans:</Typography>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='New Inquiries:' isInline>
                    <abbr title='New Online Loans Inquiry in Last 30 Days'>
                        {report.onlineLoansInquiries}
                    </abbr>
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='New Loans:' isInline>
                    <abbr title='New Online Loans Opened in Last 1 Year'>{report.onlineLoansOpened}</abbr>
                </KasInfo>
            </Grid2>

            <Grid2 size={4}>
                <Typography variant='subtitle1'>Open Loans:</Typography>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Last 90 Days:' isInline>
                    <abbr title='Days With Open Loans in Last 90 Days'>{report.daysWithOpenLoans90}</abbr>
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Last 1 Year:' isInline>
                    <abbr title='Days With Open Loans in Last 1 Year'>{report.daysWithOpenLoansYear}</abbr>
                </KasInfo>
            </Grid2>

            <Grid2 size={4}>
                <Typography variant='subtitle1'>Collections:</Typography>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Count:' isInline>
                    <abbr title={`Last Date: ${report.lastCollectionDate}`}>{report.loansInCollections}</abbr>
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Amount:' isInline>
                    {toCurrency(report.amountLoansInCollections || 0)}
                </KasInfo>
            </Grid2>

            <Grid2 size={4}>
                <Typography variant='subtitle1'>Charge-off:</Typography>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Count:' isInline>
                    <abbr title={`Last Date: ${report.lastChargeOffDate}`}>{report.loansChargedOff}</abbr>
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Amount:' isInline>
                    {toCurrency(report.amountLoansChargedOff || 0)}
                </KasInfo>
            </Grid2>
        </>
    );
};
