import React from 'react';
import {Grid2} from '@mui/material';
import {KasInfo} from '@/components';
import {useReportMainActionSection} from './useReportMainActionSection';
import {ReportDescriptions} from '@/components/reports';

interface ReportMainActionSectionProps {
    root: Element;
}

export const ReportMainActionSection = ({root}: ReportMainActionSectionProps) => {
    const report = useReportMainActionSection(root);

    return (
        <>
            <Grid2 size={7}>
                <KasInfo label='Action:' isInline>
                    {report.action}
                </KasInfo>
            </Grid2>
            <Grid2 size={5}>
                <KasInfo label='Deny Codes:' isInline>
                    {report.denyCodes}
                </KasInfo>
            </Grid2>
            <ReportDescriptions label='Descriptions:' descriptions={report.descriptions} />
        </>
    );
};
