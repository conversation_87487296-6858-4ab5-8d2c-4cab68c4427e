interface ReportMainActionSectionDataModel {
    root?: Element;
    action?: string | null;
    denyCodes?: string | null;
    descriptions?: string[];
    error: string | null;
}

export const useReportMainActionSection = (root: Element): ReportMainActionSectionDataModel => {
    const action = root.querySelector('action')?.textContent;
    const denyCodes = root.querySelector('deny-codes')?.textContent;

    const descriptions = Array.from(
        root.querySelectorAll('deny-descriptions, exception-descriptions, identity-theft-prevention'),
    ).flatMap((desc) => desc.textContent?.split('|') || []);

    return {
        root,
        action,
        denyCodes,
        descriptions,
        error: null,
    };
};
