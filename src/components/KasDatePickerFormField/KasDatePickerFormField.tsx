import React, {useCallback, useState} from 'react';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DatePicker, DatePickerProps} from '@mui/x-date-pickers-pro';
import {FormikValues, getIn} from 'formik';
import {FormControl} from '@mui/material';
import dayjs, {Dayjs} from 'dayjs';

interface KasDatePickerFormFieldProps
    extends Omit<DatePickerProps<Dayjs>, 'value' | 'onChange' | 'renderInput'> {
    name: string;
    formik: FormikValues;
    disabled?: boolean;
    label?: string;
    showValidation?: boolean;
    dataFormat?: string;
}

export const KasDatePickerFormField = ({
    name,
    formik,
    disabled = false,
    label = 'Date',
    showValidation = true,
    dataFormat,
    ...rest
}: KasDatePickerFormFieldProps) => {
    const [open, setOpen] = useState(false);
    const [openEver, setOpenEver] = useState(false);

    const getDisplay = useCallback(
        (value: string) => {
            return dataFormat ? dayjs(value, dataFormat) : dayjs(value);
        },
        [dataFormat],
    );

    const getFormValue = useCallback(
        (value: Dayjs | null) => {
            return dataFormat ? value?.format(dataFormat) : value;
        },
        [dataFormat],
    );

    return (
        <FormControl fullWidth variant='outlined'>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                    open={open}
                    value={getDisplay(getIn(formik.values, name))}
                    onClose={() => {
                        setOpen(false);
                        setOpenEver(true);
                    }}
                    onChange={(value) => {
                        formik.setFieldValue(name, dayjs(value).isValid() ? getFormValue(value) : '');
                    }}
                    slotProps={{
                        textField: {
                            InputProps: {endAdornment: <></>},
                            variant: 'outlined',
                            size: 'small',
                            name,
                            label,
                            disabled,
                            error: showValidation && openEver && !!formik.errors[name],
                            helperText: showValidation && openEver && formik.errors[name],
                            onClick: () => setOpen(true),
                        },
                    }}
                    {...rest}
                />
            </LocalizationProvider>
        </FormControl>
    );
};
