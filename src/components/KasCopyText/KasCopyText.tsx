import './styles.scss';

import React, {PropsWithChildren} from 'react';
import {Tooltip} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import {useSnackbar} from '@/hooks/useSnackbar';
import {OverridableStringUnion} from '@mui/types';
import {SvgIconPropsSizeOverrides} from '@mui/material/SvgIcon/SvgIcon';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';
import Box from '@mui/material/Box';

interface KasCopyTextProps extends PropsWithChildren, TestableProps {
    textToCopy?: string | number;
    fontSize?: OverridableStringUnion<'inherit' | 'large' | 'medium' | 'small', SvgIconPropsSizeOverrides>;
}

export const KasCopyText = ({textToCopy, fontSize = 'inherit', testid, children}: KasCopyTextProps) => {
    const {showMessage} = useSnackbar();

    const handleCopyClick = async () => {
        try {
            const copyValue = (textToCopy ? textToCopy : children) as string;

            await navigator.clipboard.writeText(copyValue);
            showMessage('Copied!', 'success');
        } catch (err) {
            showMessage('Failed to copy', 'error');
        }
    };

    return (
        <div data-testid={`${testid}-copy-text`} className='kas-copy-text' onClick={handleCopyClick}>
            {children}
            <Tooltip title='Copy' arrow placement='top'>
                <Box className='kas-copy-text__icon' pl={0.5}>
                    <ContentCopyIcon fontSize={fontSize} />
                </Box>
            </Tooltip>
        </div>
    );
};
