import React, {createContext, useContext, useState} from 'react';

interface HeaderContextModel {
    openSettings: boolean;
    setOpenSettings: (value: boolean) => void;
}

const HeaderContext = createContext<HeaderContextModel | undefined>(undefined);

export const HeaderProvider = ({children}: {children: React.ReactNode}) => {
    const [openSettings, setOpenSettings] = useState(false);

    const value: HeaderContextModel = {
        openSettings,
        setOpenSettings,
    };

    return <HeaderContext.Provider value={value}>{children}</HeaderContext.Provider>;
};

export function useHeader() {
    const context = useContext(HeaderContext);
    if (!context) {
        throw new Error('useHeader must be used within HeaderProvider');
    }
    return context;
}
