import './styles.scss';

import React, {useMemo, useState} from 'react';
import MenuItem from '@mui/material/MenuItem';
import {Avatar, ListItemIcon, styled} from '@mui/material';
import Menu from '@mui/material/Menu';
import IconButton from '@mui/material/IconButton';
import {ArrowDropDown, FormatSize, LightMode, Logout, Nightlight, Settings} from '@mui/icons-material';
import {stringToColor} from '@/utils/AvatarUtils';
import {useRouter} from 'next/navigation';
import {useThemeMode} from '@/hooks/useThemeMode';
import {useAppSelector} from '@/lib/hooks';
import {selectUser} from '@/lib/slices/userSlice';
import {useHeader} from './../../useHeader';

const StyledListItemIcon = styled(ListItemIcon)(({theme: {palette}}) =>
    palette.mode === 'light'
        ? {
              color: '#ffffff',
          }
        : {},
);

const StyledMenuItem = styled(MenuItem)(({theme: {palette}}) => ({
    fontSize: 'var(--small-text-size)',
    ...(palette.mode === 'light' && {color: '#ffffff'}),
}));

const StyledAvatar = styled(Avatar)(() => ({
    width: 24,
    height: 24,
    fontSize: 'var(--small-text-size)',
    color: '#ffffff',
}));

export const HeaderUserMenu = () => {
    const {setOpenSettings} = useHeader();
    const user = useAppSelector(selectUser);
    const {comfortMode, themeMode, toggleThemeMode, toggleComfortMode} = useThemeMode();
    const router = useRouter();
    const [anchorEl, setAnchorEl] = useState(null);

    const handleMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const onChangeThemeMode = () => {
        toggleThemeMode();
        handleClose();
    };

    const onChangeComfortMode = () => {
        toggleComfortMode();
        handleClose();
    };

    const onOpenSettings = () => {
        setOpenSettings(true);
        handleClose();
    };

    const username = useMemo(() => {
        if (user.value) {
            const {first_name, last_name} = user.value;

            if (first_name || last_name) {
                return `${first_name || ''} ${last_name || ''}`;
            }
        }
        return '';
    }, [user.value]);

    const userSymbols = useMemo(() => {
        const usernameArr = username.split(' ');

        switch (usernameArr.length) {
            case 1:
                return usernameArr[0][0];
            case 2:
                return `${usernameArr[0][0]}${usernameArr[1][0]}`;
            default:
                return '';
        }
    }, [username]);

    return (
        <div className='kas-header-user-menu'>
            <IconButton color='inherit' sx={{fontSize: 'var(--small-text-size)'}} onClick={handleMenu}>
                {username ? (
                    <>
                        <StyledAvatar
                            alt={username}
                            sx={{
                                bgcolor: stringToColor(username),
                            }}>
                            {userSymbols}
                        </StyledAvatar>
                        <div className='kas-header-user-menu__username'>{username}</div>
                    </>
                ) : (
                    <StyledAvatar />
                )}

                <ArrowDropDown />
            </IconButton>
            <Menu
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                sx={{
                    '& .MuiPaper-root': {
                        ...(themeMode === 'light' && {
                            backgroundColor: '#28262c',
                            borderColor: '#373737',
                        }),
                    },
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}>
                <StyledMenuItem onClick={onOpenSettings}>
                    <StyledListItemIcon>
                        <Settings fontSize='small' />
                    </StyledListItemIcon>
                    Settings
                </StyledMenuItem>
                <StyledMenuItem onClick={onChangeThemeMode}>
                    <StyledListItemIcon>
                        {themeMode === 'light' ? (
                            <Nightlight fontSize='small' />
                        ) : (
                            <LightMode fontSize='small' />
                        )}
                    </StyledListItemIcon>
                    {themeMode === 'light' ? 'Dark' : 'Light'} Mode
                </StyledMenuItem>
                <StyledMenuItem onClick={onChangeComfortMode}>
                    <StyledListItemIcon>
                        <FormatSize fontSize='small' />
                    </StyledListItemIcon>
                    {comfortMode ? 'Default' : 'Comfort'} Mode
                </StyledMenuItem>
                <StyledMenuItem onClick={() => router.replace('../login')}>
                    <StyledListItemIcon>
                        <Logout fontSize='small' />
                    </StyledListItemIcon>
                    Logout
                </StyledMenuItem>
            </Menu>
        </div>
    );
};
