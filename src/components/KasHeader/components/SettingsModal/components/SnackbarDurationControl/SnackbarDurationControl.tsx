import React, {useMemo, useState} from 'react';
import {Button, FormControl, FormLabel, Grid2} from '@mui/material';
import {useThemeMode} from '@/hooks/useThemeMode';
import Slider from '@mui/material/Slider';

export const SnackbarDurationControl = () => {
    const {snackbarHideDuration, updateSnackbarHideDuration} = useThemeMode();

    const snackbarHideDurationSec = useMemo(() => snackbarHideDuration / 1000, [snackbarHideDuration]);
    const [value, setValue] = useState(snackbarHideDurationSec);

    const handleSliderChange = (event: Event, newValue: number | number[]) => {
        setValue(newValue as number);
    };

    const handleSaveDuration = () => {
        updateSnackbarHideDuration(value * 1000);
    };

    return (
        <FormControl>
            <FormLabel>
                Snackbar Hide Duration: {snackbarHideDurationSec} second
                {snackbarHideDurationSec > 1 ? 's' : ''}
            </FormLabel>
            <Grid2 container spacing={2} pt={1} alignItems='center'>
                <Grid2 size={10}>
                    <Slider
                        value={value}
                        step={1}
                        min={1}
                        max={10}
                        valueLabelDisplay='on'
                        valueLabelFormat={(value: number) => value + 's'}
                        onChange={handleSliderChange}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='contained'
                        size='small'
                        disabled={snackbarHideDurationSec === value}
                        onClick={handleSaveDuration}>
                        Save
                    </Button>
                </Grid2>
            </Grid2>
        </FormControl>
    );
};
