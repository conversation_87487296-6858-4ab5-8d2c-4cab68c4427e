import React from 'react';
import {KasModal} from '@/components';
import {useHeader} from '@/components/KasHeader/useHeader';
import {Divider, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, Stack} from '@mui/material';
import {useThemeMode} from '@/hooks/useThemeMode';
import {SnackbarDurationControl} from './components';

export const SettingsModal = () => {
    const {openSettings, setOpenSettings} = useHeader();
    const {cancelTimeout, comfortMode, themeMode, toggleThemeMode, toggleComfortMode, toggleCancelTimeout} =
        useThemeMode();

    return (
        <KasModal
            title='User Settings'
            size='small'
            open={openSettings}
            onClose={() => setOpenSettings(false)}>
            <Stack spacing={1}>
                <FormControl>
                    <FormLabel id='theme-group-label'>Theme:</FormLabel>
                    <RadioGroup
                        row
                        aria-labelledby='theme-group-label'
                        value={themeMode}
                        onChange={() => toggleThemeMode()}>
                        <FormControlLabel value='light' control={<Radio />} label='Light' />
                        <FormControlLabel value='dark' control={<Radio />} label='Dark' />
                    </RadioGroup>
                </FormControl>
                <Divider />
                <FormControl>
                    <FormLabel id='mode-group-label'>Content Mode:</FormLabel>
                    <RadioGroup
                        row
                        aria-labelledby='mode-group-label'
                        value={comfortMode}
                        onChange={() => toggleComfortMode()}>
                        <FormControlLabel value={false} control={<Radio />} label='Default' />
                        <FormControlLabel value={true} control={<Radio />} label='Comfort' />
                    </RadioGroup>
                </FormControl>
                <Divider />
                <FormControl>
                    <FormLabel id='cancel-time-group-label'>Cancel Timeout:</FormLabel>
                    <RadioGroup
                        row
                        aria-labelledby='cancel-time-group-label'
                        value={cancelTimeout}
                        onChange={() => toggleCancelTimeout()}>
                        <FormControlLabel value={false} control={<Radio />} label='Off' />
                        <FormControlLabel value={true} control={<Radio />} label='On' />
                    </RadioGroup>
                </FormControl>
                <Divider />
                <SnackbarDurationControl />
            </Stack>
        </KasModal>
    );
};
