import React from 'react';
import AppBar from '@mui/material/AppBar';
import {Kas<PERSON>ogo, KasLogoSize} from '@/components';
import {HeaderUserMenu, HeaderViewDropdown, SettingsModal} from './components';
import {Stack, styled} from '@mui/material';
import {HeaderProvider} from '@/components/KasHeader/useHeader';

const StyledAppBar = styled(AppBar)(({theme: {palette}}) =>
    palette.mode === 'light'
        ? {
              backgroundColor: '#28262c',
              color: '#ffffff',
          }
        : {},
);

export const KasHeader = () => {
    return (
        <HeaderProvider>
            <StyledAppBar position='static'>
                <Stack flexDirection='row' justifyContent='space-between' py={2} px={2.5}>
                    <Stack flexDirection='row' alignItems='center' columnGap={3}>
                        <KasLogo size={KasLogoSize.Small} />
                        <HeaderViewDropdown />
                    </Stack>
                    <HeaderUserMenu />
                </Stack>
                <SettingsModal />
            </StyledAppBar>
        </HeaderProvider>
    );
};
