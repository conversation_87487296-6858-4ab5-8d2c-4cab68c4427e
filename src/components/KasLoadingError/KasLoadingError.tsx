import './styles.scss';

import React from 'react';
import {InfoOutlined} from '@mui/icons-material';
import {KasLink} from '@/components';
import {DEFAULT_ERROR_MSG} from '@/constants';

interface KasLoadingErrorProps {
    error: string;
    view?: 'outlined' | 'contained';
    onTryAgain?: () => void;
}

export const KasLoadingError = ({error, view = 'outlined', onTryAgain}: KasLoadingErrorProps) => (
    <div className={`kas-loading-error ${view}`}>
        <InfoOutlined fontSize='large' color={'error'} />
        <div className='kas-loading-error__content'>
            <p>{error || DEFAULT_ERROR_MSG}</p>
            {onTryAgain && <KasLink onClick={onTryAgain}>Try Again</KasLink>}
        </div>
    </div>
);
