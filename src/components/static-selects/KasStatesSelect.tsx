import React from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import {STATES} from './data';
import {FormikValues} from 'formik';

interface KasStatesSelectProps {
    name: string;
    formik: FormikValues;
    label?: string;
    disabled?: boolean;
}

export const KasStatesSelect = ({name, formik, label = 'Select', disabled = false}: KasStatesSelectProps) => {
    const currentValue = STATES.find((state) => state.id === formik.values[name]) || null;

    return (
        <Autocomplete
            size='small'
            options={STATES}
            disabled={disabled}
            value={currentValue}
            getOptionLabel={(option) => option.text}
            filterOptions={(options, state) => {
                const searchText = state.inputValue.toLowerCase();

                return options.filter(
                    (option) =>
                        option.text.toLowerCase().includes(searchText) ||
                        option.id.toLowerCase().includes(searchText),
                );
            }}
            isOptionEqualToValue={(option, value) => option.id === value?.id}
            onChange={(_, newValue) => {
                formik.setFieldValue(name, newValue?.id || '');
            }}
            onBlur={() => {
                formik.setFieldTouched(name);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    variant='outlined'
                    label={label}
                    error={!!formik.errors[name] && formik.touched[name]}
                    helperText={formik.touched[name] && formik.errors[name]}
                />
            )}
        />
    );
};
