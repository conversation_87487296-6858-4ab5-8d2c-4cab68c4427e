import React from 'react';
import {STATES} from './data';
import {FormikValues} from 'formik';
import {KasMultipleSelect} from '@/components';

interface KasStatesSelectProps {
    name: string;
    formik: FormikValues;
    disabled?: boolean;
}

export const KasMultipleStatesSelect = ({name, formik, disabled = false}: KasStatesSelectProps) => {
    return (
        <KasMultipleSelect
            formik={formik}
            name={name}
            placeholder='States'
            options={STATES}
            disabled={disabled}
        />
    );
};
