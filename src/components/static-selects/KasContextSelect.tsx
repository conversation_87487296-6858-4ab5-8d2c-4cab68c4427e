import React from 'react';
import {KasSelect} from '@/components';
import {FormikValues} from 'formik';
import MenuItem from '@mui/material/MenuItem';
import {CONTEXT_OPTIONS} from './data';

interface KasContextSelectProps {
    formik: FormikValues;
    name: string;
    label?: string;
    disabled?: boolean;
}

export const KasContextSelect = ({
    formik,
    name,
    label = 'Context',
    disabled = false,
}: KasContextSelectProps) => {
    return (
        <KasSelect
            name={name}
            label={label}
            disabled={disabled}
            value={formik.values[name]}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}>
            {CONTEXT_OPTIONS.map(({id, value, label}) => (
                <MenuItem key={id} value={value}>
                    {label}
                </MenuItem>
            ))}
        </KasSelect>
    );
};
