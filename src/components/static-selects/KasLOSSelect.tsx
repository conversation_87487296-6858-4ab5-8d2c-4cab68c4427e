import React from 'react';
import {KasSelect} from '@/components';
import {FormikValues} from 'formik';
import MenuItem from '@mui/material/MenuItem';
import {LOS_OPTIONS} from './data';

interface KasLOSSelectProps {
    formik: FormikValues;
    name: string;
    label?: string;
    disabled?: boolean;
}

export const KasLOSSelect = ({
    formik,
    name,
    label = 'Loan Origination',
    disabled = false,
}: KasLOSSelectProps) => {
    return (
        <KasSelect
            name={name}
            label={label}
            disabled={disabled}
            value={formik.values[name]}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}>
            {LOS_OPTIONS.map(({id, value, label}) => (
                <MenuItem key={id} value={value}>
                    {label}
                </MenuItem>
            ))}
        </KasSelect>
    );
};
