import React from 'react';
import {Checkbox, ListItemText, SelectChangeEvent} from '@mui/material';
import {KasSelect} from '@/components';
import {FormikValues} from 'formik';
import MenuItem from '@mui/material/MenuItem';
import {LOS_OPTIONS} from './data';

interface LOSSelectProps {
    formik: FormikValues;
    disabled?: boolean;
}

export const KasLOSMultipleSelect = ({formik, disabled = false}: LOSSelectProps) => {
    const handleLOSChange = (event: SelectChangeEvent<string[]>) => {
        formik.setFieldValue('los', event.target.value);
    };

    return (
        <KasSelect
            disabled={disabled}
            label='LOS'
            multiple
            value={formik.values.los}
            onChange={handleLOSChange}
            renderValue={(selected: string[]) => selected.join(', ')}>
            {LOS_OPTIONS.map((option) => (
                <MenuItem key={option.id} value={option.value}>
                    <Checkbox
                        size='small'
                        sx={{
                            marginY: '-8px',
                            marginLeft: '-8px',
                        }}
                        checked={formik.values.los.includes(option.value)}
                    />
                    <ListItemText primary={option.label} />
                </MenuItem>
            ))}
        </KasSelect>
    );
};
