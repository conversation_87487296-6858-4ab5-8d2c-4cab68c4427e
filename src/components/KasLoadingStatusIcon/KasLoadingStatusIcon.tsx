import './styles.scss';

import React from 'react';
import {CheckCircleRounded, InfoOutlined} from '@mui/icons-material';
import {CircularProgress, Tooltip} from '@mui/material';

interface KasLoadingStatusIconProps {
    loading: boolean;
    loadingError?: string;
    success?: boolean;
}

export const KasLoadingStatusIcon = ({loading, loadingError, success = false}: KasLoadingStatusIconProps) => (
    <div className='kas-loading-status-icon'>
        {loading && <CircularProgress size={20} />}
        {loadingError && !loading && (
            <Tooltip title={loadingError}>
                <InfoOutlined color='error' />
            </Tooltip>
        )}
        {success && <CheckCircleRounded color='success' />}
    </div>
);
