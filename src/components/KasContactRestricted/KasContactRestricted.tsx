import React, {PropsWithChildren} from 'react';
import {useTheme} from '@mui/material';

interface KasContactRestrictedProps extends PropsWithChildren {
    restricted: string | null;
}

export const KasContactRestricted = ({restricted, children}: KasContactRestrictedProps) => {
    const {palette} = useTheme();

    return (
        <abbr
            style={{
                color: restricted ? palette.action.disabled : 'inherit',
            }}
            title={restricted ? `Contact Restricted ${restricted}` : undefined}>
            {children}
        </abbr>
    );
};
