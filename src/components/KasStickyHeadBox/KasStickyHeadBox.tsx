import './styles.scss';

import React from 'react';
import Box from '@mui/material/Box';
import {BoxProps} from '@mui/material';

interface KasStickyHeadBoxProps extends BoxProps {
    isSticky: boolean;
}

export const KasStickyHeadBox = ({isSticky, ref, children, ...rest}: KasStickyHeadBoxProps) => {
    return (
        <Box {...rest} ref={ref} className={`kas-sticky-head-box ${isSticky ? 'sticky' : ''}`}>
            <div className={rest.className}>{children}</div>
        </Box>
    );
};
