.kas-sticky-head-box {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--color-grey);
  transition: border-bottom 0.3s ease;
  border-bottom: 1px solid transparent;

  &:before,
  &:after {
    position: absolute;
    top: 0;
    width: 20px;
    height: 100%;
    border-bottom: 1px solid transparent;
    transition: border-bottom 0.3s ease;
    content: '';
  }

  &:before {
    right: 100%;
  }

  &:after {
    left: 100%;
  }

  &.sticky {
    border-bottom-color: var(--color-divider);

    &:before,
    &:after {
      border-bottom-color: var(--color-divider);
    }
  }
}
