import './styles.scss';

import React from 'react';
import {TooltipProps} from 'recharts';
import {Divider, Stack, Typography} from '@mui/material';

export interface KasCustomTooltipProps extends TooltipProps<string | number, string | number> {
    labelKey?: string;
    labelFormatter?: (value: any) => React.ReactNode;
    valueFormatter?: (value: any, dataKey?: string | number) => React.ReactNode;
    showTotal?: boolean;
}

export const KasCustomTooltip = ({
    active,
    payload,
    labelKey = 'label',
    labelFormatter,
    valueFormatter,
    showTotal = true,
}: KasCustomTooltipProps) => {
    if (active && payload && payload.length) {
        const labelValue = payload[0].payload?.[labelKey];
        return (
            <Stack spacing={1} sx={{minWidth: 120}} className='kas-custom-tooltip'>
                <Typography variant='body1'>
                    {labelFormatter ? labelFormatter(labelValue) : labelValue}
                </Typography>
                <Divider />
                {payload.map((item) => (
                    <div key={item.dataKey as string} className='kas-custom-tooltip__item'>
                        <span style={{color: item.color, fontWeight: 500}}>{item.name}:</span>
                        {valueFormatter ? valueFormatter(item.value, item.dataKey) : item.value}
                    </div>
                ))}
                {showTotal && (
                    <div className='kas-custom-tooltip__item'>
                        <span style={{fontWeight: 500}}>TOTAL:</span>
                        {valueFormatter
                            ? valueFormatter(payload.reduce((acc, item) => acc + Number(item.value || 0), 0))
                            : payload.reduce((acc, item) => acc + Number(item.value || 0), 0)}
                    </div>
                )}
            </Stack>
        );
    }
    return null;
};
