import './styles.scss';

import React from 'react';
import logo from './sources/kashable-logo-light.svg';
import Image from 'next/image';
import {Link} from '@mui/material';

export enum KasLogoSize {
    Small = 'small',
    Medium = 'medium',
}

interface KasLogoProps {
    size?: KasLogoSize;
    href?: string;
}

export const KasLogo = ({size = KasLogoSize.Medium, href}: KasLogoProps) => {
    const className = `kas-logo ${size}`;
    const LogoImage = <Image src={logo} alt='Kashable Portal' priority />;

    return href ? (
        <Link href={href} className={className}>
            {LogoImage}
        </Link>
    ) : (
        <div className={className}>{LogoImage}</div>
    );
};
