import './styles.scss';

import React, {PropsWithChildren, ReactNode} from 'react';
import {Typography, useTheme} from '@mui/material';
import {NO_RESULTS_SYMBOL} from '@/constants';

interface KasInfoProps extends PropsWithChildren {
    label: ReactNode;
    isInline?: boolean;
}

export const KasInfo = ({label, isInline = false, children}: KasInfoProps) => {
    const {palette} = useTheme();

    return (
        <div className={`kas-info ${isInline ? 'inline' : ''}`}>
            <Typography variant='body1' color={palette.text.disabled}>
                {label}
            </Typography>
            <Typography variant='body1' pl={isInline ? 0.5 : 0} component='div' color={palette.text.primary}>
                {children ?? NO_RESULTS_SYMBOL}
            </Typography>
        </div>
    );
};
