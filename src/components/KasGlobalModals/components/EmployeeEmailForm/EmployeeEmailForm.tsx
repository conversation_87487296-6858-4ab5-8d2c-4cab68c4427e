import React, {useState} from 'react';
import {GlobalModalEmployeeEmailProps, KasModalFooter, useGlobalModal} from '@/components';
import {TextField, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {EmployeeEmailFormValues, validationSchema} from './schema';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingEmployeeProfileEmailModel} from '@/screens/UnderwritingScreen/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useDispatch} from 'react-redux';
import {eventUpdateEmployeeEmails} from '@/lib/slices/eventSlice';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const EmployeeEmailForm = ({
    employeeId,
    email = '',
    method,
    gid,
    source,
}: GlobalModalEmployeeEmailProps) => {
    const dispatch = useDispatch();
    const {showMessage} = useSnackbar();
    const {hideGlobalModal} = useGlobalModal();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: EmployeeEmailFormValues) => {
        const url = `/api/secured/underwriting/employee-profile/${employeeId}/emails`;
        const payload: Partial<UnderwritingEmployeeProfileEmailModel> = {
            email_name: values.email,
        };

        if (gid) {
            payload.gid = gid;
        }

        if (source) {
            payload.source = source;
        }

        setSubmitting(true);

        const response = await apiRequest(gid ? `${url}/${gid}` : url, {
            method,
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            dispatch(eventUpdateEmployeeEmails());
            onClose();
        }

        setSubmitting(false);
    };

    const onClose = () => {
        hideGlobalModal();
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            email,
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='email'
                        disabled={submitting}
                        defaultValue={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Email'
                        variant='outlined'
                        error={!!formik.errors.email && formik.touched.email}
                        helperText={formik.touched.email && formik.errors.email}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
