import React, {useEffect, useMemo, useState} from 'react';
import {<PERSON>rid2, <PERSON><PERSON>, TextField, InputAdornment, FormControlLabel, Switch, CircularProgress} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasIn<PERSON>, KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {getValidationSchema, InitPNCFormValues} from './schema';

import {useFormik} from 'formik';
import {CollectionsDebitCardPNCModel, Completable, pending, DataStateInterface} from '@/interfaces';
import {toCurrency} from '@/utils/FormatUtils';
import {LoanSettlementInfoDTO, PayerExpressConfigDTO} from '@/models';
import {AlternativePaymentModeEnum} from '@/models/employeePaymentEmailDTO';
import { getDefaultState, getLoadedState, getLoadingState } from '@/utils/DataStateUtils';

interface InitPNCFormProps {
    loanId: number;
    payoffAmount?: number;
    onClose: () => void;
    onPrev: () => void;
    onSuccess: (value: CollectionsDebitCardPNCModel | undefined) => void;
}

export const InitPNCForm = ({loanId, payoffAmount, onClose, onPrev, onSuccess}: InitPNCFormProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);

    const [loanSettlementInfo, setLoanSettlementInfo] = useState<DataStateInterface<LoanSettlementInfoDTO>>(getDefaultState());
    const [config, setConfig] = useState<DataStateInterface<PayerExpressConfigDTO>>(getDefaultState());

    useEffect(() => {
        const fetchSettlement = async () => {
            const url = `/api/secured/collections/loan/${loanId}/settlement`;
            setLoanSettlementInfo(getLoadingState(loanSettlementInfo));
            const response: Completable<LoanSettlementInfoDTO> = await apiRequest(url);
            setLoanSettlementInfo(getLoadedState(response));
        };
        fetchSettlement();
    }, [loanId]);

    const onSubmit = async (values: InitPNCFormValues) => {
        const params = new URLSearchParams({
            amount: values.amount.toString()
        });
        
        if (values.settlementEnabled) {
            params.append('mode', AlternativePaymentModeEnum.SETTLEMENT);
        }
        
        const url = `/api/secured/collections/loan/${loanId}/actions/init-payment?${params.toString()}`;

        setSubmitting(true);

        const response: Completable<CollectionsDebitCardPNCModel> = await apiRequest(url);

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            onSuccess(response.value);
        }

        setSubmitting(false);
    };

    const maxPayment = useMemo(() => {
        if (payoffAmount == null) {
            return config.data?.max_payment || 0;
        }
        return Math.min(config.data?.max_payment || 0, payoffAmount);
    }, [config.data?.max_payment, payoffAmount]);

    const validationSchema = useMemo(() => getValidationSchema(maxPayment), [maxPayment]);

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            amount: '',
            settlementEnabled: false,
        },
        onSubmit,
        validationSchema
    });

    // Fetch PNC config based on settlement state
    useEffect(() => {
        const fetchConfig = async () => {
            const url = `/api/secured/collections/loan/${loanId}/pnc-config`;
            const params = new URLSearchParams();
            
            if (formik.values.settlementEnabled) {
                params.append('mode', AlternativePaymentModeEnum.SETTLEMENT);
            }
            
            setConfig(getLoadingState(config));
            const response: Completable<PayerExpressConfigDTO> = await apiRequest(`${url}?${params.toString()}`);
            setConfig(getLoadedState(response));
        };

        fetchConfig();
    }, [loanId, formik.values.settlementEnabled]);

    // Re-validate form when validation schema changes
    useEffect(() => {
        formik.validateForm();
    }, [validationSchema]);

    const formLoading = useMemo(
        () => config.loading || !config.data?.service_fee || !config.data?.max_payment,
        [config.loading, config.data?.service_fee, config.data?.max_payment]
    );

    const totalAmount = useMemo(
        () => {
            const serviceFee = config.data?.service_fee;
            if (!serviceFee) {
                return null;
            }
            const amount = parseFloat(formik.values.amount) || 0;
            return (amount + serviceFee).toFixed(2);
        },
        [formik.values.amount, config.data?.service_fee]
    );

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>
                        Make sure you turn off the recording before accepting a debit card number over the
                        phone
                    </Alert>
                </Grid2>
                {loanSettlementInfo.loading ? (
                    <Grid2 size={12}>
                        <CircularProgress size={20} />
                    </Grid2>
                ) : (!!loanSettlementInfo.data?.settlement_id || loanSettlementInfo.data?.contact_restriction_settlement) && (
                    <Grid2 size={12}> 
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={formik.values.settlementEnabled}
                                    onChange={(e) => formik.setFieldValue('settlementEnabled', e.target.checked)}
                                    disabled={submitting || config.loading}
                                />
                            }
                            label='Settlement Payment'
                        />
                    </Grid2>
                )}
                {formLoading ? (
                    <Grid2 size={12}>
                        <CircularProgress size={20} />
                    </Grid2>
                ) : (
                    <>
                        <Grid2 size={12}>
                            <TextField
                                fullWidth
                                size='small'
                                name='amount'
                                disabled={submitting}
                                value={formik.values.amount}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                label='Amount'
                                variant='outlined'
                                type='number'
                                slotProps={{
                                    input: {
                                        startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                                    },
                                    htmlInput: {
                                        step: '0.01',
                                    },
                                }}
                                error={!!formik.errors.amount && formik.touched.amount}
                                helperText={formik.touched.amount && formik.errors.amount}
                            />
                        </Grid2>
                        <Grid2 size={12}>
                            <KasInfo label='Fee:' isInline>
                                {toCurrency(config.data?.service_fee)}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={12}>
                            <KasInfo label='Total:' isInline>
                                {toCurrency(totalAmount)}
                            </KasInfo>
                        </Grid2>
                    </>
                )}
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        submitText='Submit'
                        customButtonText='Prev'
                        onCustomClick={onPrev}
                        disabled={!formik.isValid || submitting || formLoading}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
