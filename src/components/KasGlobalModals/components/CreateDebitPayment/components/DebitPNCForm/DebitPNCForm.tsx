import React, {useEffect, useRef} from 'react';
import {CollectionsDebitCardPNCModel} from '@/interfaces';

const PNC_URL = process.env.NEXT_PUBLIC_PNC_URL;

interface DebitPNCFormProps {
    data: CollectionsDebitCardPNCModel;
    onClose: () => void;
}

export const DebitPNCForm = ({data, onClose}: DebitPNCFormProps) => {
    const formRef = useRef<HTMLFormElement | null>(null);

    useEffect(() => {
        const form = formRef.current;

        if (form) {
            form.submit();
            onClose();
        }
    }, []);

    return (
        <form hidden ref={formRef} target='_blank' action={PNC_URL} method='post'>
            <input readOnly name='Loan ID' value={data.loan_id} />
            <input readOnly name='SSN Last 4' value={data.ssn4} />
            <input readOnly name='First Name' value={data.first_name} />
            <input readOnly name='Last Name' value={data.last_name} />
            <input readOnly name='Amount Due' value={data.amount} />
            <input readOnly name='Address' value={data.street1} />
            <input readOnly name='Address 2' value={data.street2} />
            <input readOnly name='City' value={data.city} />
            <input readOnly name='State' value={data.state_cd} />
            <input readOnly name='Zip' value={data.zip} />
            <input readOnly name='key' value={data.key} />
            <input readOnly name='ID' value={data.id} />
            <input type='submit' />
        </form>
    );
};
