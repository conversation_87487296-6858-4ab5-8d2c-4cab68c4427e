import React, {ChangeEvent, useEffect, useState} from 'react';
import {Checkbox, FormControlLabel, SelectChangeEvent, Typography, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {apiRequest} from '@/utils/AxiosUtils';
import {CollectionsDebitEmailModel} from '@/interfaces';
import {
    KasLoading,
    Kas<PERSON>oadingError,
    KasModalFooter,
    KasNoResults,
    KasSelect,
    KasSwitch,
    KasSwitchWhen,
} from '@/components';
import {EmailPreview} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {useFormik} from 'formik';

interface EmailPreviewFormProps {
    loanId: number;
    onClose: () => void;
    onSkip: () => void;
}

export const EmailPreviewForm = ({loanId, onClose, onSkip}: EmailPreviewFormProps) => {
    const url = `/api/secured/collections/loan/${loanId}/actions/email/debit-terms`;
    const {showMessage} = useSnackbar();
    const [selectedItem, setSelectedItem] = useState<CollectionsDebitEmailModel>();
    const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
    const [state, setState] = useState(getDefaultState<CollectionsDebitEmailModel[]>());
    const [submitting, setSubmitting] = useState(false);

    const handleChange = (event: SelectChangeEvent) => {
        const item = state.data?.find(({email_type}) => email_type === event.target.value);

        setSelectedItem(item);
        setSelectedEmails(item?.recipients.map((item) => item.email || '') || []);
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedEmails.includes(value)) {
            setSelectedEmails(selectedEmails.filter((email) => email !== value));
        } else {
            setSelectedEmails([...selectedEmails, value]);
        }
    };

    const onSubmit = async () => {
        setSubmitting(true);
        showMessage('Sending email...', 'info');
        const recipients = selectedItem?.recipients.filter(
            (item) => !!item.email && selectedEmails.includes(item.email),
        );
        const response = await apiRequest(`${url}?preview=false`, {
            method: 'POST',
            body: JSON.stringify({...selectedItem, recipients}),
        });

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            showMessage('Email sent successfully', 'success');
            onSkip();
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(url);
        setState(getLoadedState(response));
        if (response.value) {
            const values: CollectionsDebitEmailModel[] = response.value;

            if (values.length) {
                setSelectedItem(values[0]);
                setSelectedEmails(values[0].recipients.map((item) => item.email || ''));
            }
        }
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <KasLoadingError error={state.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data && state.data.length < 1}>
                <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                <form onSubmit={formik.handleSubmit}>
                    <Grid2 container spacing={1}>
                        <Grid2 size={3}>
                            <KasSelect
                                label='Template'
                                value={selectedItem?.email_type ? selectedItem.email_type : ''}
                                onChange={handleChange}
                                renderValue={() => (selectedItem ? selectedItem.email_type : '')}>
                                {state.data?.map((item) => (
                                    <MenuItem key={item.email_type} value={item.email_type}>
                                        {item.email_type}
                                    </MenuItem>
                                ))}
                            </KasSelect>
                        </Grid2>
                        {selectedItem && (
                            <Grid2 size={12}>
                                <Typography variant='subtitle1'>Recipients</Typography>
                                {selectedItem.recipients.map((item, index) => (
                                    <div key={index}>
                                        <FormControlLabel
                                            label={item.email}
                                            control={
                                                <Checkbox
                                                    size='small'
                                                    value={item.email}
                                                    checked={
                                                        !!item.email && selectedEmails.includes(item.email)
                                                    }
                                                    onChange={handleChangeRecipients}
                                                    inputProps={{'aria-label': 'controlled'}}
                                                />
                                            }
                                        />
                                    </div>
                                ))}
                                <Typography variant='subtitle1' mb={1}>
                                    Preview
                                </Typography>
                                <EmailPreview
                                    url={url + '?preview=true'}
                                    payload={JSON.stringify(selectedItem)}
                                />
                            </Grid2>
                        )}
                        <Grid2 size={12}>
                            <KasModalFooter
                                loading={submitting}
                                submitText='Send Email'
                                customButtonText='Skip'
                                onCustomClick={onSkip}
                                disabled={!selectedEmails.length || submitting}
                                onCancel={onClose}
                            />
                        </Grid2>
                    </Grid2>
                </form>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
