import React, {useEffect} from 'react';
import {KasAutocompleteField} from '@/components';
import {Button, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {useVerbalACH} from '@/components/KasGlobalModals/components';
import {Add} from '@mui/icons-material';
import {VerbalACHModal} from '@/components/KasGlobalModals/components/VerbalACH/interfaces';

export const BankForm = () => {
    const {banksState, setOpenModal, updateVerbalACHPayload, verbalACHPayload, agreementState} =
        useVerbalACH();

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            bank: (verbalACHPayload?.bank_id || '').toString(),
        },
        onSubmit: () => {},
        validationSchema,
    });

    useEffect(() => {
        updateVerbalACHPayload({bank_id: formik.values.bank ? Number(formik.values.bank) : undefined});
    }, [formik.values.bank]);

    return (
        <Grid2 container spacing={2}>
            <Grid2 size={4}>
                <KasAutocompleteField
                    disabled={agreementState.loading}
                    name='bank'
                    label='Bank'
                    options={banksState.data || []}
                    formik={formik}
                />
            </Grid2>
            <Grid2 size={8}>
                <Button
                    variant='outlined'
                    size='small'
                    title='Add Bank'
                    disabled={agreementState.loading}
                    onClick={() =>
                        setOpenModal({type: VerbalACHModal.Add_Bank_Account, props: {bank: null}})
                    }>
                    <Add fontSize='small' />
                </Button>
            </Grid2>
        </Grid2>
    );
};
