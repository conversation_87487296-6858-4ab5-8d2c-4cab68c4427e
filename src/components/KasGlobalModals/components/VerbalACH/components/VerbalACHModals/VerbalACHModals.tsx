import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {useVerbalACH} from '@/components/KasGlobalModals/components';
import {VerbalACHModal} from '@/components/KasGlobalModals/components/VerbalACH/interfaces';
import {ChangeBankAccount, CreateEditBankAccountForm} from './components';

export const VerbalACHModals = () => {
    const {openModal, setOpenModal, addBankAccount} = useVerbalACH();

    const onClose = () => setOpenModal(null);

    const size = useMemo(() => {
        switch (openModal?.type) {
            case VerbalACHModal.Add_Bank_Account:
            case VerbalACHModal.Change_Bank_Account:
                return 'small';
            default:
                return 'medium';
        }
    }, [openModal?.type]);

    const title = useMemo(() => {
        switch (openModal?.type) {
            case VerbalACHModal.Add_Bank_Account:
            case VerbalACHModal.Change_Bank_Account:
                return openModal.type;
            default:
                return '';
        }
    }, [openModal?.type]);

    const renderModalContent = useMemo(() => {
        switch (openModal?.type) {
            case VerbalACHModal.Add_Bank_Account:
                return (
                    <CreateEditBankAccountForm
                        {...openModal.props}
                        onClose={onClose}
                        onSubmitHandler={addBankAccount}
                    />
                );
            case VerbalACHModal.Change_Bank_Account:
                return <ChangeBankAccount />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal size={size} title={title} open={!!openModal} onClose={onClose}>
            {renderModalContent}
        </KasModal>
    );
};
