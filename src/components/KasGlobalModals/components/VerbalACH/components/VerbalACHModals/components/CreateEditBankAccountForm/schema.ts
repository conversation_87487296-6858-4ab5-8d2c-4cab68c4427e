import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG, DIGITS_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    abaNumber: Yup.string()
        .required(DEFAULT_VALIDATION_MSG)
        .matches(/^\d+$/, DIGITS_VALIDATION_MSG)
        .length(9, 'Must be exactly 9 digits'),
    accountNumber: Yup.string()
        .required(DEFAULT_VALIDATION_MSG)
        .matches(/^\d+$/, DIGITS_VALIDATION_MSG)
        .max(15, 'Must <= 15 digits'),
    accountType: Yup.string().required(DEFAULT_VALIDATION_MSG),
});
