import React, {useState} from 'react';
import {KasAutocompleteField, KasModalFooter} from '@/components';
import {Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {ChangeBankAccountValues, validationSchema} from './schema';
import {useVerbalACH} from '@/components/KasGlobalModals/components';

export const ChangeBankAccount = () => {
    const {banksState, loadAgreementData, setOpenModal, verbalACHPayload, updateVerbalACHPayload} =
        useVerbalACH();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: ChangeBankAccountValues) => {
        setSubmitting(true);
        await loadAgreementData({bank_id: Number(values.bank)});
        setOpenModal(null);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            bank: (verbalACHPayload?.bank_id || '').toString(),
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={1}>
                <Grid2 size={12}>
                    <KasAutocompleteField
                        disabled={submitting}
                        name='bank'
                        label='Bank'
                        options={banksState.data || []}
                        formik={formik}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
