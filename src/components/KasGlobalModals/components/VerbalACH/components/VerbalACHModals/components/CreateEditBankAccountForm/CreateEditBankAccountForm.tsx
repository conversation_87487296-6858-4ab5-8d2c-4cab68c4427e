import React, {useState} from 'react';
import {FormControl, FormHelperText, InputLabel, Select, TextField, Grid2} from '@mui/material';
import {validationSchema} from './schema';
import {useFormik} from 'formik';
import MenuItem from '@mui/material/MenuItem';
import {KasMaskInput, KasModalFooter} from '@/components';
import {BankModel} from '@/interfaces';
import {CreateEditBankAccountValues} from './../../../../interfaces';

interface CreateEditBankAccountFormProps {
    bank: Partial<BankModel> | null;
    onSubmitHandler: (values: CreateEditBankAccountValues, bankId?: number) => Promise<void>;
    onClose: () => void;
}

export const CreateEditBankAccountForm = ({
    bank,
    onSubmitHandler,
    onClose,
}: CreateEditBankAccountFormProps) => {
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: CreateEditBankAccountValues) => {
        setSubmitting(true);
        await onSubmitHandler(values, bank?.gid);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            abaNumber: bank?.aba_number || '',
            accountNumber: bank?.account_number || '',
            accountType: bank?.account_type || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasMaskInput
                        formik={formik}
                        name='abaNumber'
                        disabled={submitting || !!bank?.aba_number}
                        mask='*********'
                        maskPlaceholder=''>
                        <TextField
                            fullWidth
                            name='abaNumber'
                            size='small'
                            variant='outlined'
                            placeholder={formik.values.abaNumber}
                            label='ABA Number'
                            disabled={submitting || !!bank?.aba_number}
                            value={formik.values.abaNumber}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={!!formik.errors.abaNumber && formik.touched.abaNumber}
                            helperText={formik.touched.abaNumber && formik.errors.abaNumber}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={6}>
                    <KasMaskInput
                        formik={formik}
                        name='accountNumber'
                        disabled={submitting || !!bank?.account_number}
                        mask='***************'
                        maskPlaceholder=''>
                        <TextField
                            fullWidth
                            name='accountNumber'
                            size='small'
                            variant='outlined'
                            placeholder={formik.values.accountNumber}
                            label='Account Number'
                            disabled={submitting || !!bank?.account_number}
                            value={formik.values.accountNumber}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={!!formik.errors.accountNumber && formik.touched.accountNumber}
                            helperText={formik.touched.abaNumber && formik.errors.accountNumber}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={12}>
                    <FormControl
                        fullWidth
                        size='small'
                        variant='outlined'
                        error={formik.touched.accountType && !!formik.errors.accountType}>
                        <InputLabel id='select-label'>Account Type</InputLabel>
                        <Select
                            labelId='select-label'
                            id='select-field'
                            name='accountType'
                            label='Account Type'
                            value={formik.values.accountType}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}>
                            <MenuItem value='Checking'>Checking</MenuItem>
                            <MenuItem value='Savings'>Savings</MenuItem>
                            <MenuItem value='Prepaid Card'>Prepaid Card</MenuItem>
                        </Select>
                        {formik.touched.accountType && !!formik.errors.accountType && (
                            <FormHelperText error={true}>{formik.errors.accountType}</FormHelperText>
                        )}
                    </FormControl>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
