import {useFormik} from 'formik';
import {VerbalACHFormValues, validationSchema} from './schema';
import {TextField, Grid2} from '@mui/material';
import {KasModalFooter, useGlobalModal} from '@/components';
import React, {useState} from 'react';
import {useVerbalACH} from '@/components/KasGlobalModals/components';

export const VerbalACHForm = () => {
    const {hideGlobalModal} = useGlobalModal();
    const {agreementState, submitAgreement} = useVerbalACH();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: VerbalACHFormValues) => {
        setSubmitting(true);
        await submitAgreement(values.ticketNumber);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            ticketNumber: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={4}>
                    <TextField
                        fullWidth
                        size='small'
                        name='ticketNumber'
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Ticket Number'
                        variant='outlined'
                        error={!!formik.errors.ticketNumber && formik.touched.ticketNumber}
                        helperText={formik.touched.ticketNumber && formik.errors.ticketNumber}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={!formik.isValid || !agreementState.data}
                        loading={submitting}
                        submitText='Sign'
                        onCancel={hideGlobalModal}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
