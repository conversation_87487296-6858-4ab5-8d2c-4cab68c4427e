import {BankModel} from '@/interfaces';
import {PaymentDTO} from '@/models';

export enum VerbalACHModal {
    Add_Bank_Account = 'Add Bank Account',
    Change_Bank_Account = 'Change Bank Account',
}

export type VerbalACHModalProps =
    | {type: VerbalACHModal.Add_Bank_Account; props: VerbalACHModalAddBankProps}
    | {type: VerbalACHModal.Change_Bank_Account};

export interface VerbalACHModalAddBankProps {
    bank: BankModel | null;
}

export interface VerbalACHValues {
    frequency: string;
    request_date?: string;
    start_date?: string;
    installment?: number;
    amount?: number;
    bank_id: number;
    option: string;
    paymentId?: number;
    payments: PaymentDTO[];
}

export interface CreateEditBankAccountValues {
    abaNumber: string;
    accountNumber: string;
    accountType: string;
}
