import React, {createContext, useContext, useRef, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {BankModel, Completable, PaymentFrequency, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DataStateInterface} from '@/interfaces';
import {AgreementDTO} from '@/models';
import {formatTimeStampISO} from '@/utils/DateUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {CreateEditBankAccountValues, VerbalACHModalProps, VerbalACHValues} from './interfaces';
import {GlobalModalVerbalACHProps, useGlobalModal} from '@/components';

interface VerbalACHContextModel extends GlobalModalVerbalACHProps {
    agreementState: DataStateInterface<AgreementDTO>;
    loadAgreementData: (payload?: Partial<VerbalACHValues>) => Promise<void>;
    banksState: DataStateInterface<SelectModel<string>[]>;
    loadBanksData: () => Promise<void>;
    submitAgreement: (ticketNumber: string) => Promise<void>;
    openModal: VerbalACHModalProps | null;
    setOpenModal: (value: VerbalACHModalProps | null) => void;
    addBankAccount: (values: CreateEditBankAccountValues) => Promise<void>;
    verbalACHPayload: Partial<VerbalACHValues>;
    updateVerbalACHPayload: (value: Partial<VerbalACHValues>) => void;
}

const VerbalACHContext = createContext<VerbalACHContextModel | undefined>(undefined);

interface VerbalACHProviderProps extends GlobalModalVerbalACHProps {
    children: React.ReactNode;
}

const INIT_VERBAL_ACH_PAYLOAD: Partial<VerbalACHValues> = {
    frequency: '',
    option: '',
    payments: [],
};

export const VerbalACHProvider = ({loan, children}: VerbalACHProviderProps) => {
    const {showMessage} = useSnackbar();
    const {hideGlobalModal} = useGlobalModal();
    const signaturePad = useRef<HTMLCanvasElement | null>(null);
    const [verbalACHPayload, setVerbalACHPayload] =
        useState<Partial<VerbalACHValues>>(INIT_VERBAL_ACH_PAYLOAD);
    const [agreementState, setAgreementState] = useState(getDefaultState<AgreementDTO>());
    const [banksState, setBanksState] = useState(getDefaultState<SelectModel<string>[]>());
    const [openModal, setOpenModal] = useState<VerbalACHModalProps | null>(null);

    const updateVerbalACHPayload = (value: Partial<VerbalACHValues>) => {
        setVerbalACHPayload((prev) => ({...prev, ...value}));
    };

    const loadAgreementData = async (payload?: Partial<VerbalACHValues>) => {
        payload && updateVerbalACHPayload(payload);

        const values = {...verbalACHPayload, ...payload};
        const body = JSON.stringify({
            ...values,
            option: values.frequency === PaymentFrequency.SINGLE ? 'SINGLE' : 'MULTIPLE',
        });
        const url = `/api/secured/collections/employee/${loan.employee_id}/agreement`;

        setAgreementState(getLoadingState(agreementState));

        const response: Completable<AgreementDTO> = await apiRequest(url, {method: 'put', body});

        setAgreementState(getLoadedState(response));
    };

    const loadBanksData = async () => {
        const url = `/api/secured/collections/employee/${loan.employee_id}/banks`;
        setBanksState(getLoadingState(banksState));

        const response: Completable<SelectModel<string>[]> = await apiRequest(url);

        setBanksState(getLoadedState(response));
    };

    const addBankAccount = async (values: CreateEditBankAccountValues) => {
        const url = `/api/secured/collections/employee/${loan.employee_id}/banks`;
        const body = JSON.stringify({
            aba_number: values.abaNumber,
            account_number: values.accountNumber,
            account_type: values.accountType,
        });

        const response: Completable<BankModel> = await apiRequest(url, {method: 'post', body});

        if (response.value) {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenModal(null);
            await loadBanksData();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    const submitAgreement = async (ticketNumber: string) => {
        const signatureText = `Ticket #${ticketNumber} - ${formatTimeStampISO(new Date())}`;
        const body = JSON.stringify({
            signature: generateSignature(signatureText),
            type: 'ACH',
        });
        const pymtId = `${agreementState.data?.payment_id || ''}`;
        const urlParams = new URLSearchParams({
            pymtId,
            frequency: verbalACHPayload?.frequency || '',
        }).toString();
        const url = `/api/secured/collections/employee/${loan.employee_id}/agreement?${urlParams}`;

        const response: Completable<boolean> = await apiRequest(url, {method: 'post', body});

        if (response.value) {
            showMessage('Agreement signed', 'success');
            hideGlobalModal();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    const generateSignature = (signatureText: string) => {
        const canvas = signaturePad.current!;
        const ctx = canvas.getContext('2d');
        const fontSize = 40;
        const fontFamily = 'Over the Rainbow';

        if (ctx) {
            ctx.font = fontSize + 'px ' + fontFamily;
            const metrics = ctx.measureText(signatureText);
            const textWidth = metrics.width + 40;

            let yScale = canvas.width / textWidth;

            if (fontSize * yScale > canvas.height) {
                yScale = canvas.height / fontSize;
            }
            let xScale = 1;
            if (textWidth > canvas.width) {
                xScale = canvas.width / textWidth;
            }
            const posY = canvas.height / 2 / yScale + 15;

            ctx.scale(xScale, yScale);
            ctx.fillText(signatureText, 10, posY);
        }
        return canvas.toDataURL();
    };

    const value: VerbalACHContextModel = {
        loan,
        agreementState,
        loadAgreementData,
        banksState,
        loadBanksData,
        submitAgreement,
        openModal,
        setOpenModal,
        addBankAccount,
        verbalACHPayload,
        updateVerbalACHPayload,
    };

    return (
        <VerbalACHContext.Provider value={value}>
            <canvas ref={signaturePad} width='400' height='80' hidden></canvas>
            {children}
        </VerbalACHContext.Provider>
    );
};

export function useVerbalACH() {
    const context = useContext(VerbalACHContext);
    if (!context) {
        throw new Error('useVerbalACH must be used within VerbalACHProvider');
    }
    return context;
}
