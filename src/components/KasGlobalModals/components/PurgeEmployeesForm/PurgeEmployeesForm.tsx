import React, {useEffect, useState} from 'react';
import {
    GlobalModalPurgeEmployeesProps,
    KasLoadingBackDrop,
    KasModalFooter,
    useGlobalModal,
} from '@/components';
import {Grid2, TextField} from '@mui/material';
import {useFormik} from 'formik';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {PurgeEmployeesFormValues, validationSchema} from './schema';
import {EmployeeUnderwritingDTO} from '@/models/employeeUnderwritingDTO';
import {MergeEmployeesDetail} from '../MergeEmployeesForm/components/MergeEmployeesDetail';
import {PurgeEmployeeDTO} from '@/models/purgeEmployeeDTO';

export const PurgeEmployeesForm = ({employeeId}: GlobalModalPurgeEmployeesProps) => {
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [oldEmployee, setOldEmployee] = useState<EmployeeUnderwritingDTO>();
    const {showMessage} = useSnackbar();
    const {hideGlobalModal} = useGlobalModal();

    const getEmployee = async (id: string): Promise<EmployeeUnderwritingDTO> => {
        const response = await apiRequest(`/api/secured/manager/support/profile/employee/${id}`);
        return response.value;
    };

    useEffect(() => {
        const fillOldEmployee = async (id: number) => {
            setLoading(true);
            const data = await getEmployee(id.toString());
            setOldEmployee(data);
            setLoading(false);
        };

        fillOldEmployee(employeeId);
    }, [employeeId]);

    const onSubmit = async (values: PurgeEmployeesFormValues) => {
        setSubmitting(true);

        const dto: PurgeEmployeeDTO = {
            gid: values.purge_employee_id,
            comment: values.comment,
        };

        const response = await apiRequest('/api/secured/support/manage/employee/partial/purge', {
            method: 'put',
            body: JSON.stringify(dto),
        });

        setSubmitting(false);

        if (response.value) {
            showMessage('Partial purged employee', 'success');
            onClose();
        } else {
            showMessage(response.error || 'Failed to partial merge');
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            purge_employee_id: employeeId,
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    const onClose = () => {
        hideGlobalModal();
    };

    return (
        <form onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container columns={12} spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <MergeEmployeesDetail item={oldEmployee} />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submitting}
                        value={formik.values.comment}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Partial Purge'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
