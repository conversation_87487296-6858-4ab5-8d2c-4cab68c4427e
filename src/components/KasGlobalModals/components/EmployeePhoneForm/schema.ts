import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    phone: Yup.string()
        .matches(/^\d{3}-\d{3}-\d{4}$/, DEFAULT_VALIDATION_MSG)
        .required(DEFAULT_VALIDATION_MSG),
    source: Yup.string().required(DEFAULT_VALIDATION_MSG),
});

export type CreateEditPhoneFormValues = Yup.Asserts<typeof validationSchema>;
