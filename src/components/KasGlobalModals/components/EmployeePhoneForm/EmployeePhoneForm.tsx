import React, {useEffect, useState} from 'react';
import {
    GlobalModalEmployeePhoneProps,
    KasAutocompleteField,
    KasLoadingBackDrop,
    KasMaskInput,
    KasModalFooter,
    useGlobalModal,
} from '@/components';
import {TextField, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {CreateEditPhoneFormValues, validationSchema} from './schema';
import {SelectModel} from '@/interfaces';
import {eventUpdateEmployeePhones} from '@/lib/slices/eventSlice';
import {useDispatch} from 'react-redux';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {PhoneDTO} from '@/models';

export const EmployeePhoneForm = ({
    employeeId,
    phone = '',
    method,
    gid,
    source,
}: GlobalModalEmployeePhoneProps) => {
    const dispatch = useDispatch();
    const {showMessage} = useSnackbar();
    const {hideGlobalModal} = useGlobalModal();
    const [submitting, setSubmitting] = useState(false);
    const [sources, setSources] = useState<SelectModel<string>[]>([]);
    const [loading, setLoading] = useState(true);

    const onSubmit = async (values: CreateEditPhoneFormValues) => {
        const url = `/api/secured/underwriting/employee-profile/${employeeId}/phones`;
        const payload: Partial<PhoneDTO> = {
            phone: values.phone,
            source: values.source,
        };

        if (gid) {
            payload.gid = gid;
        }

        setSubmitting(true);

        const response = await apiRequest(gid ? `${url}/${gid}` : url, {
            method,
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            dispatch(eventUpdateEmployeePhones());
            onClose();
        }

        setSubmitting(false);
    };

    const onClose = () => {
        hideGlobalModal();
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            phone,
            source: source || 'SUPPORT',
        },
        onSubmit,
        validationSchema,
    });

    const loadSources = () => {
        (async () => {
            setLoading(true);
            const response = await apiRequest('/api/secured/ui/lookup/phone/source');

            setSources(response.value || []);

            if (response.error) {
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            }

            setLoading(false);
        })();
    };

    useEffect(loadSources, []);

    return (
        <form onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={6}>
                    <KasMaskInput formik={formik} name='phone' disabled={submitting} mask='************'>
                        <TextField
                            fullWidth
                            size='small'
                            name='phone'
                            disabled={submitting}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            label='Phone'
                            variant='outlined'
                            error={!!formik.errors.phone && formik.touched.phone}
                            helperText={formik.touched.phone && formik.errors.phone}
                        />
                    </KasMaskInput>
                </Grid2>
                <Grid2 size={6}>
                    <KasAutocompleteField name='source' label='Source' options={sources} formik={formik} />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
