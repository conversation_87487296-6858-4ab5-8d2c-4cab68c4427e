import React, {useState} from 'react';
import {GlobalModalConfirmProps, KasModalFooter, useGlobalModal} from '@/components';
import {useFormik} from 'formik';
import {useSnackbar} from '@/hooks/useSnackbar';

export const ConfirmForm = ({onSubmit}: GlobalModalConfirmProps) => {
    const {hideGlobalModal} = useGlobalModal();
    const {showMessage} = useSnackbar();
    const [loading, setLoading] = useState(false);

    const wrappedSubmit = async () => {
        setLoading(true);
        const result = await onSubmit();
        setLoading(false);

        if (result) {
            showMessage('Operation successful', 'success');
            hideGlobalModal();
        } else {
            showMessage('Operation failed');
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {},
        onSubmit: wrappedSubmit,
    });

    const onCloseWrapped = () => {
        hideGlobalModal();
    };

    return (
        <form onSubmit={formik.handleSubmit}>
            <KasModalFooter submitText={'Confirm'} onCancel={onCloseWrapped} loading={loading} />
        </form>
    );
};
