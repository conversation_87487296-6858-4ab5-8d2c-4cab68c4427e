import React, {useState} from 'react';
import {
    GlobalModalEmployeeAddressProps,
    KasGooglePlacesAutocomplete,
    KasModalFooter,
    useGlobalModal,
} from '@/components';
import {TextField, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {KasAutocompleteSelect} from '@/components/autocomplete/KasAutocompleteSelect';
import {AddressDTO} from '@/models';
import {useDispatch} from 'react-redux';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {eventUpdateEmployeeAddresses} from '@/lib/slices/eventSlice';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const AddEmpolyeeAddressForm = ({
    employeeId,
    address,
    method,
    gid,
    source,
}: GlobalModalEmployeeAddressProps) => {
    const dispatch = useDispatch();
    const {showMessage} = useSnackbar();
    const {hideGlobalModal} = useGlobalModal();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: AddressDTO) => {
        const url = `/api/secured/underwriting/employee-profile/${employeeId}/addresses`;
        const payload: AddressDTO = {
            ...values,
        };

        if (gid) {
            payload.gid = gid;
        }

        if (source) {
            payload.source = source;
        }

        setSubmitting(true);

        const response = await apiRequest(gid ? `${url}/${gid}` : url, {
            method,
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            dispatch(eventUpdateEmployeeAddresses());
            onClose();
        }

        setSubmitting(false);
    };

    const onClose = () => {
        hideGlobalModal();
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            city: address?.city || '',
            street1: address?.street1 || '',
            street2: address?.street2 || '',
            street3: address?.street3 || '',
            state_cd: address?.state_cd || '',
            zip: address?.zip || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <KasGooglePlacesAutocomplete
                        label='Address Line 1'
                        name='street1'
                        formik={formik}
                        onSelect={(address) => {
                            formik.setFieldValue('street1', address.street1);
                            formik.setFieldValue('city', address.city);
                            formik.setFieldValue('state_cd', address.state_cd);
                            formik.setFieldValue('zip', address.zip);
                        }}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='street2'
                        disabled={submitting}
                        value={formik.values.street2}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Address Line 2'
                        variant='outlined'
                    />
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='street3'
                        value={formik.values.street3}
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Address Line 3'
                        variant='outlined'
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        name='city'
                        disabled={submitting}
                        value={formik.values.city}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='City'
                        variant='outlined'
                        error={!!formik.errors.city && formik.touched.city}
                        helperText={formik.touched.city && formik.errors.city}
                    />
                </Grid2>
                <Grid2 size={3}>
                    <KasAutocompleteSelect
                        formik={formik}
                        name='state_cd'
                        label='State'
                        searchUrl={'/api/secured/ui/lookup/state/code?value='}
                    />
                </Grid2>
                <Grid2 size={3}>
                    <TextField
                        fullWidth
                        size='small'
                        name='zip'
                        disabled={submitting}
                        value={formik.values.zip}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Zip'
                        variant='outlined'
                        error={!!formik.errors.zip && formik.touched.zip}
                        helperText={formik.touched.zip && formik.errors.zip}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
