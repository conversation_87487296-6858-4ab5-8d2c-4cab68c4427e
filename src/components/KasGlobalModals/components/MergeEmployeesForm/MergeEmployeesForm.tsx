import React, {useEffect, useState} from 'react';
import {
    GlobalModalMergeEmployeesProps,
    KasAsyncSearchAutocompleteField,
    KasLoadingBackDrop,
    KasModalFooter,
    useGlobalModal,
} from '@/components';
import {Divider, Grid2, TextField} from '@mui/material';
import {useFormik} from 'formik';
import {apiRequest} from '@/utils/AxiosUtils';
import {SelectModel} from '@/interfaces/select.interface';
import {
    approveSchema,
    MergeEmployeesApproveFormValues,
    MergeEmployeesRequestFormValues,
    requestSchema,
} from './schema';
import {EmployeeUnderwritingDTO} from '@/models/employeeUnderwritingDTO';
import {MergeEmployeesDetail} from './components/MergeEmployeesDetail';
import {MergeEmployeeDTO} from '@/models/mergeEmployeeDTO';
import {useSnackbar} from '@/hooks/useSnackbar';
import {MakerCheckerRequestDTO} from '@/models/makerCheckerRequestDTO';

export const MergeEmployeesForm = ({
    employeeId,
    approvable,
    merge_to_employee_id,
    request_id,
    comments,
    onSuccess,
}: GlobalModalMergeEmployeesProps) => {
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState<SelectModel<string> | null>(null);
    const [oldEmployee, setOldEmployee] = useState<EmployeeUnderwritingDTO>();
    const [newEmployee, setNewEmployee] = useState<EmployeeUnderwritingDTO>();
    const {hideGlobalModal} = useGlobalModal();
    const {showMessage} = useSnackbar();

    const getEmployee = async (id: string): Promise<EmployeeUnderwritingDTO> => {
        const response = await apiRequest(`/api/secured/manager/support/profile/employee/${id}`);
        return response.value;
    };

    const fillNewEmployee = async (id: string): Promise<Boolean> => {
        setLoading(true);
        const data = await getEmployee(id);
        setNewEmployee(data);
        setLoading(false);
        return true;
    };

    useEffect(() => {
        merge_to_employee_id && fillNewEmployee(merge_to_employee_id.toString());
    }, [merge_to_employee_id]);

    useEffect(() => {
        const fillOldEmployee = async (id: number) => {
            setLoading(true);
            const data = await getEmployee(id.toString());
            setOldEmployee(data);
            setLoading(false);
        };

        fillOldEmployee(employeeId);
    }, [employeeId]);

    useEffect(() => {
        if (!!selectedEmployee) {
            fillNewEmployee(selectedEmployee?.value ?? '');
        } else {
            setNewEmployee(undefined);
        }
    }, [selectedEmployee]);

    const onRequestSubmit = async (values: MergeEmployeesRequestFormValues) => {
        setSubmitting(true);

        const content: MergeEmployeeDTO = {
            merge_from_employee_id: values.merge_from_employee_id,
            merge_to_employee_id: values.merge_to_employee_id,
        };
        const dto: MakerCheckerRequestDTO = {
            operation: 'MERGE_EMPLOYEE',
            contents: JSON.stringify(content),
            comment: values.comment,
        };

        const response = await apiRequest('/api/secured/common/makerchecker/request', {
            method: 'post',
            body: JSON.stringify(dto),
        });

        setSubmitting(false);

        if (response.value) {
            showMessage('Requested merge', 'success');
            onClose();
        } else {
            showMessage('Failed to request merge');
        }
    };

    const onApproveSubmit = async (values: MergeEmployeesApproveFormValues) => {
        setSubmitting(true);

        const response = await apiRequest(`/api/secured/common/makerchecker/request/${values.request_id}`, {
            method: 'put',
            body: JSON.stringify({value: values.comment}),
        });

        setSubmitting(false);

        if (response.value) {
            showMessage('Merged', 'success');
            onClose();
            onSuccess && (await onSuccess());
        } else {
            showMessage('Failed to merge');
        }
    };

    const requestFormik = useFormik({
        validateOnMount: true,
        initialValues: {
            merge_from_employee_id: employeeId,
            merge_to_employee_id: NaN,
            comment: '',
        },
        onSubmit: onRequestSubmit,
        validationSchema: requestSchema,
    });

    const approveFormik = useFormik({
        validateOnMount: true,
        initialValues: {
            request_id: request_id ?? NaN,
            comment: '',
        },
        onSubmit: onApproveSubmit,
        validationSchema: approveSchema,
    });

    const formik = approvable ? approveFormik : requestFormik;

    const onClose = () => {
        hideGlobalModal();
    };

    const onSelectEmployee = (value: SelectModel<string> | null) => {
        formik.setFieldValue('merge_to_employee_id', parseInt(value?.value || ''));
        setSelectedEmployee(value);
    };

    return (
        <form onSubmit={formik.handleSubmit}>
            {loading && <KasLoadingBackDrop />}
            <Grid2 container columns={13} spacing={2} rowSpacing={2}>
                {approvable && comments?.length && comments.length > 0 && (
                    <Grid2 size={13}>
                        <b>Comments:</b>
                        {comments.map((item, index) => (
                            <p key={index}>{item}</p>
                        ))}
                    </Grid2>
                )}

                <Grid2 size={6}>
                    <b>Old Employee</b>
                </Grid2>
                <Divider orientation='vertical' flexItem />
                <Grid2 size={6}>
                    <b>New Employee</b>
                </Grid2>

                {!approvable && (
                    <>
                        <Grid2 size={6}></Grid2>
                        <Divider orientation='vertical' flexItem />
                        <Grid2 size={6}>
                            <KasAsyncSearchAutocompleteField
                                value={selectedEmployee}
                                label='Employee'
                                searchUrl={'/api/secured/ui/lookup/employee/'}
                                onSelect={onSelectEmployee}
                            />
                        </Grid2>
                    </>
                )}

                <Grid2 size={6}>
                    <MergeEmployeesDetail item={oldEmployee} />
                </Grid2>
                <Divider orientation='vertical' flexItem />
                <Grid2 size={6}>{newEmployee && <MergeEmployeesDetail item={newEmployee} />}</Grid2>

                <Grid2 size={13}>
                    <TextField
                        fullWidth
                        name='comment'
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                    />
                </Grid2>

                <Grid2 size={13}>
                    <KasModalFooter
                        submitText={approvable ? 'Commit Merge' : 'Request Merge'}
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={onClose}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
