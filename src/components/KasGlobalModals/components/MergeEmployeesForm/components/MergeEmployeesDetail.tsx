import {KasFlaggedIcon} from '@/components/KasFlaggedIcon/KasFlaggedIcon';
import {KasInfo} from '@/components/KasInfo/KasInfo';
import {KasUnderwritingSharedLink} from '@/components/KasLink/KasLink';
import {KasMaskedSSN} from '@/components/KasMaskedSSN/KasMaskedSSN';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {Completable} from '@/interfaces';
import {EmployeeUnderwritingDTO} from '@/models/employeeUnderwritingDTO';
import {apiRequest} from '@/utils/AxiosUtils';
import {toCurrency} from '@/utils/FormatUtils';
import {useTheme} from '@mui/material';

interface MergeEmployeesDetailProps {
    item: EmployeeUnderwritingDTO | undefined;
}

export const MergeEmployeesDetail = ({item}: MergeEmployeesDetailProps) => {
    const {showMessage} = useSnackbar();
    const {palette} = useTheme();

    const getSSN = async (id: number) => {
        const url = `/api/secured/underwriting/employee-profile/${id || ''}/ssn`;
        const response: Completable<string> = await apiRequest(url);

        if (!response.value) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        return response;
    };

    const statusColor = (status: string): string => {
        if (status === 'Active' || status === 'LeaveWithPay') {
            return palette.success.main;
        }

        return palette.error.main;
    };

    return (
        <>
            <p>Employer Name: {item?.employer_name}</p>
            <br />
            <p>
                ID: [<KasUnderwritingSharedLink id={item?.gid} />]
            </p>
            <KasInfo label='ID @ Employer: ' isInline>
                {item?.id_at_employer}
            </KasInfo>
            <KasInfo label='First Name: ' isInline>
                {item?.first_name}
            </KasInfo>
            <KasInfo label='Last Name: ' isInline>
                {item?.last_name}
            </KasInfo>
            <KasInfo label='SSN: ' isInline>
                <KasMaskedSSN ssn={item?.ssn || ''} onLoanSSN={() => getSSN(item?.gid || 0)} />
            </KasInfo>
            <KasInfo label='Date of Birth: ' isInline>
                {item?.dob}
            </KasInfo>
            <br />
            <KasInfo label='Employment Status: ' isInline>
                <span style={{color: statusColor(item?.status || '')}}>{item?.status}</span>
            </KasInfo>
            <KasInfo label='Benefits Eligible: ' isInline>
                {item && (
                    <span style={{verticalAlign: 'middle'}}>
                        <KasFlaggedIcon flagged={item.benefit_eligible_employee} />
                    </span>
                )}
            </KasInfo>
            <KasInfo label='Account Balance: ' isInline>
                {toCurrency(item?.account_balance || '')}
            </KasInfo>
            <KasInfo label='Status: ' isInline>
                {item?.application_status}
            </KasInfo>
            <br />
            <KasInfo label='Home Phone: ' isInline>
                {item?.phone2}
            </KasInfo>
            <KasInfo label='Work Email: ' isInline>
                {item?.email_at_employer}
            </KasInfo>
            <KasInfo label='Pay Type: ' isInline>{`${item?.pay_type} (${item?.full_part_time})`}</KasInfo>
            <KasInfo label='Position: ' isInline>
                {item?.position}
            </KasInfo>
            <KasInfo label='Hire Date: ' isInline>
                {item?.last_hire_date}
            </KasInfo>
            <KasInfo label='Income Flagged: ' isInline>
                {item && (
                    <span style={{verticalAlign: 'middle'}}>
                        <KasFlaggedIcon reverseColors={true} flagged={item.income_flagged} />
                    </span>
                )}
            </KasInfo>
        </>
    );
};
