import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const requestSchema = Yup.object().shape({
    merge_from_employee_id: Yup.number().required(DEFAULT_VALIDATION_MSG),
    merge_to_employee_id: Yup.number().required(DEFAULT_VALIDATION_MSG),
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG)
});

export type MergeEmployeesRequestFormValues = Yup.Asserts<typeof requestSchema>;

export const approveSchema = Yup.object().shape({
    request_id: Yup.number().required(DEFAULT_VALIDATION_MSG),
    comment: Yup.string().required(DEFAULT_VALIDATION_MSG)
});

export type MergeEmployeesApproveFormValues = Yup.Asserts<typeof approveSchema>;
