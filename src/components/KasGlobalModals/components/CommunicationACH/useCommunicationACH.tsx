import React, {createContext, useContext} from 'react';
import {GlobalModalCommunicationACHProps} from '@/components';

interface CommunicationACHContextModel extends GlobalModalCommunicationACHProps {}

const CommunicationACHContext = createContext<CommunicationACHContextModel | undefined>(undefined);

interface CommunicationACHProviderProps extends GlobalModalCommunicationACHProps {
    children: React.ReactNode;
}

export const CommunicationACHProvider = ({children, ...rest}: CommunicationACHProviderProps) => {
    const value: CommunicationACHContextModel = {
        ...rest,
    };

    return <CommunicationACHContext.Provider value={value}>{children}</CommunicationACHContext.Provider>;
};

export function useCommunicationACH() {
    const context = useContext(CommunicationACHContext);
    if (!context) {
        throw new Error('useCommunicationACH must be used within CommunicationACHProvider');
    }
    return context;
}
