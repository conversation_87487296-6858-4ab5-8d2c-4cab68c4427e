import React, {createContext, Props<PERSON>ith<PERSON>hildren, useContext, useState} from 'react';
import {SelectModel} from '@/interfaces';
import {usePrint} from '@/hooks/usePrint';

interface EmailACHContextModel extends ReturnType<typeof usePrint> {
    selectedTypeOption: SelectModel<string> | null;
    changeSelectedTypeOption: (value: SelectModel<string> | null) => void;
}

const EmailACHContext = createContext<EmailACHContextModel | undefined>(undefined);

export const EmailACHProvider = ({children}: PropsWithChildren) => {
    const printMethods = usePrint();
    const [selectedTypeOption, setSelectedTypeOption] = useState<SelectModel<string> | null>(null);

    const changeSelectedTypeOption = (value: SelectModel<string> | null) => {
        setSelectedTypeOption(value);
    };

    const value: EmailACHContextModel = {
        selectedTypeOption,
        changeSelectedTypeOption,
        ...printMethods,
    };

    return <EmailACHContext.Provider value={value}>{children}</EmailACHContext.Provider>;
};

export function useEmailACH() {
    const context = useContext(EmailACHContext);
    if (!context) {
        throw new Error('useEmailACH must be used within EmailACHProvider');
    }
    return context;
}
