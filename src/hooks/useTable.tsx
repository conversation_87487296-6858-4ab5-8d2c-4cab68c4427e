import {Dispatch, SetStateAction, useEffect, useMemo, useState} from 'react';
import {
    ColumnDef,
    ColumnFiltersState,
    ColumnOrderState,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    PaginationState,
    Row,
    SortingState,
    Table,
    useReactTable,
} from '@tanstack/react-table';

interface UseTableResult<T> {
    table: Table<T>;
    sortedData: T[];
    setGlobalFilter: Dispatch<SetStateAction<string>>;
    setColumnFilters: Dispatch<SetStateAction<ColumnFiltersState>>;
    setPagination: Dispatch<SetStateAction<PaginationState>>;
}

export const useTable = <T,>(
    data: T[],
    columns: ColumnDef<T>[],
    initialSorting?: SortingState,
): UseTableResult<T> => {
    const [pagination, setPagination] = useState<PaginationState>({pageIndex: 0, pageSize: 12});
    const [globalFilter, setGlobalFilter] = useState('');
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = useState<SortingState>(initialSorting || []);
    const [columnVisibility, setColumnVisibility] = useState({});
    const [columnOrder, setColumnOrder] = useState<ColumnOrderState>([]);
    const [prevDataLength, setPrevDataLength] = useState(data.length);

    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnVisibility,
            columnOrder,
            globalFilter,
            columnFilters,
            pagination,
        },
        initialState: {
            pagination,
        },
        onGlobalFilterChange: setGlobalFilter,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
        onPaginationChange: setPagination,
        getRowCanExpand: (row: Row<T>) => true,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        autoResetPageIndex: data.length !== prevDataLength,
    });

    useEffect(() => {
        setPrevDataLength(data.length);
    }, [data]);

    useEffect(() => {
        setPagination((prev) => ({...prev, pageIndex: 0}));
    }, [globalFilter, columnFilters, sorting]);

    const sortedData = useMemo(() => {
        return table.getSortedRowModel().rows.map((row) => row.original);
    }, [sorting]);

    return {table, sortedData, setGlobalFilter, setColumnFilters, setPagination};
};
