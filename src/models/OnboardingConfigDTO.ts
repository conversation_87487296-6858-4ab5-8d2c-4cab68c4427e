export interface OnboardingOrganization {
  alias: string;
}



export interface OnboardingConfigDTO {
  config: OnboardingConfig;
  mnemonic: string;
  commit_hash: string;
  git_ref_name: string;
  status: string;
}

export interface OnboardingConfig {
  parent_mnemonic: string;
  id_at_intermediary: string;
  state_of_incorporation: string;
  industry_subcode: string;
  industry: string;
  name: string;
  alias: string;
  legal_name: string;
  street1: string;
  street2: string;
  city: string;
  state: string;
  zip: string;
  phone1: string;
  phone2: string;
  website: string;
  fax: string;
  broker: string;
  payroll_processor: string;
  reuse_id:boolean;
  primary_client: boolean;
  unusable_eid: boolean;
  deduction_consolidate: boolean;
  deposit_confirmation_required: boolean;
  intermediary_partner: boolean;
  sso_required: boolean;
  deduction_output_always: boolean;
  self_service_direct_deposit: boolean
  searchable:boolean;
  business_day: string;
  start_date?: string;
  data_room_uri: string;
  assigned_team: string;
  assigned_user: string;
  agreement_export: string;
  deduction_mode: string;
  deduction_export: string;
  payroll_groups: OnboardingConfigPayrollGroup[];
  contacts: OnboardingContact[];
  primary_contact?: string;
  census: OnboardingCensus;
  billing: OnboardingBilling;
  reconciliation: OnboardingReconciliation;
  email_blacklist: string[];
}

export interface OnboardingCensus {
  key: string;
  template?: string;
}

export interface OnboardingBilling {
  key: string;
  template?: string;
}
export interface OnboardingReconciliation {
  key: string;
  template?: string;
}

export interface OnboardingContact {
  key: string;
  type: string;
  body?: ContactBody;
}

export interface ContactBody {
  first_name: string;
  last_name: string;
  email?: string;
  phone1?: string;
  ext? : string;
  title?: string;
}


export interface EmployerContactTableModel {
  is_inherited : boolean;
  has_body? : boolean;
  key: string;
  first_name?: string;
  last_name? : string;
  contact_type : string;
  email?: string;
  phone1?: string;
  title?: string;
  ext?: string;
}

export interface OnboardingConfigPayrollGroup {
  key:string;
  payroll_frequency?: string;
  mode?: string;
  alias?: string;
  sample_date?: string;
  deduction_lead_days?: number;
  deduction_warning_days?: number;
  deduction_lookback_days?: number;
  first_installment_lead_days?: number;
  active: boolean;
}

export enum OnboardingConfigPayrollGroupFrequency {
  WEEKLY,
  WEEKLY5,
  BIWEEKLY,
  BIWEEKLY3,
  SEMIMONTHLY,
  MONTHLY
}

export enum OnboardingEmployerDetailItemType {
  BasicInfo,
  Payroll,
  Repayment,
  Contacts,
  Census,
  Reconciliation,
  Billing,
  EmailBlackList
}

export enum OnboardingStep {
  REQUEST_QA = "REQUEST_QA",
  DECLINE_QA = "DECLINE_QA",
  DEPLOY_DEV = "DEPLOY_DEV",
  REVERT_DRAFT = "REVERT_DRAFT",
  REQUEST_ADMIN = "REQUEST_ADMIN",
  DECLINE_ADMIN = "DECLINE_ADMIN",
  DEPLOY_PROD = "DEPLOY_PROD",
}


export interface OnboardingEmployerDetailItemModel {
  id: string;
  type: OnboardingEmployerDetailItemType;
  title : string;
  expand: boolean;
}

export const EMPLOYER_ITEMS_CONFIG_KEY = 'onboarding-employer-items-config';

export const DEFAULT_EMPLOYER_DETAIL_SECTION: OnboardingEmployerDetailItemModel[] = [
  {
    id: '1',
    type: OnboardingEmployerDetailItemType.BasicInfo,
    title: 'Basic Info',
    expand: true,
  },
  {
    id: '2',
    type: OnboardingEmployerDetailItemType.Payroll,
    title: 'Payroll',
    expand: true,
  },
  {
    id: '3',
    type: OnboardingEmployerDetailItemType.Repayment,
    title: 'Repayment',
    expand: true,
  },
  {
    id: '4',
    type: OnboardingEmployerDetailItemType.Contacts,
    title: 'Contacts',
    expand: true,
  },
  {
    id: '5',
    type: OnboardingEmployerDetailItemType.Census,
    title: 'Census',
    expand: true,
  },
  {
    id: '6',
    type: OnboardingEmployerDetailItemType.Reconciliation,
    title: 'Reconciliation',
    expand: true,
  },
  {
    id: '7',
    type: OnboardingEmployerDetailItemType.Billing,
    title: 'Billing',
    expand: true,
  },
  {
    id: '8',
    type: OnboardingEmployerDetailItemType.EmailBlackList,
    title: 'Email Black List',
    expand: true,
  },

]