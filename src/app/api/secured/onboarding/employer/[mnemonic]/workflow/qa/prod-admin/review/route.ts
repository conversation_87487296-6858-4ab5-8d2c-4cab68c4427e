import { NextRequest, NextResponse } from 'next/server';
import { errored, mapSucceeded } from '@/utils/AxiosUtils';
import { AxiosError } from 'axios';
import { axiosInterceptorInstance } from '@/app/api/axiosInstance';
import { KashableResponseDTO } from '@/models';

const requestProdAdminReview = (mnemonic: string, commitHash: string) => {
    const url = `/secured/employer/onboarding/${mnemonic}/workflow/qa/prod-admin/review`;

    return axiosInterceptorInstance
        .post(url, JSON.stringify({ value: commitHash }), {
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then((res) => mapSucceeded(res, () => ({ success: true })))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function PUT(request: NextRequest, { params }: { params: Promise<{ mnemonic: string }> }) {
    const { mnemonic } = await params;
    const body = await request.json();
    const commitHash = body.value || '';

    const response = await requestProdAdminReview(mnemonic, commitHash);
    return NextResponse.json(response, { status: response.code });
}
