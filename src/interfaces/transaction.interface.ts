export interface TransactionModel {
    gid: number;
    employee_id: number;
    transaction_type: string;
    category: string;
    loan_id: number;
    document_id: number;
    clearing_id: number;
    bank_id: number;
    voided: boolean;
    last_update_user: string;
    source: string;
    entry_date: string;
    effective_date: string;
    amount: string;
    loan_balance?: number;
    account_balance?: number;
}

export interface NotificationModel {
    gid: number;
    entity_class: string;
    entity_id: number;
    notification_type: string;
    notification_template: string;
    last_update_user: string;
    collections: boolean;
    sent_time: string;
}
