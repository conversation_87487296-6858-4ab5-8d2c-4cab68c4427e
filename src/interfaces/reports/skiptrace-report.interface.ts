export enum SkiptraceReportDetailsTabType {
    Names = 'Names',
    Addresses = 'Addresses',
    SSNs = 'SSNs',
    Phones = 'Phones',
    DOBs = 'DOBs',
    DODs = 'DODs',
}

export interface SkiptraceReportModel {
    gid: number;
    report_date: string;
    product: string;
    agency: string;
    search_params: string[];
}

export interface SkiptraceVerifiedItemsModel {
    ssn_verified: boolean;
    address_verified: boolean;
    phone_verified: boolean;
}

interface BaseSkiptraceRecord {
    warnings: SkiptraceRecordWarningModel[];
    match: number | null;
    matched: boolean;
}

export interface SkiptraceRecordWarningModel {
    message: string;
    summary: string;
    critical: boolean;
}

interface SkiptraceReportAddress {
    unique_id: string | null;
    street1: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    formattedAddress: string;
}

interface SkiptraceReportPhone {
    phone_type: string;
    phone_number: string;
    carrier: string | null;
    listed_name: string | null;
    first_seen: string;
    last_seen: string;
    phone_cnam: string | null;
}

export interface SkiptraceRecordNameModel extends BaseSkiptraceRecord {
    name: string;
    gender: string | null;
}

export interface SkiptraceRecordPhoneLinkedAddressModel extends BaseSkiptraceRecord, SkiptraceReportAddress {
    first_seen: string | null;
    last_seen: string | null;
    linked_phones: string[];
}

export interface SkiptraceRecordPhoneModel extends BaseSkiptraceRecord, SkiptraceReportPhone {
    linked_addresses: SkiptraceRecordPhoneLinkedAddressModel[];
}

export interface SkiptraceRecordAddressLinkedPhoneModel extends BaseSkiptraceRecord, SkiptraceReportPhone {
    linked_addresses: SkiptraceRecordPhoneLinkedAddressModel[];
}

export interface SkiptraceRecordAddressModel extends BaseSkiptraceRecord, SkiptraceReportAddress {
    first_seen: string;
    last_seen: string;
    linked_phones: SkiptraceRecordAddressLinkedPhoneModel[];
}

export interface SkiptraceRecordSSNModel extends BaseSkiptraceRecord {
    ssn: string;
    valid: boolean;
    start: string;
    end: string;
}

export interface SkiptraceRecordDateModel {
    date: string;
}

export interface SkiptraceRecordDOBModel extends SkiptraceRecordDateModel {
    yearMatch: boolean;
    monthMatch: boolean;
    dayMatch: boolean;
}

export interface SkiptraceRecordModel extends BaseSkiptraceRecord {
    id: string;
    names: SkiptraceRecordNameModel[];
    phones: SkiptraceRecordPhoneModel[];
    addresses: SkiptraceRecordAddressModel[];
    ssns: SkiptraceRecordSSNModel[];
    dobs: SkiptraceRecordDateModel[];
    dods: SkiptraceRecordDateModel[];
    name_verified: boolean;
    phone_verified: boolean;
    address_verified: boolean;
    dob_verified: boolean;
    ssn_verified: boolean;
    name_flagged: boolean | null;
    phone_flagged: boolean | null;
    address_flagged: boolean | null;
    dob_flagged: boolean | null;
    dod_flagged: boolean | null;
    ssn_flagged: boolean | null;
}

export interface SkiptracePreviewReportModel {
    gid: number;
    report_date: string;
    transaction_id: string;
    records: SkiptraceRecordModel[];
}
