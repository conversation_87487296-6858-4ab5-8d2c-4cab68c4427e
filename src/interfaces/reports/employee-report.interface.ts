export interface EmploymentReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    active: boolean;
    comment: string;
    records: number;
}

export interface EmploymentPinwheelIncomeReportModel {
    id: string;
    account_id: string;
    created_at: string;
    compensation_amount: number;
    compensation_unit: string;
    currency: string;
    error_msg: string | null;
}

export interface EmploymentPinwheelEmploymentReportModel {
    id: string;
    account_id: string;
    status: string;
    title: string | null;
    start_date: string;
    termination_date: string | null;
    employer_name: string;
    created_at: string;
    error_msg: string | null;
}

export interface EmploymentPinwheelPaystubTaxReportModel {
    name: string;
    category: string;
    amount: number;
}

export interface EmploymentPinwheelPaystubDeductionReportModel {
    name: string;
    category: string;
    amount: number;
    type: string;
}

export interface EmploymentPinwheelPaystubEarningReportModel {
    name: string;
    category: string;
    amount: number;
    rate: number | null;
    hours: number | null;
}

export interface EmploymentPinwheelPaystubReportModel {
    id: string;
    account_id: string;
    pay_date: string;
    pay_period_start: string;
    pay_period_end: string;
    currency: string;
    gross_pay_amount: number;
    net_pay_amount: number;
    check_amount: number;
    gross_pay_ytd: number;
    net_pay_ytd: number;
    total_taxes: number;
    total_deductions: number;
    total_reimbursements: number;
    taxes: EmploymentPinwheelPaystubTaxReportModel[];
    deductions: EmploymentPinwheelPaystubDeductionReportModel[];
    earnings: EmploymentPinwheelPaystubEarningReportModel[];
    created_at: string;
    error_msg: string | null;
}

export interface EmploymentPinwheelReportModel {
    employmentReportCompleteTime: string;
    incomeReportCompleteTime: string;
    paystubsReportCompleteTime: string;
    incomeReport: EmploymentPinwheelIncomeReportModel;
    employmentReport: EmploymentPinwheelEmploymentReportModel;
    paystubsReport: EmploymentPinwheelPaystubReportModel[];
}

export interface EmploymentAtomicEmploymentReport {
    employment: EmploymentAtomicEmploymentDetails;
    company: EmploymentAtomicCompany;
    connector: EmploymentAtomicConnector;
    linkedAccount: string;
    task: string;
    uid: string;
}

export interface EmploymentAtomicEmploymentDetails {
    employeeType: string;
    employmentStatus: string;
    jobTitle: string;
    startDate: string;
    minimumMonthsOfEmployment: number;
    employer: EmploymentAtomicEmployer;
}

export interface EmploymentAtomicEmployer {
    name: string;
    address: EmploymentAtomicAddress;
}

export interface EmploymentAtomicAddress {
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
}

export interface EmploymentAtomicCompany {
    _id: string;
    name: string;
    branding: EmploymentAtomicCompanyBranding;
}

export interface EmploymentAtomicCompanyBranding {
    logo: {
        url: string;
    };
    color: string;
}

export interface EmploymentAtomicConnector {
    _id: string;
    name: string;
    branding: {
        color: string;
    };
}

export interface EmploymentAtomicIncomeReport {
    income: EmploymentAtomicIncomeDetails;
    company: EmploymentAtomicCompany;
    connector: EmploymentAtomicConnector;
    linkedAccount: string;
    task: string;
    uid: string;
}

export interface EmploymentAtomicIncomeDetails {
    income: number;
    incomeType: string;
    annualIncome: number;
    hourlyIncome: number;
    netHourlyRate: number;
    payCycle: string;
    nextExpectedPayDate: string;
    currentPayPeriodStart: string;
    unpaidHoursInPayPeriod: number;
}

export interface EmploymentAtomicStatementReport {
    date: string;
    hours: string;
    grossAmount: number;
    paystub: {_id: string};
}

export interface EmploymentAtomicTimesheetReport {
    date: string;
    duration: string;
    type: string;
}

export interface EmploymentAtomicReportModel {
    emplymentReportUuid: string;
    employmentReportCompleteTime: string;
    incomeReportUuid: string;
    incomeReportCompleteTime: string;
    employmentReports: EmploymentAtomicEmploymentReport[];
    incomeReports: EmploymentAtomicIncomeReport[];
    statementsReport?: {
        task: string;
        statements: EmploymentAtomicStatementReport[];
    };
    timesheetsReport?: EmploymentAtomicTimesheetReport[];
}

export type EmploymentReportDetailModel =
    | string
    | EmploymentPinwheelReportModel
    | EmploymentAtomicReportModel;
