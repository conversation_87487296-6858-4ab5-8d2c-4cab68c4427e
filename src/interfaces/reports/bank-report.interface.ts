import {CommentDTO} from '@/models/restrictDTO';

export interface BankAccountOwnerValueReportModel {
    matched: boolean;
    primary: boolean | null;
    value: string;
}

export interface BankAccountOwnerAddressValueReportModel {
    matched: boolean;
    primary: boolean;
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zip: string;
    formattedAddress: string;
}

export interface BankAccountOwnerReportModel {
    names: BankAccountOwnerValueReportModel[];
    emails: BankAccountOwnerValueReportModel[];
    phone_numbers: BankAccountOwnerValueReportModel[];
    addresses: BankAccountOwnerAddressValueReportModel[];
}

export interface BankAccountTransactionReportModel {
    date: string;
    amount: string;
    description: string;
}

export interface BankAccountHistoricalBalanceModel {
    amount: string;
    date: string;
}

export interface BankAccountReportModel {
    account_name: string;
    type: string;
    aba_number: string | null;
    account_number: string | null;
    balance: string;
    aba_match?: boolean;
    account_match?: boolean;
    owners: BankAccountOwnerReportModel[];
    transactions: BankAccountTransactionReportModel[];
    historical_balances?: BankAccountHistoricalBalanceModel[];
}

export interface BankWarningReportModel {
    message: string;
}

export interface BankErrorReportModel {
    institution: string;
    code: string;
    message: string;
}

export interface BankReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    active: boolean;
    applications: number[];
    bank_name: string;
    report_id: string;
    reportPending: boolean;
    override: boolean;
    override_user_name: string;
    computed_annual_salary: string;
    accounts: BankAccountReportModel[];
    errors: BankErrorReportModel[];
    warnings: BankWarningReportModel[];
    comments: CommentDTO[];
}
