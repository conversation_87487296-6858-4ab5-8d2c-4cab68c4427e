import {Dayjs} from 'dayjs';

export * from './dashboards';
export * from './reports';
export * from './bank.interface';
export * from './Completable';
export * from './data-state.interface';
export * from './loan.interface';
export * from './meta-data.interface';
export * from './select.interface';
export * from './transaction.interface';

export interface DayJsRangeModel {
    start: Dayjs | null;
    end: Dayjs | null;
}

export interface AmountRange {
    start: number;
    end: number;
}

export interface ExcelColumnFormatModel {
    key: string;
    numFmt?: string;
}

export interface TableHeaderModel {
    id: string;
    title: string;
}

export enum DashboardScreenType {
    Admin = 'admin',
    Statistics = 'statistics',
    Performance = 'performance',
    Underwriting = 'underwriting',
    Accounts = 'accounts',
    Operator = 'operator',
    Manager = 'manager',
    Origination = 'origination',
    Verification = 'verification',
    Accounting = 'accounting',
    Monitor = 'monitor',
    Collections = 'collections',
    Compliance = 'compliance',
    System = 'system',
    Configuration = 'configuration',
    Onboarding2 = 'onboarding2',
}

export enum PaymentFrequency {
    SINGLE = 'SINGLE',
    WEEKLY = 'WEEKLY',
    BIWEEKLY = 'BIWEEKLY',
    SEMIMONTHLY = 'SEMIMONTHLY',
    MONTHLY = 'MONTHLY',
}

export type ThemeMode = 'light' | 'dark';

export interface SelectableSearchValueModel {
    param: string;
    value: string;
    text: string;
}
