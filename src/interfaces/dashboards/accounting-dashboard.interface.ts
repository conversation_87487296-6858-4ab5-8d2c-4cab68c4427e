import {RecipientDTO} from '@/models';

export enum AccountingItemType {
    Revenue,
    Compensation,
    Balance_Sheets,
    Transactions_Reconciliations,
    External_System_Export,
}

export enum AccountingRevenueType {
    Revenue = 'revenue',
    Revenue_Bank = 'revenue-bank',
    Tables_Projected = 'tables-projected',
    Charge_Off = 'charge-off',
    Billing = 'billing',
}

export enum AccountingBalanceSheetsType {
    Employee = 'employee',
    Employer = 'employer',
    AR_Roll_Forward = 'AR-roll-forward',
}

export enum AccountingCompensationType {
    Broker_Commissions = 'broker-commissions',
    Employers = 'employers',
    Referrals = 'referrals',
    Employer_Participation = 'employer-participation',
}

export enum AccountingExternalSystemExportType {
    Xero = 'xero',
    Xpay = 'xpay',
    Sage = 'sage',
}

export enum AccountingTransactionsReconciliationsType {
    Employee = 'employee',
    Employer = 'employer',
    Employer_Transaction_Reconciliation = 'employer-transaction-reconciliation',
    Employee_Transaction_Reconciliation = 'employee-transaction-reconciliation',
    Employer_AR_Balances = 'employer-AR-balances',
    Disbursements = 'disbursements',
    Aging = 'aging',
    Origination_List = 'origination-list',
    Ownership_List = 'ownership-list',
    Origination_Costs = 'origination-costs',
    LOS_Fees = 'LOS-fees',
    LOS_Adjustments = 'LOS-adjustments',
    LOS_Collections = 'LOS-collections',
    LOS_Premiums = 'LOS-premiums',
    Total_Collections = 'Total-collections',
    Vendor_Costs = 'vendor-costs',
    Debit_Card = 'debit-card',
    Loan_Roll_Forward = 'loan-roll-forward',
}

export type AccountingFilterType =
    | AccountingRevenueType
    | AccountingExternalSystemExportType
    | AccountingBalanceSheetsType
    | AccountingCompensationType
    | AccountingTransactionsReconciliationsType;

export interface AccountingItemModel {
    id: string;
    content: string;
    expanded: boolean;
    loading: boolean;
    loadingError?: string;
    type: AccountingItemType;
    filterType: AccountingFilterType;
    isComparison?: boolean;
    data?: any;
}

export interface AccountingDashboardValuesModel {
    KAS: number;
    CRB: number;
    CRBA: number;
    CRBX: number;
    CRBY: number;
    BRB: number;
    BRBA: number;
    BRBX: number;
    BRBY: number;
    MDB: number;
    MDBA: number;
    MDBX: number;
}

export interface BaseAccountingTableModel {
    values: AccountingDashboardValuesModel;
}

export interface RollForwardTableModel extends BaseAccountingTableModel {
    category: string;
    description: string | null;
}

export interface BillingTableModel {
    employer_id: string;
    employer_name: string;
    amount: number;
    date: string;
}

export interface RevenueTableModel {
    entity_id: string | null;
    name: string;
    unrealized_interest: number;
    unrealized_origination_fee: number;
    unrealized_processing_fee: number;
    unrealized_other_fee: number;
    realized_interest: number;
    realized_origination_fee: number;
    realized_processing_fee: number;
    realized_other_fee: number;
    account_credit: number;
    other_income: number;
    pepm_billed: number;
    pepm_received: number;
    unrealized_pepm: number;
    reconciles: {
        EFFECTIVE: number;
    };
    group: string;
    date_from: number;
    date_to: number;
}
export interface RevenuePartnershipTableModel {
    entity_id: string | null;
    name: string;
    unrealized_interest: number;
    unrealized_origination_fee: number;
    unrealized_processing_fee: number;
    unrealized_other_fee: number;
    realized_interest: number;
    realized_origination_fee: number;
    realized_processing_fee: number;
    realized_other_fee: number;
    account_credit: number;
    other_income: number;
    pepm_billed: number;
    pepm_received: number;
    unrealized_chargeoff_adjustment: number;
    partnership_performance_fee: number;
    partnership_sponsor_fee: number;
    partnership_servicing_fee: number;
    partnership_adjustments_start: number;
    partnership_adjustments_end: number;
    partnership_purchase_premium: number;
    reconciles: {
        EFFECTIVE: number;
    };
}

export interface ComparisonRevenueTableModel extends RevenueTableModel {
    comparisonDate: string;
}

export interface RowsComparisonRevenueTableModel extends ComparisonRevenueTableModel {
    diff?: boolean;
    id?: string | null;
}

export interface ComparisonColumnsRevenueTableModel {
    entity_id: string | null;
    name: string;
    date_first: ComparisonRevenueTableModel;
    date_second: ComparisonRevenueTableModel;
}

export interface ProjectedTableModel {
    bucket: number;
    revenue_fee: number;
    revenue_interest: number;
    revenue_total: number;
}

export interface ComparisonProjectedTableModel extends ProjectedTableModel {
    comparisonDate: string;
}

export interface RowsComparisonProjectedTableModel extends ComparisonProjectedTableModel {
    diff?: boolean;
    id?: string | null;
}
export interface AmountByOwner {
    KS1: number;
    KAS: number;
}

export interface ChargeOffTableModel {
    step: number;
    date: string;
    description: string;
    amount: number;
    amount_by_owner: AmountByOwner;
    multiplier: number;
}

export interface BrokerCommissionSummaryTableModel {
    broker_id: number;
    broker_name: string;
    producer_name: string;
    amount: number;
    date: string;
    components: {
        employer_name: string;
        entity_id: number;
        entity_class: string;
        type: string;
        amount: number;
        base: number;
        bee: number | null;
        loan_count: number;
        loan_count_new: number;
        rate: number;
        brackets: {
            bracket: number;
            rate: number;
        }[];
    }[];
}

export interface BrokerCommissionDetailsTableModel {
    broker_name: string;
    producer_name: string;
    employer_name: string;
    type: string;
    amount: number;
    bee: number | null;
    rate: number;
    loan_count: number;
    loan_count_new: number;
}

export interface ReferralsTableModel {
    recipient: string;
    compensation_method: string;
    kas: string;
    crb: number;
    brb: number;
    mdb: number;
}

export interface EmployersTableModel {
    step: string | null;
    date: string | null;
    description: string | null;
    amount: number | null;
    multiplier: number;
    employer_id: number;
    employer_name: string;
    employer_mnemonic: string;
    root_id: number;
    root_name: string;
    broker_id: number | null;
    broker_company: string | null;
    broker_name: string | null;
    active: boolean;
    primary: string | null;
    primary_id: number;
    primary_name: string;
    start_date: string;
    first_date: string;
    bee_count: number;
    active_count: number;
}

export interface XeroTableModel {
    narration: string;
    date: string;
    description: string;
    account_code: string;
    tax_rate: string;
    amount: string;
}

export interface XpayTableModel {
    date: string;
    loan_id: number;
    prior_loan_id?: number;
    loan_payoff_owner?: string;
    transaction_type: string;
    los: string;
    transaction_amount: number;
    misc_description?: string;
    account: string;
    start_date: string;
    end_date: string;
}

export interface SageTableModel {
    donotimport: string;
    journal: string;
    date: Date;
    description: string;
    reference_no: string;
    line_no: number;
    acct_no: number;
    location_id: string;
    memo: string;
    debit: string;
    glentry_customerid: string;
    entry_date: Date;
}

export interface OriginalCostsTableModel {
    step: string;
    date: string;
    description: string;
    amount: number;
    multiplier: number;
}

export interface LOSCommonTableModel {
    title: string;
    type: string;
    origination_source: string;
    amount: number;
}

export interface LOSPurchasePartyTableModel {
    title: string;
    type: string;
    origination_source: string;
    purchase_party: string;
    amount: number;
}

export interface TransformedLOSPurchasePartyTableModel {
    title: string;
    type: string;
    values: {
        CRB_KAS: number;
        CRB_KS1: number;
        BRB_KAS: number;
        BRB_KS1: number;
        MDB_KAS: number;
        MDB_KS1: number;
    };
    dateFrom: number;
    dateTo: number;
}

export interface TransformedLOSCommonTableModel {
    title: string;
    type: string;
    values: {
        CRB: number;
        BRB: number;
        MDB: number;
    };
    dateFrom: number;
    dateTo: number;
}

export interface LOSCollectionsTableModel {
    owner: string;
    principal_collected: string;
    interest_collected: string;
    fee_collected: string;
    other_income_collected: string;
    account_credit: string;
    charged_off: string;
    total_collected: string;
    dateFrom: number;
    dateTo: number;
}

export interface LOSPremiumsTableModel {
    loan_id: number;
    source: string;
    components: {
        interest_rate: number;
        days_outstanding: number;
        accrued_interest: number;
        purchase_interest: number;
        purchase_premium: number;
        purchase_party: string | null;
    };
}

export interface EmployerARBalancesTableModel {
    employer_id: number;
    employer_name: string;
    primary_client_id: number;
    primary_client_name: string;
    billed: number;
    received: number;
    misc: number;
    balance: number;
    dateFrom: number;
    dateTo: number;
}

export interface EmployerTransactionReconciliationTableModel {
    vid: number;
    entity_class: string;
    entity_id: number;
    entity_name: string;
    payable_class: string;
    payable_id: number;
    payable_name: string;
    intended_date: string;
    effective_date: string;
    type: string;
    amount: number;
    balance: number;
}

export interface EmployeeTransactionReconciliationTableModel {
    step: number;
    date: string;
    description: string;
    amount: number;
    multiplier: number;
    employer_id: number;
    employer_name: string;
    employer_mnemonic: string | null;
    root_id: number;
    root_name: string;
    broker_id: number | null;
    broker_company: string | null;
    broker_name: string | null;
    active: string | null;
    primary: string | null;
}

export interface EmployeeTransactionReconciliationDetailsModel {
    step: number;
    date: string;
    description: string;
    amount: number;
    multiplier: number;
    gid: number;
    name: string;
}

export interface DisbursementsTableModel {
    step: number | null;
    date: string;
    description: string;
    amount: number;
    multiplier: number;
}

export interface AgingTableModel {
    bucket: number;
    count: number;
    balance: number;
    balance_nopay: number;
    vintage: string;
    date: string;
}

export interface AgingTableDetailsModel {
    bucket: number;
    loan_id: number;
    balance: number;
}

export interface OriginationListTableModel {
    step: number;
    date: string;
    description: string;
    amount: number;
    multiplier: number;
    employer_id: number;
    employer_name: string;
    employer_mnemonic: string | null;
    root_id: number;
    root_name: string;
    broker_id: number | null;
    broker_company: string | null;
    broker_name: string | null;
    active: string | null;
    primary: string | null;
}

export interface OwnershipListTableModel {
    date: string;
    root_id: number;
    root_name: string;
    amount: number;
}

export interface AccountingTableModel extends BaseAccountingTableModel {
    title: string;
    entity_id: string | null;
    date: string | null;
    owner: string | null;
    step: number;
    employerId: string;
    dateFrom?: string;
    dateTo?: string;
}

export interface ComparisonAccountingTableModel extends AccountingTableModel {
    comparisonDate: string;
}

export interface RowsComparisonAccountingTableModel extends ComparisonAccountingTableModel {
    diff?: boolean;
    id?: string | null;
}

export interface ComparisonColumnsTableModel<T> {
    step: number;
    title: string;
    date_first: T;
    date_second: T;
}

export interface BrokerCommissionEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    entity_class: string;
    entity_id: number;
}

export interface EmployerParticipationModel {
    all_employee_count: number;
    all_employee_current_loan_count: number;
    all_employee_current_loan_count_12: number;
    all_employee_cumulative_loans_count: number;
    all_employee_cumulative_loans_count_12: number;
    all_employee_cumulative_borrower_count: number;
    all_employee_cumulative_borrower_count_12: number;
    all_employee_registered_count: number;
    all_employee_registered_count_12: number;

    benefit_eligible_employee_count: number;
    benefit_eligible_employee_current_loan_count: number;
    benefit_eligible_employee_current_loan_count_12: number;
    benefit_eligible_employee_cumulative_loans_count: number;
    benefit_eligible_employee_cumulative_loans_count_12: number;
    benefit_eligible_employee_cumulative_borrower_count: number;
    benefit_eligible_employee_cumulative_borrower_count_12: number;
    benefit_eligible_employee_registered_count: number;
    benefit_eligible_employee_registered_count_12: number;

    active_employee_count: number;
    active_employee_current_loan_count: number;
    active_employee_current_loan_count_12: number;
    active_employee_cumulative_loans_count: number;
    active_employee_cumulative_loans_count_12: number;
    active_employee_cumulative_borrower_count: number;
    active_employee_cumulative_borrower_count_12: number;
    active_employee_registered_count: number;
    active_employee_registered_count_12: number;

    non_terminated_employee_count: number;
    non_terminated_employee_current_loan_count: number;
    non_terminated_employee_current_loan_count_12: number;
    non_terminated_employee_cumulative_loans_count: number;
    non_terminated_employee_cumulative_loans_count_12: number;
    non_terminated_employee_cumulative_borrower_count: number;
    non_terminated_employee_cumulative_borrower_count_12: number;
    non_terminated_employee_registered_count: number;
    non_terminated_employee_registered_count_12: number;
}

export interface EmployerParticipationTableModel {
    label: string;
    allEmployees: number;
    benefitEligible: number;
    activeEmployees: number;
    nonTerminatedEmployees: number;
}

export interface LoanRollForwardStatusModel {
    gid: number;
    job_status: string;
    job_detail: string;
    job_type: string;
    job_start_time: string;
    job_end_time: string;
    total: number;
    success: number;
    failure: number;
    skipped: number;
}
