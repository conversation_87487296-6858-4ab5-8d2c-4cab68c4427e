import { CommentDTO } from "@/models/restrictDTO";

export interface MonitorBackupACHModel extends MonitorBackupACHExtendedModel{
    application_id: number;
    context: string;
    session_id: string;
    application_date: string;
    employee_id: number;
    loan_id: number | null;
    bank_id: number | null;
    bank_verification_report_id: number | null;
    first_name: string;
    last_name: string;
    employer_name: string;
    backup_authorized: boolean;
    decision: boolean;
    declines: string | null;
    amount: number | null;
    state: string;
}

export interface MonitorDisabledBackupACHModel extends MonitorBackupACHExtendedModel{
    loan_id: number | null;
    first_name: string;
    last_name: string;
    employee_id: string;
    ach_type: string;
    comment: CommentDTO;
    commentText: string | null;
    disabled_date: string;
}

export interface MonitorBackupACHExtendedModel {
    employeeInfo: string;
}