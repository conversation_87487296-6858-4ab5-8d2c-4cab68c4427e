import {LoanStatusType, PayrollElectionReportDTO, RecipientDTO} from '@/models';
import {CommentDTO} from '@/models/restrictDTO';
import {
    VerificationQueueItemType,
    VerificationQueueLookingUserModel,
} from '@/screens/VerificationScreen/interfaces';

export interface VerificationQueueCountModel {
    total_count: number;
    available_count: number;
}

export interface VerificationQueueModel {
    gid: number;
    employee_id: number | null;
    employee_ssn: string;
    employee_name: string;
    application_id: number | null;
    loan_id: number | null;
    loan_amount: string | null;
    queue_time: string;
    verify_time: string | null;
    decline_time: string | null;
    verify_user: string | null;
    decline_user: string | null;
    archived: boolean;
    state: string;
    employer: string;
    create_time: string | null;
    last_updated_time: string;
    code: VerificationQueueItemType;
    text: string | null;
}

export interface VerificationQueueExpandedModel extends VerificationQueueModel {
    lookingUsers: VerificationQueueLookingUserModel[];
    employeeTableName: string;
}

export interface VerificationQueueSystemMessageModel {
    rule: string;
    message: string;
    critical: boolean;
}

export interface VerificationQueueReviewMatchesModel {
    ip_address: string;
    last_seen: string;
    link_comment: string;
    match_id: number;
    match_last_seen: string;
}

export interface VerificationQueueRiskMetricModel {
    name: string;
    level: string;
    description: string;
}

export interface VerificationQueuePriorLoanModel {
    amount: string;
    count: number;
    max_dlq: number;
}

export interface VerificationQueueDetailsModel {
    employee_id: number;
    employee_ssn: string | undefined;
    employee_name: string;
    application_id: number;
    verify_user: string;
    loan_guarantee: boolean;
    primary_employer_name: string;
    previous_queues: VerificationQueueModel[][];
    hired_date: string | null;
    employee_email_added: string;
    pre_qualify: boolean;
    challenge_issued: boolean;
    queue_id: number;
    employer_id: number;
    queue_type: string;
    workflow: string;
    review_matches_by_ip: VerificationQueueReviewMatchesModel[];
    employee_dob: string;
    decline_date: string | null;
    comments_all: CommentDTO[];
    bad_actor: boolean;
    employee_eid: string | null;
    review_date: string | null;
    employee_phone_cnam: string | null;
    messages: string[];
    primary_employer_mnemonic: string;
    address_zip: string;
    system_messages: VerificationQueueSystemMessageModel[];
    google_api_key: string;
    address_state: string;
    account_recent_change: boolean;
    employer_name: string;
    employee_phone: string;
    address_line_1: string;
    cycle_frequency: string;
    cycle_count?: number;
    risk_metrics: VerificationQueueRiskMetricModel[];
    address_line_2: string | null;
    challenge_passed: boolean;
    google_geocode?: {
        lat: number;
        lng: number;
    };
    employee_phone_added: string;
    payroll_election_report: PayrollElectionReportDTO;
    comments: CommentDTO[];
    income_self: number;
    income_evs?: number;
    previous_loan_id: number | null;
    ip_address: {[key: string]: string}[];
    verify_date: string | null;
    unable_to_pay: boolean;
    primary_employer_id: number;
    address_city: string;
    current_queues: VerificationQueueModel[];
    prior_loans: VerificationQueuePriorLoanModel;
    employee_email: string;
    address_zip_type: string;
    previous_loan_payoff_amount: string | null;
    evs_employer?: string;
    evs_name?: string;
    evs_status?: string;
    evs_hire_date?: string;
    evs_income?: number;
    loan_amount?: number;
    installment_date?: string;
    amount?: number;
    amount_rounded?: number;
    refund_amount?: number;
    bank_name?: string;
    aba_number?: number;
    acct_number?: number;
    bank_routing_number?: number;
    bank_account_number?: number;
    evs_name_match?: number;
    evs_employer_match?: number;
    last_originated_date: string | null;
    document_count: number;
    aba_number_documents?: string[];
    account_number_documents?: string[];
    amount_documents?: string[];
}

export interface VerificationQueueApplicationModel {
    gid: number | null;
    application_id: number;
    code: string;
    queued: boolean;
    verified: boolean;
    queue_time: string | null;
    verify_time: string | null;
}

export interface VerificationEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    employee_id: number;
    queue_id: number;
}

export interface VerificationQueueDuplicatesModel {
    employee_id: number;
    name: string;
    ip_address: string;
    employer_name: string;
    reason: string;
    reason_code: string;
    application_status: string;
    loan_id: number | null;
    loan_balance: number | null;
    loan_status: LoanStatusType | null;
    current_dlq: number | null;
    current_dlq_days: number | null;
    max_dlq: number | null;
    max_dlq_days: number | null;
    repayment_count: number | null;
    repayment_amount: number | null;
    expected_count: number | null;
    expected_amount: number | null;
}
