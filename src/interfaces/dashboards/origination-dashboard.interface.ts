export interface OriginationLogModel {
    gid: number;
    job_type: string;
    job_status: string;
    job_detail: string;
    job_context: string | null;
    job_start_time: string;
    job_end_time: string;
    job_document_id: number | null;
    job_user_id: number | null;
    job_user_name: string | null;
    employer_gid: number | null;
    employer_name: string | null;
    rollback: boolean;
    source: string | null;
}

export interface OriginationLogRecordModel {
    key: string;
    completed: boolean | null;
    message: string;
}

export interface OriginationLogSummaryModel {
    amount: string;
    status: string;
    count: number;
    batch_job_id: number;
}
