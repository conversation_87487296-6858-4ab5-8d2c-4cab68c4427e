import React, {useEffect, useState} from 'react';
import {KasDownloadIconButton, KasModal} from '@/components';
import {UnderwritingApplicationTapeModel} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {ApplicationTapeModalLoading, ApplicationTapeError, ApplicationTapeContent} from './components';

interface ApplicationTapeModalProps {
    gid: number;
    onClose: () => void;
}

export const ApplicationTapeModal = ({gid, onClose}: ApplicationTapeModalProps) => {
    const {showMessage} = useSnackbar();
    const [data, setData] = useState<UnderwritingApplicationTapeModel>();
    const params = JSON.stringify({path: `/secured/uw/profile/application/${gid}/pdf`});

    const loadData = () => {
        (async () => {
            const url = `/api/secured/underwriting/applications/application?id=${gid}`;
            const response = await apiRequest(url);

            setData(response.value || []);

            if (response.error) {
                showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
                onClose();
            }
        })();
    };

    useEffect(loadData, []);

    return (
        <KasModal
            title={`Application Tape: #${gid}`}
            open={true}
            onClose={onClose}
            titleAction={<KasDownloadIconButton params={params} />}>
            {data ? (
                <>
                    {data.error ? (
                        <ApplicationTapeError data={data} />
                    ) : (
                        <ApplicationTapeContent data={data} />
                    )}
                </>
            ) : (
                <ApplicationTapeModalLoading />
            )}
        </KasModal>
    );
};
