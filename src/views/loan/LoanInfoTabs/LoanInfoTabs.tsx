import React, {PropsWithChildren} from 'react';
import {LoanInfoTabType} from './interfaces';
import {LoanInfoTabsProvider} from './useLoanInfoTabs';
import {LoanInfoTabButtons} from './components';
import {LoanDetailsModel} from '@/interfaces';

const DEFAULT_ITEMS = Object.entries(LoanInfoTabType).map(([_, value]) => value);

interface LoanInfoTabsProps extends PropsWithChildren {
    loan: LoanDetailsModel;
    employeeId: number;
    items?: LoanInfoTabType[];
}

export const LoanInfoTabs = ({loan, employeeId, items = DEFAULT_ITEMS, children}: LoanInfoTabsProps) => {
    if (!items?.length) {
        return null;
    }

    return (
        <LoanInfoTabsProvider employeeId={employeeId} loan={loan} items={items}>
            <LoanInfoTabButtons items={items} />
            {children}
        </LoanInfoTabsProvider>
    );
};
