import React, {createContext, useContext, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    Completable,
    LoanAlternativePaymentModel,
    LoanAlternativeScheduleModel,
    LoanCreditReportingModel,
    LoanDelinquencyModel,
    LoanDetailsModel,
    LoanRepaymentScheduleModel,
    TransactionModel,
} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {LoanInfoTabType} from '@/views/loan/LoanInfoTabs/interfaces';

interface LoanInfoTabsContextModel {
    loan: LoanDetailsModel;
    activeTab: LoanInfoTabType;
    setActiveTab: (value: LoanInfoTabType) => void;
    repaymentScheduleState: DataStateInterface<LoanRepaymentScheduleModel[]>;
    loadRepaymentScheduleData: () => Promise<void>;
    alternativeScheduleState: DataStateInterface<LoanAlternativeScheduleModel[]>;
    loadAlternativeScheduleData: () => Promise<void>;
    alternativePaymentState: DataStateInterface<LoanAlternativePaymentModel[]>;
    loadAlternativePaymentData: () => Promise<void>;
    creditReportingState: DataStateInterface<LoanCreditReportingModel[]>;
    loadCreditReportingData: () => Promise<void>;
    delinquencyState: DataStateInterface<LoanDelinquencyModel[]>;
    loadDelinquencyData: () => Promise<void>;
    transactionsState: DataStateInterface<TransactionModel[]>;
    loadTransactionsData: () => Promise<void>;
}

const LoanInfoTabsContext = createContext<LoanInfoTabsContextModel | undefined>(undefined);

interface LoanInfoTabsProviderProps {
    children: React.ReactNode;
    loan: LoanDetailsModel;
    employeeId: number;
    items: LoanInfoTabType[];
}

export const LoanInfoTabsProvider = ({children, loan, employeeId, items}: LoanInfoTabsProviderProps) => {
    if (items.length === 0) {
        throw new Error('items array cannot be empty');
    }

    const [activeTab, setActiveTab] = useState<LoanInfoTabType>(items[0]);
    const [repaymentScheduleState, setRepaymentScheduleState] =
        useState(getDefaultState<LoanRepaymentScheduleModel[]>());
    const [alternativeScheduleState, setAlternativeScheduleState] =
        useState(getDefaultState<LoanAlternativeScheduleModel[]>());
    const [alternativePaymentState, setAlternativePaymentState] =
        useState(getDefaultState<LoanAlternativePaymentModel[]>());
    const [creditReportingState, setCreditReportingState] =
        useState(getDefaultState<LoanCreditReportingModel[]>());
    const [delinquencyState, setDelinquencyState] = useState(getDefaultState<LoanDelinquencyModel[]>());
    const [transactionsState, setTransactionsState] = useState(getDefaultState<TransactionModel[]>());

    const loadRepaymentScheduleData = async () => {
        const url = `/api/secured/underwriting/loans/${loan.gid}/repayment-schedule`;

        setRepaymentScheduleState(getLoadingState(repaymentScheduleState));

        const response: Completable<LoanRepaymentScheduleModel[]> = await apiRequest(url);

        setRepaymentScheduleState(getLoadedState(response));
    };

    const loadCreditReportingData = async () => {
        const url = `/api/secured/underwriting/loans/${loan.gid}/credit-reporting`;

        setCreditReportingState(getLoadingState(creditReportingState));

        const response: Completable<LoanCreditReportingModel[]> = await apiRequest(url);

        setCreditReportingState(getLoadedState(response));
    };

    const loadDelinquencyData = async () => {
        const url = `/api/secured/underwriting/loans/${loan.gid}/delinquency`;

        setDelinquencyState(getLoadingState(delinquencyState));

        const response: Completable<LoanDelinquencyModel[]> = await apiRequest(url);

        setDelinquencyState(getLoadedState(response));
    };

    const loadAlternativeScheduleData = async () => {
        const url = `/api/secured/underwriting/loans/${loan.gid}/alternative-schedule`;

        setAlternativeScheduleState(getLoadingState(alternativeScheduleState));

        const response: Completable<LoanAlternativeScheduleModel[]> = await apiRequest(url);

        setAlternativeScheduleState(getLoadedState(response));
    };

    const loadAlternativePaymentData = async () => {
        const url = `/api/secured/underwriting/loans/${loan.gid}/alternative-payment`;

        setAlternativePaymentState(getLoadingState(alternativePaymentState));

        const response: Completable<LoanAlternativePaymentModel[]> = await apiRequest(url);

        setAlternativePaymentState(getLoadedState(response));
    };

    const loadTransactionsData = async () => {
        const url = `/api/secured/underwriting/transactions/${employeeId}/transactions?showTransactions=true&loan=${loan.gid}`;

        setTransactionsState(getLoadingState(transactionsState));

        const response: Completable<TransactionModel[]> = await apiRequest(url);

        setTransactionsState(getLoadedState(response));
    };

    const value: LoanInfoTabsContextModel = {
        loan,
        activeTab,
        setActiveTab,
        repaymentScheduleState,
        loadRepaymentScheduleData,
        alternativeScheduleState,
        loadAlternativeScheduleData,
        alternativePaymentState,
        loadAlternativePaymentData,
        creditReportingState,
        loadCreditReportingData,
        delinquencyState,
        loadDelinquencyData,
        transactionsState,
        loadTransactionsData,
    };

    return <LoanInfoTabsContext.Provider value={value}>{children}</LoanInfoTabsContext.Provider>;
};

export function useLoanInfoTabs() {
    const context = useContext(LoanInfoTabsContext);
    if (!context) {
        throw new Error('useLoanInfoTabs must be used within LoanInfoTabsProvider');
    }
    return context;
}
