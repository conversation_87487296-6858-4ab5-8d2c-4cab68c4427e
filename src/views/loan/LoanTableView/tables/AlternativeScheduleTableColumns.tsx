import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {LoanAlternativeScheduleModel} from '@/interfaces';
import {KasStrike} from '@/components';
import React, {PropsWithChildren} from 'react';
import {AmountCell} from '@/components/table/cells';

const columnHelper = createColumnHelper<LoanAlternativeScheduleModel>();

const _defaultInfoColumn = defaultInfoColumn<LoanAlternativeScheduleModel>;

interface CellContentProps extends PropsWithChildren {
    props: CellContext<LoanAlternativeScheduleModel, string>;
}

const CellContent = ({props, children}: CellContentProps) => (
    <KasStrike
        key={`${props.column.id}-${props.row.original.gid}`}
        isStrike={props.row.original.active === false && !props.row.original.process_date}>
        {children}
    </KasStrike>
);

const _strikeContent = (props: CellContext<LoanAlternativeScheduleModel, string>) => (
    <CellContent props={props}>{props.getValue()}</CellContent>
);

const _strikeAmountContent = (props: CellContext<LoanAlternativeScheduleModel, string>) => (
    <CellContent props={props}>
        <AmountCell data={Number(props.getValue() || 0)} />
    </CellContent>
);

export const AlternativeScheduleTableColumns = [
    _defaultInfoColumn('amount', 'Amount', undefined, _strikeAmountContent),
    columnHelper.accessor('date', {
        id: 'date',
        header: 'Requested',
        cell: (props) => {
            const {date, create_time} = props.row.original;

            return <CellContent props={props}>{date ? date : create_time + '*'}</CellContent>;
        },
    }),
    _defaultInfoColumn('process_date', 'Processed', undefined, _strikeContent),
    _defaultInfoColumn('cleared_date', 'Cleared', undefined, _strikeContent),
] as ColumnDef<LoanAlternativeScheduleModel, unknown>[];
