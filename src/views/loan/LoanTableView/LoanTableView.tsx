import React, {useEffect, useMemo, useState} from 'react';
import {TableView} from '@/views';
import {ColumnDef, Row, SortingState} from '@tanstack/react-table';
import {DataStateInterface, NotificationModel} from '@/interfaces';
import {
    AlternativePaymentTableColumns,
    AlternativeScheduleTableColumns,
    CreditReportingTableColumns,
    DelinquencyTableColumns,
    NotificationsTableColumns,
    RepaymentScheduleTableColumns,
    TransactionsTableColumns,
} from './tables';
import {
    LoanAlternativePaymentModel,
    LoanAlternativeScheduleModel,
    LoanCreditReportingModel,
    LoanDelinquencyModel,
    LoanRepaymentScheduleModel,
    TransactionModel,
} from '@/interfaces';
import {LoanCreditReportingExpand, LoanDelinquencyChart} from './components';
import Box from '@mui/material/Box';
import {Checkbox, FormControlLabel} from '@mui/material';

interface LoanTableProps<T> {
    state: DataStateInterface<T[]>;
    loadData: () => Promise<void>;
    tableName?: string;
    hideBackDropLoading?: boolean;
    columns?: ColumnDef<T, string>[];
    sortingColumns?: SortingState;
    renderExpand?: (row: Row<T>) => React.ReactNode;
    tableActions?: React.ReactNode;
}

export const LoanTableView = <T,>({
    state,
    loadData,
    tableName,
    hideBackDropLoading = false,
    columns,
    sortingColumns,
    renderExpand,
    tableActions,
}: LoanTableProps<T>) => {
    useEffect(() => {
        if (!state.loading && !state.data) {
            loadData().then();
        }
    }, []);

    return (
        <TableView<T>
            withTableActions
            loading={state.loading}
            error={state.error}
            data={state.data}
            hideBackDropLoading={hideBackDropLoading}
            columns={columns as ColumnDef<T, unknown>[]}
            onRetry={loadData}
            sortingColumns={sortingColumns}
            renderExpand={renderExpand}
            tableName={tableName}
            tableActions={tableActions}
        />
    );
};

export const LoanRepaymentScheduleTable = (props: LoanTableProps<LoanRepaymentScheduleModel>) => (
    <LoanTableView<LoanRepaymentScheduleModel>
        {...props}
        tableName='Loan Repayment Schedule'
        columns={props.columns || RepaymentScheduleTableColumns}
    />
);

export const LoanAlternativeScheduleTable = (props: LoanTableProps<LoanAlternativeScheduleModel>) => {
    const [hideCancelled, setHideCancelled] = useState(false);

    const filteredData = useMemo(
        () =>
            props.state.data
                ? props.state.data.filter((item) => {
                      const isCancelled = item.active === false && !item.process_date;

                      return !(hideCancelled && isCancelled);
                  })
                : null,
        [props.state.data, hideCancelled],
    );

    return (
        <LoanTableView<LoanAlternativeScheduleModel>
            {...props}
            state={{...props.state, data: filteredData}}
            tableName='Loan Alternative Schedule'
            columns={props.columns || AlternativeScheduleTableColumns}
            sortingColumns={props.sortingColumns || [{id: 'date', desc: false}]}
            tableActions={
                <FormControlLabel
                    label='Hide cancelled payments'
                    control={
                        <Checkbox
                            size='small'
                            value={hideCancelled}
                            checked={hideCancelled}
                            onChange={() => setHideCancelled(!hideCancelled)}
                        />
                    }
                />
            }
        />
    );
};

export const LoanAlternativePaymentTable = (props: LoanTableProps<LoanAlternativePaymentModel>) => (
    <LoanTableView<LoanAlternativePaymentModel>
        {...props}
        tableName='Loan Alternative Payment'
        columns={props.columns || AlternativePaymentTableColumns}
        sortingColumns={props.sortingColumns}
    />
);

interface LoanCreditReportingTableProps extends LoanTableProps<LoanCreditReportingModel> {
    loanId: number;
}

export const LoanCreditReportingTable = (props: LoanCreditReportingTableProps) => (
    <LoanTableView<LoanCreditReportingModel>
        sortingColumns={[{id: 'payment_rating.month', desc: true}]}
        {...props}
        tableName='Loan Credit Reporting'
        columns={props.columns || CreditReportingTableColumns}
        renderExpand={(row: Row<LoanCreditReportingModel>) => (
            <LoanCreditReportingExpand data={row.original} loanId={props.loanId} />
        )}
    />
);

export const LoanDelinquencyTable = (props: LoanTableProps<LoanDelinquencyModel>) => (
    <>
        {!!props.state.data?.length && (
            <Box mb={2.5}>
                <LoanDelinquencyChart data={props.state.data} />
            </Box>
        )}
        <LoanTableView<LoanDelinquencyModel>
            {...props}
            tableName='Loan Delinquency'
            columns={props.columns || DelinquencyTableColumns}
        />
    </>
);

export const LoanTransactionsTable = (props: LoanTableProps<TransactionModel>) => (
    <LoanTableView<TransactionModel>
        {...props}
        tableName='Loan Transactions'
        columns={props.columns || TransactionsTableColumns}
    />
);

export const LoanNotificationsTable = (props: LoanTableProps<NotificationModel>) => (
    <LoanTableView<NotificationModel>
        {...props}
        tableName='Loan Notifications'
        columns={props.columns || NotificationsTableColumns}
    />
);
