{"printWidth": 110, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "all", "bracketSpacing": false, "bracketSameLine": true, "arrowParens": "always", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "endOfLine": "lf", "quoteProps": "as-needed", "htmlWhitespaceSensitivity": "strict", "importOrder": ["^react", "<THIRD_PARTY_MODULES>", "", "^@(.*)", "^[./]"], "importOrderBuiltinModulesToTop": true, "importOrderCaseInsensitive": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderMergeDuplicateImports": true, "importOrderCombineTypeAndValueImports": true, "importOrderSeparation": true, "importOrderSortSpecifiers": true}