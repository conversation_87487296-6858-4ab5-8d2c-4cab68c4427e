#!/bin/bash 
if [ "$#" -ne 1 ]; then
    echo "Usage ${0} <project>"
    exit 1
fi

export_dynamodb() {
    local tableName="$1"
    echo "Scanning table: $tableName env=${TARGET_ENV}"

    local rows=$(aws dynamodb scan \
        --table-name "$tableName" \
        --projection-expression "#key,${TARGET_ENV}" \
        --expression-attribute-names '{"#key":"key"}' \
        | jq -r ".Items[] | select(.${TARGET_ENV}) | [.key.S, .${TARGET_ENV}.S] | @tsv")

    while IFS=$'\t' read -r key value; do
        echo "Exporting Row: $key"
        export $key="$value"
    done <<< "$rows"
}

CONFIG_SOURCE="configs.${1}"
export_dynamodb "${CONFIG_SOURCE}"

export LANG=en_US.UTF-8 
export LC_ALL=en_US.UTF-8 
export LANGUAGE=en_US
