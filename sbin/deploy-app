#!/bin/bash
if [ "$#" -ne 1 ]; then
	echo "Usage ${0} <environment>"
	exit 1
fi

find_cluster() {
	local service_name=$1

	CLUSTERS=$(aws ecs list-clusters \
		--query "clusterArns[]" \
		--output text)
	for CLUSTER in $CLUSTERS; do
		SERVICE_DESC=$(aws ecs describe-services \
	   	--cluster "$CLUSTER" \
	   	--services "$service_name" \
	   	--query "services[?serviceName=='$service_name'].serviceName" \
	   	--output text)
		if [ ! -z "$SERVICE_DESC" ]; then
			echo $CLUSTER
			return
		fi
	done
}

get_current_task_definition() {
    local cluster_arn=$1
    local service_name=$2

    CURRENT_TASK_DEF=$(aws ecs describe-services \
        --cluster "${cluster_arn}" \
        --services "${service_name}" \
        --query "services[0].taskDefinition" \
        --output text)
    echo $CURRENT_TASK_DEF
}

get_latest_task_definition() {
    local task_family=$1

    LATEST_TASK_DEF=$(aws ecs describe-task-definition \
        --task-definition "${task_family}" \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text)
    echo $LATEST_TASK_DEF
}

SERVICE_NAME="kashable-portal-support2-${1}-svc"

echo "Searching for the Cluster for $SERVICE_NAME..."
CLUSTER_ARN="$(find_cluster $SERVICE_NAME)"
echo "Found Cluster: $CLUSTER_ARN"

echo "Getting current Task Definition for $SERVICE_NAME..."
CURRENT_TASK_DEF="$(get_current_task_definition $CLUSTER_ARN $SERVICE_NAME)"
echo "Current Task Definition: $CURRENT_TASK_DEF"

TASK_FAMILY=$(echo $CURRENT_TASK_DEF | cut -d'/' -f2 | cut -d':' -f1)
echo "Getting latest Task Definition for Task Family $TASK_FAMILY..."
LATEST_TASK_DEF="$(get_latest_task_definition $TASK_FAMILY)"
echo "Latest Task Definition: $LATEST_TASK_DEF"

aws ecs update-service \
    --no-cli-pager \
    --cluster "${CLUSTER_ARN}" \
    --service "${SERVICE_NAME}" \
    --task-definition "${LATEST_TASK_DEF}" \
    --force-new-deployment

exit 0